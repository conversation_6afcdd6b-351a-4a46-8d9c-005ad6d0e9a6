{"name": "calendar", "version": "0.1.0", "description": "View your notes in Calendar using any YAML key with date", "main": "main.js", "scripts": {"dev": "node esbuild.config.mjs", "build": "tsc -noEmit -skipLibCheck && node esbuild.config.mjs production", "version": "node version-bump.mjs && git add manifest.json versions.json"}, "keywords": [], "author": "", "license": "MIT", "devDependencies": {"@types/common-tags": "^1.8.1", "@types/node": "^16.11.6", "@types/react": "18.2.38", "@types/react-calendar": "^3.9.0", "@types/react-dom": "18.2.17", "@typescript-eslint/eslint-plugin": "5.29.0", "@typescript-eslint/parser": "5.29.0", "builtin-modules": "3.3.0", "esbuild": "0.17.3", "obsidian": "latest", "prettier": "^2.8.4", "tslib": "2.4.0", "typescript": "4.7.4"}, "dependencies": {"@popperjs/core": "^2.11.6", "common-tags": "^1.8.2", "dayjs": "1.11.7", "react": "18.2.0", "react-calendar": "4.0.0", "react-dom": "18.2.0", "react-icons": "4.8.0"}}