/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin
*/

var Ns=Object.create;var Fe=Object.defineProperty;var Ss=Object.getOwnPropertyDescriptor;var Is=Object.getOwnPropertyNames;var Cs=Object.getPrototypeOf,Ls=Object.prototype.hasOwnProperty;var Ds=(u,e)=>()=>(e||u((e={exports:{}}).exports,e),e.exports),$=(u,e)=>{for(var t in e)Fe(u,t,{get:e[t],enumerable:!0})},la=(u,e,t,a)=>{if(e&&typeof e=="object"||typeof e=="function")for(let s of Is(e))!Ls.call(u,s)&&s!==t&&Fe(u,s,{get:()=>e[s],enumerable:!(a=Ss(e,s))||a.enumerable});return u};var ku=(u,e,t)=>(t=u!=null?Ns(Cs(u)):{},la(e||!u||!u.__esModule?Fe(t,"default",{value:u,enumerable:!0}):t,u)),Os=u=>la(Fe({},"__esModule",{value:!0}),u);var Cu=Ds((W2,ur)=>{ur.exports={trueFunc:function(){return!0},falseFunc:function(){return!1}}});var Zo={};$(Zo,{default:()=>Ht});module.exports=Os(Zo);var v=require("obsidian");var o0={};$(o0,{contains:()=>et,extract:()=>Si,html:()=>xi,merge:()=>wa,parseHTML:()=>_i,root:()=>Ni,text:()=>Fu,xml:()=>Ai});var mu={};$(mu,{DocumentPosition:()=>eu,append:()=>ei,appendChild:()=>ui,compareDocumentPosition:()=>ya,existsOne:()=>Oa,filter:()=>ju,find:()=>$e,findAll:()=>si,findOne:()=>Ze,findOneChild:()=>ri,getAttributeValue:()=>zs,getChildren:()=>Ku,getElementById:()=>oi,getElements:()=>ci,getElementsByClassName:()=>di,getElementsByTagName:()=>Hu,getElementsByTagType:()=>fi,getFeed:()=>c0,getInnerHTML:()=>js,getName:()=>Zs,getOuterHTML:()=>La,getParent:()=>Da,getSiblings:()=>i0,getText:()=>Ke,hasAttrib:()=>$s,hasChildren:()=>R,innerText:()=>le,isCDATA:()=>Wu,isComment:()=>Uu,isDocument:()=>uu,isTag:()=>N,isText:()=>K,nextElementSibling:()=>je,prepend:()=>ai,prependChild:()=>ti,prevElementSibling:()=>ze,removeElement:()=>hu,removeSubsets:()=>li,replaceElement:()=>Js,testElement:()=>ni,textContent:()=>bu,uniqueSort:()=>Nu});var Z={};$(Z,{CDATA:()=>Kt,Comment:()=>Gt,Directive:()=>Vt,Doctype:()=>jt,ElementType:()=>O,Root:()=>qt,Script:()=>Wt,Style:()=>Qt,Tag:()=>Xt,Text:()=>Yt,isTag:()=>Ft});var O;(function(u){u.Root="root",u.Text="text",u.Directive="directive",u.Comment="comment",u.Script="script",u.Style="style",u.Tag="tag",u.CDATA="cdata",u.Doctype="doctype"})(O||(O={}));function Ft(u){return u.type===O.Tag||u.type===O.Script||u.type===O.Style}var qt=O.Root,Yt=O.Text,Vt=O.Directive,Gt=O.Comment,Wt=O.Script,Qt=O.Style,Xt=O.Tag,Kt=O.CDATA,jt=O.Doctype;var qe=class{constructor(){this.parent=null,this.prev=null,this.next=null,this.startIndex=null,this.endIndex=null}get parentNode(){return this.parent}set parentNode(e){this.parent=e}get previousSibling(){return this.prev}set previousSibling(e){this.prev=e}get nextSibling(){return this.next}set nextSibling(e){this.next=e}cloneNode(e=!1){return Qu(this,e)}},ce=class extends qe{constructor(e){super(),this.data=e}get nodeValue(){return this.data}set nodeValue(e){this.data=e}},ou=class extends ce{constructor(){super(...arguments),this.type=O.Text}get nodeType(){return 3}},wu=class extends ce{constructor(){super(...arguments),this.type=O.Comment}get nodeType(){return 8}},Bu=class extends ce{constructor(e,t){super(t),this.name=e,this.type=O.Directive}get nodeType(){return 1}},oe=class extends qe{constructor(e){super(),this.children=e}get firstChild(){var e;return(e=this.children[0])!==null&&e!==void 0?e:null}get lastChild(){return this.children.length>0?this.children[this.children.length-1]:null}get childNodes(){return this.children}set childNodes(e){this.children=e}},de=class extends oe{constructor(){super(...arguments),this.type=O.CDATA}get nodeType(){return 4}},J=class extends oe{constructor(){super(...arguments),this.type=O.Root}get nodeType(){return 9}},vu=class extends oe{constructor(e,t,a=[],s=e==="script"?O.Script:e==="style"?O.Style:O.Tag){super(a),this.name=e,this.attribs=t,this.type=s}get nodeType(){return 1}get tagName(){return this.name}set tagName(e){this.name=e}get attributes(){return Object.keys(this.attribs).map(e=>{var t,a;return{name:e,value:this.attribs[e],namespace:(t=this["x-attribsNamespace"])===null||t===void 0?void 0:t[e],prefix:(a=this["x-attribsPrefix"])===null||a===void 0?void 0:a[e]}})}};function N(u){return Ft(u)}function Wu(u){return u.type===O.CDATA}function K(u){return u.type===O.Text}function Uu(u){return u.type===O.Comment}function Ye(u){return u.type===O.Directive}function uu(u){return u.type===O.Root}function R(u){return Object.prototype.hasOwnProperty.call(u,"children")}function Qu(u,e=!1){let t;if(K(u))t=new ou(u.data);else if(Uu(u))t=new wu(u.data);else if(N(u)){let a=e?zt(u.children):[],s=new vu(u.name,{...u.attribs},a);a.forEach(i=>i.parent=s),u.namespace!=null&&(s.namespace=u.namespace),u["x-attribsNamespace"]&&(s["x-attribsNamespace"]={...u["x-attribsNamespace"]}),u["x-attribsPrefix"]&&(s["x-attribsPrefix"]={...u["x-attribsPrefix"]}),t=s}else if(Wu(u)){let a=e?zt(u.children):[],s=new de(a);a.forEach(i=>i.parent=s),t=s}else if(uu(u)){let a=e?zt(u.children):[],s=new J(a);a.forEach(i=>i.parent=s),u["x-mode"]&&(s["x-mode"]=u["x-mode"]),t=s}else if(Ye(u)){let a=new Bu(u.name,u.data);u["x-name"]!=null&&(a["x-name"]=u["x-name"],a["x-publicId"]=u["x-publicId"],a["x-systemId"]=u["x-systemId"]),t=a}else throw new Error(`Not implemented yet: ${u.type}`);return t.startIndex=u.startIndex,t.endIndex=u.endIndex,u.sourceCodeLocation!=null&&(t.sourceCodeLocation=u.sourceCodeLocation),t}function zt(u){let e=u.map(t=>Qu(t,!0));for(let t=1;t<e.length;t++)e[t].prev=e[t-1],e[t-1].next=e[t];return e}var ba={withStartIndices:!1,withEndIndices:!1,xmlMode:!1},Xu=class{constructor(e,t,a){this.dom=[],this.root=new J(this.dom),this.done=!1,this.tagStack=[this.root],this.lastNode=null,this.parser=null,typeof t=="function"&&(a=t,t=ba),typeof e=="object"&&(t=e,e=void 0),this.callback=e!=null?e:null,this.options=t!=null?t:ba,this.elementCB=a!=null?a:null}onparserinit(e){this.parser=e}onreset(){this.dom=[],this.root=new J(this.dom),this.done=!1,this.tagStack=[this.root],this.lastNode=null,this.parser=null}onend(){this.done||(this.done=!0,this.parser=null,this.handleCallback(null))}onerror(e){this.handleCallback(e)}onclosetag(){this.lastNode=null;let e=this.tagStack.pop();this.options.withEndIndices&&(e.endIndex=this.parser.endIndex),this.elementCB&&this.elementCB(e)}onopentag(e,t){let a=this.options.xmlMode?O.Tag:void 0,s=new vu(e,t,void 0,a);this.addNode(s),this.tagStack.push(s)}ontext(e){let{lastNode:t}=this;if(t&&t.type===O.Text)t.data+=e,this.options.withEndIndices&&(t.endIndex=this.parser.endIndex);else{let a=new ou(e);this.addNode(a),this.lastNode=a}}oncomment(e){if(this.lastNode&&this.lastNode.type===O.Comment){this.lastNode.data+=e;return}let t=new wu(e);this.addNode(t),this.lastNode=t}oncommentend(){this.lastNode=null}oncdatastart(){let e=new ou(""),t=new de([e]);this.addNode(t),e.parent=t,this.lastNode=e}oncdataend(){this.lastNode=null}onprocessinginstruction(e,t){let a=new Bu(e,t);this.addNode(a)}handleCallback(e){if(typeof this.callback=="function")this.callback(e,this.dom);else if(e)throw e}addNode(e){let t=this.tagStack[this.tagStack.length-1],a=t.children[t.children.length-1];this.options.withStartIndices&&(e.startIndex=this.parser.startIndex),this.options.withEndIndices&&(e.endIndex=this.parser.endIndex),t.children.push(e),a&&(e.prev=a,a.next=e),e.parent=t,this.lastNode=null}};var ha=new Uint16Array('\u1D41<\xD5\u0131\u028A\u049D\u057B\u05D0\u0675\u06DE\u07A2\u07D6\u080F\u0A4A\u0A91\u0DA1\u0E6D\u0F09\u0F26\u10CA\u1228\u12E1\u1415\u149D\u14C3\u14DF\u1525\0\0\0\0\0\0\u156B\u16CD\u198D\u1C12\u1DDD\u1F7E\u2060\u21B0\u228D\u23C0\u23FB\u2442\u2824\u2912\u2D08\u2E48\u2FCE\u3016\u32BA\u3639\u37AC\u38FE\u3A28\u3A71\u3AE0\u3B2E\u0800EMabcfglmnoprstu\\bfms\x7F\x84\x8B\x90\x95\x98\xA6\xB3\xB9\xC8\xCFlig\u803B\xC6\u40C6P\u803B&\u4026cute\u803B\xC1\u40C1reve;\u4102\u0100iyx}rc\u803B\xC2\u40C2;\u4410r;\uC000\u{1D504}rave\u803B\xC0\u40C0pha;\u4391acr;\u4100d;\u6A53\u0100gp\x9D\xA1on;\u4104f;\uC000\u{1D538}plyFunction;\u6061ing\u803B\xC5\u40C5\u0100cs\xBE\xC3r;\uC000\u{1D49C}ign;\u6254ilde\u803B\xC3\u40C3ml\u803B\xC4\u40C4\u0400aceforsu\xE5\xFB\xFE\u0117\u011C\u0122\u0127\u012A\u0100cr\xEA\xF2kslash;\u6216\u0176\xF6\xF8;\u6AE7ed;\u6306y;\u4411\u0180crt\u0105\u010B\u0114ause;\u6235noullis;\u612Ca;\u4392r;\uC000\u{1D505}pf;\uC000\u{1D539}eve;\u42D8c\xF2\u0113mpeq;\u624E\u0700HOacdefhilorsu\u014D\u0151\u0156\u0180\u019E\u01A2\u01B5\u01B7\u01BA\u01DC\u0215\u0273\u0278\u027Ecy;\u4427PY\u803B\xA9\u40A9\u0180cpy\u015D\u0162\u017Aute;\u4106\u0100;i\u0167\u0168\u62D2talDifferentialD;\u6145leys;\u612D\u0200aeio\u0189\u018E\u0194\u0198ron;\u410Cdil\u803B\xC7\u40C7rc;\u4108nint;\u6230ot;\u410A\u0100dn\u01A7\u01ADilla;\u40B8terDot;\u40B7\xF2\u017Fi;\u43A7rcle\u0200DMPT\u01C7\u01CB\u01D1\u01D6ot;\u6299inus;\u6296lus;\u6295imes;\u6297o\u0100cs\u01E2\u01F8kwiseContourIntegral;\u6232eCurly\u0100DQ\u0203\u020FoubleQuote;\u601Duote;\u6019\u0200lnpu\u021E\u0228\u0247\u0255on\u0100;e\u0225\u0226\u6237;\u6A74\u0180git\u022F\u0236\u023Aruent;\u6261nt;\u622FourIntegral;\u622E\u0100fr\u024C\u024E;\u6102oduct;\u6210nterClockwiseContourIntegral;\u6233oss;\u6A2Fcr;\uC000\u{1D49E}p\u0100;C\u0284\u0285\u62D3ap;\u624D\u0580DJSZacefios\u02A0\u02AC\u02B0\u02B4\u02B8\u02CB\u02D7\u02E1\u02E6\u0333\u048D\u0100;o\u0179\u02A5trahd;\u6911cy;\u4402cy;\u4405cy;\u440F\u0180grs\u02BF\u02C4\u02C7ger;\u6021r;\u61A1hv;\u6AE4\u0100ay\u02D0\u02D5ron;\u410E;\u4414l\u0100;t\u02DD\u02DE\u6207a;\u4394r;\uC000\u{1D507}\u0100af\u02EB\u0327\u0100cm\u02F0\u0322ritical\u0200ADGT\u0300\u0306\u0316\u031Ccute;\u40B4o\u0174\u030B\u030D;\u42D9bleAcute;\u42DDrave;\u4060ilde;\u42DCond;\u62C4ferentialD;\u6146\u0470\u033D\0\0\0\u0342\u0354\0\u0405f;\uC000\u{1D53B}\u0180;DE\u0348\u0349\u034D\u40A8ot;\u60DCqual;\u6250ble\u0300CDLRUV\u0363\u0372\u0382\u03CF\u03E2\u03F8ontourIntegra\xEC\u0239o\u0274\u0379\0\0\u037B\xBB\u0349nArrow;\u61D3\u0100eo\u0387\u03A4ft\u0180ART\u0390\u0396\u03A1rrow;\u61D0ightArrow;\u61D4e\xE5\u02CAng\u0100LR\u03AB\u03C4eft\u0100AR\u03B3\u03B9rrow;\u67F8ightArrow;\u67FAightArrow;\u67F9ight\u0100AT\u03D8\u03DErrow;\u61D2ee;\u62A8p\u0241\u03E9\0\0\u03EFrrow;\u61D1ownArrow;\u61D5erticalBar;\u6225n\u0300ABLRTa\u0412\u042A\u0430\u045E\u047F\u037Crrow\u0180;BU\u041D\u041E\u0422\u6193ar;\u6913pArrow;\u61F5reve;\u4311eft\u02D2\u043A\0\u0446\0\u0450ightVector;\u6950eeVector;\u695Eector\u0100;B\u0459\u045A\u61BDar;\u6956ight\u01D4\u0467\0\u0471eeVector;\u695Fector\u0100;B\u047A\u047B\u61C1ar;\u6957ee\u0100;A\u0486\u0487\u62A4rrow;\u61A7\u0100ct\u0492\u0497r;\uC000\u{1D49F}rok;\u4110\u0800NTacdfglmopqstux\u04BD\u04C0\u04C4\u04CB\u04DE\u04E2\u04E7\u04EE\u04F5\u0521\u052F\u0536\u0552\u055D\u0560\u0565G;\u414AH\u803B\xD0\u40D0cute\u803B\xC9\u40C9\u0180aiy\u04D2\u04D7\u04DCron;\u411Arc\u803B\xCA\u40CA;\u442Dot;\u4116r;\uC000\u{1D508}rave\u803B\xC8\u40C8ement;\u6208\u0100ap\u04FA\u04FEcr;\u4112ty\u0253\u0506\0\0\u0512mallSquare;\u65FBerySmallSquare;\u65AB\u0100gp\u0526\u052Aon;\u4118f;\uC000\u{1D53C}silon;\u4395u\u0100ai\u053C\u0549l\u0100;T\u0542\u0543\u6A75ilde;\u6242librium;\u61CC\u0100ci\u0557\u055Ar;\u6130m;\u6A73a;\u4397ml\u803B\xCB\u40CB\u0100ip\u056A\u056Fsts;\u6203onentialE;\u6147\u0280cfios\u0585\u0588\u058D\u05B2\u05CCy;\u4424r;\uC000\u{1D509}lled\u0253\u0597\0\0\u05A3mallSquare;\u65FCerySmallSquare;\u65AA\u0370\u05BA\0\u05BF\0\0\u05C4f;\uC000\u{1D53D}All;\u6200riertrf;\u6131c\xF2\u05CB\u0600JTabcdfgorst\u05E8\u05EC\u05EF\u05FA\u0600\u0612\u0616\u061B\u061D\u0623\u066C\u0672cy;\u4403\u803B>\u403Emma\u0100;d\u05F7\u05F8\u4393;\u43DCreve;\u411E\u0180eiy\u0607\u060C\u0610dil;\u4122rc;\u411C;\u4413ot;\u4120r;\uC000\u{1D50A};\u62D9pf;\uC000\u{1D53E}eater\u0300EFGLST\u0635\u0644\u064E\u0656\u065B\u0666qual\u0100;L\u063E\u063F\u6265ess;\u62DBullEqual;\u6267reater;\u6AA2ess;\u6277lantEqual;\u6A7Eilde;\u6273cr;\uC000\u{1D4A2};\u626B\u0400Aacfiosu\u0685\u068B\u0696\u069B\u069E\u06AA\u06BE\u06CARDcy;\u442A\u0100ct\u0690\u0694ek;\u42C7;\u405Eirc;\u4124r;\u610ClbertSpace;\u610B\u01F0\u06AF\0\u06B2f;\u610DizontalLine;\u6500\u0100ct\u06C3\u06C5\xF2\u06A9rok;\u4126mp\u0144\u06D0\u06D8ownHum\xF0\u012Fqual;\u624F\u0700EJOacdfgmnostu\u06FA\u06FE\u0703\u0707\u070E\u071A\u071E\u0721\u0728\u0744\u0778\u078B\u078F\u0795cy;\u4415lig;\u4132cy;\u4401cute\u803B\xCD\u40CD\u0100iy\u0713\u0718rc\u803B\xCE\u40CE;\u4418ot;\u4130r;\u6111rave\u803B\xCC\u40CC\u0180;ap\u0720\u072F\u073F\u0100cg\u0734\u0737r;\u412AinaryI;\u6148lie\xF3\u03DD\u01F4\u0749\0\u0762\u0100;e\u074D\u074E\u622C\u0100gr\u0753\u0758ral;\u622Bsection;\u62C2isible\u0100CT\u076C\u0772omma;\u6063imes;\u6062\u0180gpt\u077F\u0783\u0788on;\u412Ef;\uC000\u{1D540}a;\u4399cr;\u6110ilde;\u4128\u01EB\u079A\0\u079Ecy;\u4406l\u803B\xCF\u40CF\u0280cfosu\u07AC\u07B7\u07BC\u07C2\u07D0\u0100iy\u07B1\u07B5rc;\u4134;\u4419r;\uC000\u{1D50D}pf;\uC000\u{1D541}\u01E3\u07C7\0\u07CCr;\uC000\u{1D4A5}rcy;\u4408kcy;\u4404\u0380HJacfos\u07E4\u07E8\u07EC\u07F1\u07FD\u0802\u0808cy;\u4425cy;\u440Cppa;\u439A\u0100ey\u07F6\u07FBdil;\u4136;\u441Ar;\uC000\u{1D50E}pf;\uC000\u{1D542}cr;\uC000\u{1D4A6}\u0580JTaceflmost\u0825\u0829\u082C\u0850\u0863\u09B3\u09B8\u09C7\u09CD\u0A37\u0A47cy;\u4409\u803B<\u403C\u0280cmnpr\u0837\u083C\u0841\u0844\u084Dute;\u4139bda;\u439Bg;\u67EAlacetrf;\u6112r;\u619E\u0180aey\u0857\u085C\u0861ron;\u413Ddil;\u413B;\u441B\u0100fs\u0868\u0970t\u0500ACDFRTUVar\u087E\u08A9\u08B1\u08E0\u08E6\u08FC\u092F\u095B\u0390\u096A\u0100nr\u0883\u088FgleBracket;\u67E8row\u0180;BR\u0899\u089A\u089E\u6190ar;\u61E4ightArrow;\u61C6eiling;\u6308o\u01F5\u08B7\0\u08C3bleBracket;\u67E6n\u01D4\u08C8\0\u08D2eeVector;\u6961ector\u0100;B\u08DB\u08DC\u61C3ar;\u6959loor;\u630Aight\u0100AV\u08EF\u08F5rrow;\u6194ector;\u694E\u0100er\u0901\u0917e\u0180;AV\u0909\u090A\u0910\u62A3rrow;\u61A4ector;\u695Aiangle\u0180;BE\u0924\u0925\u0929\u62B2ar;\u69CFqual;\u62B4p\u0180DTV\u0937\u0942\u094CownVector;\u6951eeVector;\u6960ector\u0100;B\u0956\u0957\u61BFar;\u6958ector\u0100;B\u0965\u0966\u61BCar;\u6952ight\xE1\u039Cs\u0300EFGLST\u097E\u098B\u0995\u099D\u09A2\u09ADqualGreater;\u62DAullEqual;\u6266reater;\u6276ess;\u6AA1lantEqual;\u6A7Dilde;\u6272r;\uC000\u{1D50F}\u0100;e\u09BD\u09BE\u62D8ftarrow;\u61DAidot;\u413F\u0180npw\u09D4\u0A16\u0A1Bg\u0200LRlr\u09DE\u09F7\u0A02\u0A10eft\u0100AR\u09E6\u09ECrrow;\u67F5ightArrow;\u67F7ightArrow;\u67F6eft\u0100ar\u03B3\u0A0Aight\xE1\u03BFight\xE1\u03CAf;\uC000\u{1D543}er\u0100LR\u0A22\u0A2CeftArrow;\u6199ightArrow;\u6198\u0180cht\u0A3E\u0A40\u0A42\xF2\u084C;\u61B0rok;\u4141;\u626A\u0400acefiosu\u0A5A\u0A5D\u0A60\u0A77\u0A7C\u0A85\u0A8B\u0A8Ep;\u6905y;\u441C\u0100dl\u0A65\u0A6FiumSpace;\u605Flintrf;\u6133r;\uC000\u{1D510}nusPlus;\u6213pf;\uC000\u{1D544}c\xF2\u0A76;\u439C\u0480Jacefostu\u0AA3\u0AA7\u0AAD\u0AC0\u0B14\u0B19\u0D91\u0D97\u0D9Ecy;\u440Acute;\u4143\u0180aey\u0AB4\u0AB9\u0ABEron;\u4147dil;\u4145;\u441D\u0180gsw\u0AC7\u0AF0\u0B0Eative\u0180MTV\u0AD3\u0ADF\u0AE8ediumSpace;\u600Bhi\u0100cn\u0AE6\u0AD8\xEB\u0AD9eryThi\xEE\u0AD9ted\u0100GL\u0AF8\u0B06reaterGreate\xF2\u0673essLes\xF3\u0A48Line;\u400Ar;\uC000\u{1D511}\u0200Bnpt\u0B22\u0B28\u0B37\u0B3Areak;\u6060BreakingSpace;\u40A0f;\u6115\u0680;CDEGHLNPRSTV\u0B55\u0B56\u0B6A\u0B7C\u0BA1\u0BEB\u0C04\u0C5E\u0C84\u0CA6\u0CD8\u0D61\u0D85\u6AEC\u0100ou\u0B5B\u0B64ngruent;\u6262pCap;\u626DoubleVerticalBar;\u6226\u0180lqx\u0B83\u0B8A\u0B9Bement;\u6209ual\u0100;T\u0B92\u0B93\u6260ilde;\uC000\u2242\u0338ists;\u6204reater\u0380;EFGLST\u0BB6\u0BB7\u0BBD\u0BC9\u0BD3\u0BD8\u0BE5\u626Fqual;\u6271ullEqual;\uC000\u2267\u0338reater;\uC000\u226B\u0338ess;\u6279lantEqual;\uC000\u2A7E\u0338ilde;\u6275ump\u0144\u0BF2\u0BFDownHump;\uC000\u224E\u0338qual;\uC000\u224F\u0338e\u0100fs\u0C0A\u0C27tTriangle\u0180;BE\u0C1A\u0C1B\u0C21\u62EAar;\uC000\u29CF\u0338qual;\u62ECs\u0300;EGLST\u0C35\u0C36\u0C3C\u0C44\u0C4B\u0C58\u626Equal;\u6270reater;\u6278ess;\uC000\u226A\u0338lantEqual;\uC000\u2A7D\u0338ilde;\u6274ested\u0100GL\u0C68\u0C79reaterGreater;\uC000\u2AA2\u0338essLess;\uC000\u2AA1\u0338recedes\u0180;ES\u0C92\u0C93\u0C9B\u6280qual;\uC000\u2AAF\u0338lantEqual;\u62E0\u0100ei\u0CAB\u0CB9verseElement;\u620CghtTriangle\u0180;BE\u0CCB\u0CCC\u0CD2\u62EBar;\uC000\u29D0\u0338qual;\u62ED\u0100qu\u0CDD\u0D0CuareSu\u0100bp\u0CE8\u0CF9set\u0100;E\u0CF0\u0CF3\uC000\u228F\u0338qual;\u62E2erset\u0100;E\u0D03\u0D06\uC000\u2290\u0338qual;\u62E3\u0180bcp\u0D13\u0D24\u0D4Eset\u0100;E\u0D1B\u0D1E\uC000\u2282\u20D2qual;\u6288ceeds\u0200;EST\u0D32\u0D33\u0D3B\u0D46\u6281qual;\uC000\u2AB0\u0338lantEqual;\u62E1ilde;\uC000\u227F\u0338erset\u0100;E\u0D58\u0D5B\uC000\u2283\u20D2qual;\u6289ilde\u0200;EFT\u0D6E\u0D6F\u0D75\u0D7F\u6241qual;\u6244ullEqual;\u6247ilde;\u6249erticalBar;\u6224cr;\uC000\u{1D4A9}ilde\u803B\xD1\u40D1;\u439D\u0700Eacdfgmoprstuv\u0DBD\u0DC2\u0DC9\u0DD5\u0DDB\u0DE0\u0DE7\u0DFC\u0E02\u0E20\u0E22\u0E32\u0E3F\u0E44lig;\u4152cute\u803B\xD3\u40D3\u0100iy\u0DCE\u0DD3rc\u803B\xD4\u40D4;\u441Eblac;\u4150r;\uC000\u{1D512}rave\u803B\xD2\u40D2\u0180aei\u0DEE\u0DF2\u0DF6cr;\u414Cga;\u43A9cron;\u439Fpf;\uC000\u{1D546}enCurly\u0100DQ\u0E0E\u0E1AoubleQuote;\u601Cuote;\u6018;\u6A54\u0100cl\u0E27\u0E2Cr;\uC000\u{1D4AA}ash\u803B\xD8\u40D8i\u016C\u0E37\u0E3Cde\u803B\xD5\u40D5es;\u6A37ml\u803B\xD6\u40D6er\u0100BP\u0E4B\u0E60\u0100ar\u0E50\u0E53r;\u603Eac\u0100ek\u0E5A\u0E5C;\u63DEet;\u63B4arenthesis;\u63DC\u0480acfhilors\u0E7F\u0E87\u0E8A\u0E8F\u0E92\u0E94\u0E9D\u0EB0\u0EFCrtialD;\u6202y;\u441Fr;\uC000\u{1D513}i;\u43A6;\u43A0usMinus;\u40B1\u0100ip\u0EA2\u0EADncareplan\xE5\u069Df;\u6119\u0200;eio\u0EB9\u0EBA\u0EE0\u0EE4\u6ABBcedes\u0200;EST\u0EC8\u0EC9\u0ECF\u0EDA\u627Aqual;\u6AAFlantEqual;\u627Cilde;\u627Eme;\u6033\u0100dp\u0EE9\u0EEEuct;\u620Fortion\u0100;a\u0225\u0EF9l;\u621D\u0100ci\u0F01\u0F06r;\uC000\u{1D4AB};\u43A8\u0200Ufos\u0F11\u0F16\u0F1B\u0F1FOT\u803B"\u4022r;\uC000\u{1D514}pf;\u611Acr;\uC000\u{1D4AC}\u0600BEacefhiorsu\u0F3E\u0F43\u0F47\u0F60\u0F73\u0FA7\u0FAA\u0FAD\u1096\u10A9\u10B4\u10BEarr;\u6910G\u803B\xAE\u40AE\u0180cnr\u0F4E\u0F53\u0F56ute;\u4154g;\u67EBr\u0100;t\u0F5C\u0F5D\u61A0l;\u6916\u0180aey\u0F67\u0F6C\u0F71ron;\u4158dil;\u4156;\u4420\u0100;v\u0F78\u0F79\u611Cerse\u0100EU\u0F82\u0F99\u0100lq\u0F87\u0F8Eement;\u620Builibrium;\u61CBpEquilibrium;\u696Fr\xBB\u0F79o;\u43A1ght\u0400ACDFTUVa\u0FC1\u0FEB\u0FF3\u1022\u1028\u105B\u1087\u03D8\u0100nr\u0FC6\u0FD2gleBracket;\u67E9row\u0180;BL\u0FDC\u0FDD\u0FE1\u6192ar;\u61E5eftArrow;\u61C4eiling;\u6309o\u01F5\u0FF9\0\u1005bleBracket;\u67E7n\u01D4\u100A\0\u1014eeVector;\u695Dector\u0100;B\u101D\u101E\u61C2ar;\u6955loor;\u630B\u0100er\u102D\u1043e\u0180;AV\u1035\u1036\u103C\u62A2rrow;\u61A6ector;\u695Biangle\u0180;BE\u1050\u1051\u1055\u62B3ar;\u69D0qual;\u62B5p\u0180DTV\u1063\u106E\u1078ownVector;\u694FeeVector;\u695Cector\u0100;B\u1082\u1083\u61BEar;\u6954ector\u0100;B\u1091\u1092\u61C0ar;\u6953\u0100pu\u109B\u109Ef;\u611DndImplies;\u6970ightarrow;\u61DB\u0100ch\u10B9\u10BCr;\u611B;\u61B1leDelayed;\u69F4\u0680HOacfhimoqstu\u10E4\u10F1\u10F7\u10FD\u1119\u111E\u1151\u1156\u1161\u1167\u11B5\u11BB\u11BF\u0100Cc\u10E9\u10EEHcy;\u4429y;\u4428FTcy;\u442Ccute;\u415A\u0280;aeiy\u1108\u1109\u110E\u1113\u1117\u6ABCron;\u4160dil;\u415Erc;\u415C;\u4421r;\uC000\u{1D516}ort\u0200DLRU\u112A\u1134\u113E\u1149ownArrow\xBB\u041EeftArrow\xBB\u089AightArrow\xBB\u0FDDpArrow;\u6191gma;\u43A3allCircle;\u6218pf;\uC000\u{1D54A}\u0272\u116D\0\0\u1170t;\u621Aare\u0200;ISU\u117B\u117C\u1189\u11AF\u65A1ntersection;\u6293u\u0100bp\u118F\u119Eset\u0100;E\u1197\u1198\u628Fqual;\u6291erset\u0100;E\u11A8\u11A9\u6290qual;\u6292nion;\u6294cr;\uC000\u{1D4AE}ar;\u62C6\u0200bcmp\u11C8\u11DB\u1209\u120B\u0100;s\u11CD\u11CE\u62D0et\u0100;E\u11CD\u11D5qual;\u6286\u0100ch\u11E0\u1205eeds\u0200;EST\u11ED\u11EE\u11F4\u11FF\u627Bqual;\u6AB0lantEqual;\u627Dilde;\u627FTh\xE1\u0F8C;\u6211\u0180;es\u1212\u1213\u1223\u62D1rset\u0100;E\u121C\u121D\u6283qual;\u6287et\xBB\u1213\u0580HRSacfhiors\u123E\u1244\u1249\u1255\u125E\u1271\u1276\u129F\u12C2\u12C8\u12D1ORN\u803B\xDE\u40DEADE;\u6122\u0100Hc\u124E\u1252cy;\u440By;\u4426\u0100bu\u125A\u125C;\u4009;\u43A4\u0180aey\u1265\u126A\u126Fron;\u4164dil;\u4162;\u4422r;\uC000\u{1D517}\u0100ei\u127B\u1289\u01F2\u1280\0\u1287efore;\u6234a;\u4398\u0100cn\u128E\u1298kSpace;\uC000\u205F\u200ASpace;\u6009lde\u0200;EFT\u12AB\u12AC\u12B2\u12BC\u623Cqual;\u6243ullEqual;\u6245ilde;\u6248pf;\uC000\u{1D54B}ipleDot;\u60DB\u0100ct\u12D6\u12DBr;\uC000\u{1D4AF}rok;\u4166\u0AE1\u12F7\u130E\u131A\u1326\0\u132C\u1331\0\0\0\0\0\u1338\u133D\u1377\u1385\0\u13FF\u1404\u140A\u1410\u0100cr\u12FB\u1301ute\u803B\xDA\u40DAr\u0100;o\u1307\u1308\u619Fcir;\u6949r\u01E3\u1313\0\u1316y;\u440Eve;\u416C\u0100iy\u131E\u1323rc\u803B\xDB\u40DB;\u4423blac;\u4170r;\uC000\u{1D518}rave\u803B\xD9\u40D9acr;\u416A\u0100di\u1341\u1369er\u0100BP\u1348\u135D\u0100ar\u134D\u1350r;\u405Fac\u0100ek\u1357\u1359;\u63DFet;\u63B5arenthesis;\u63DDon\u0100;P\u1370\u1371\u62C3lus;\u628E\u0100gp\u137B\u137Fon;\u4172f;\uC000\u{1D54C}\u0400ADETadps\u1395\u13AE\u13B8\u13C4\u03E8\u13D2\u13D7\u13F3rrow\u0180;BD\u1150\u13A0\u13A4ar;\u6912ownArrow;\u61C5ownArrow;\u6195quilibrium;\u696Eee\u0100;A\u13CB\u13CC\u62A5rrow;\u61A5own\xE1\u03F3er\u0100LR\u13DE\u13E8eftArrow;\u6196ightArrow;\u6197i\u0100;l\u13F9\u13FA\u43D2on;\u43A5ing;\u416Ecr;\uC000\u{1D4B0}ilde;\u4168ml\u803B\xDC\u40DC\u0480Dbcdefosv\u1427\u142C\u1430\u1433\u143E\u1485\u148A\u1490\u1496ash;\u62ABar;\u6AEBy;\u4412ash\u0100;l\u143B\u143C\u62A9;\u6AE6\u0100er\u1443\u1445;\u62C1\u0180bty\u144C\u1450\u147Aar;\u6016\u0100;i\u144F\u1455cal\u0200BLST\u1461\u1465\u146A\u1474ar;\u6223ine;\u407Ceparator;\u6758ilde;\u6240ThinSpace;\u600Ar;\uC000\u{1D519}pf;\uC000\u{1D54D}cr;\uC000\u{1D4B1}dash;\u62AA\u0280cefos\u14A7\u14AC\u14B1\u14B6\u14BCirc;\u4174dge;\u62C0r;\uC000\u{1D51A}pf;\uC000\u{1D54E}cr;\uC000\u{1D4B2}\u0200fios\u14CB\u14D0\u14D2\u14D8r;\uC000\u{1D51B};\u439Epf;\uC000\u{1D54F}cr;\uC000\u{1D4B3}\u0480AIUacfosu\u14F1\u14F5\u14F9\u14FD\u1504\u150F\u1514\u151A\u1520cy;\u442Fcy;\u4407cy;\u442Ecute\u803B\xDD\u40DD\u0100iy\u1509\u150Drc;\u4176;\u442Br;\uC000\u{1D51C}pf;\uC000\u{1D550}cr;\uC000\u{1D4B4}ml;\u4178\u0400Hacdefos\u1535\u1539\u153F\u154B\u154F\u155D\u1560\u1564cy;\u4416cute;\u4179\u0100ay\u1544\u1549ron;\u417D;\u4417ot;\u417B\u01F2\u1554\0\u155BoWidt\xE8\u0AD9a;\u4396r;\u6128pf;\u6124cr;\uC000\u{1D4B5}\u0BE1\u1583\u158A\u1590\0\u15B0\u15B6\u15BF\0\0\0\0\u15C6\u15DB\u15EB\u165F\u166D\0\u1695\u169B\u16B2\u16B9\0\u16BEcute\u803B\xE1\u40E1reve;\u4103\u0300;Ediuy\u159C\u159D\u15A1\u15A3\u15A8\u15AD\u623E;\uC000\u223E\u0333;\u623Frc\u803B\xE2\u40E2te\u80BB\xB4\u0306;\u4430lig\u803B\xE6\u40E6\u0100;r\xB2\u15BA;\uC000\u{1D51E}rave\u803B\xE0\u40E0\u0100ep\u15CA\u15D6\u0100fp\u15CF\u15D4sym;\u6135\xE8\u15D3ha;\u43B1\u0100ap\u15DFc\u0100cl\u15E4\u15E7r;\u4101g;\u6A3F\u0264\u15F0\0\0\u160A\u0280;adsv\u15FA\u15FB\u15FF\u1601\u1607\u6227nd;\u6A55;\u6A5Clope;\u6A58;\u6A5A\u0380;elmrsz\u1618\u1619\u161B\u161E\u163F\u164F\u1659\u6220;\u69A4e\xBB\u1619sd\u0100;a\u1625\u1626\u6221\u0461\u1630\u1632\u1634\u1636\u1638\u163A\u163C\u163E;\u69A8;\u69A9;\u69AA;\u69AB;\u69AC;\u69AD;\u69AE;\u69AFt\u0100;v\u1645\u1646\u621Fb\u0100;d\u164C\u164D\u62BE;\u699D\u0100pt\u1654\u1657h;\u6222\xBB\xB9arr;\u637C\u0100gp\u1663\u1667on;\u4105f;\uC000\u{1D552}\u0380;Eaeiop\u12C1\u167B\u167D\u1682\u1684\u1687\u168A;\u6A70cir;\u6A6F;\u624Ad;\u624Bs;\u4027rox\u0100;e\u12C1\u1692\xF1\u1683ing\u803B\xE5\u40E5\u0180cty\u16A1\u16A6\u16A8r;\uC000\u{1D4B6};\u402Amp\u0100;e\u12C1\u16AF\xF1\u0288ilde\u803B\xE3\u40E3ml\u803B\xE4\u40E4\u0100ci\u16C2\u16C8onin\xF4\u0272nt;\u6A11\u0800Nabcdefiklnoprsu\u16ED\u16F1\u1730\u173C\u1743\u1748\u1778\u177D\u17E0\u17E6\u1839\u1850\u170D\u193D\u1948\u1970ot;\u6AED\u0100cr\u16F6\u171Ek\u0200ceps\u1700\u1705\u170D\u1713ong;\u624Cpsilon;\u43F6rime;\u6035im\u0100;e\u171A\u171B\u623Dq;\u62CD\u0176\u1722\u1726ee;\u62BDed\u0100;g\u172C\u172D\u6305e\xBB\u172Drk\u0100;t\u135C\u1737brk;\u63B6\u0100oy\u1701\u1741;\u4431quo;\u601E\u0280cmprt\u1753\u175B\u1761\u1764\u1768aus\u0100;e\u010A\u0109ptyv;\u69B0s\xE9\u170Cno\xF5\u0113\u0180ahw\u176F\u1771\u1773;\u43B2;\u6136een;\u626Cr;\uC000\u{1D51F}g\u0380costuvw\u178D\u179D\u17B3\u17C1\u17D5\u17DB\u17DE\u0180aiu\u1794\u1796\u179A\xF0\u0760rc;\u65EFp\xBB\u1371\u0180dpt\u17A4\u17A8\u17ADot;\u6A00lus;\u6A01imes;\u6A02\u0271\u17B9\0\0\u17BEcup;\u6A06ar;\u6605riangle\u0100du\u17CD\u17D2own;\u65BDp;\u65B3plus;\u6A04e\xE5\u1444\xE5\u14ADarow;\u690D\u0180ako\u17ED\u1826\u1835\u0100cn\u17F2\u1823k\u0180lst\u17FA\u05AB\u1802ozenge;\u69EBriangle\u0200;dlr\u1812\u1813\u1818\u181D\u65B4own;\u65BEeft;\u65C2ight;\u65B8k;\u6423\u01B1\u182B\0\u1833\u01B2\u182F\0\u1831;\u6592;\u65914;\u6593ck;\u6588\u0100eo\u183E\u184D\u0100;q\u1843\u1846\uC000=\u20E5uiv;\uC000\u2261\u20E5t;\u6310\u0200ptwx\u1859\u185E\u1867\u186Cf;\uC000\u{1D553}\u0100;t\u13CB\u1863om\xBB\u13CCtie;\u62C8\u0600DHUVbdhmptuv\u1885\u1896\u18AA\u18BB\u18D7\u18DB\u18EC\u18FF\u1905\u190A\u1910\u1921\u0200LRlr\u188E\u1890\u1892\u1894;\u6557;\u6554;\u6556;\u6553\u0280;DUdu\u18A1\u18A2\u18A4\u18A6\u18A8\u6550;\u6566;\u6569;\u6564;\u6567\u0200LRlr\u18B3\u18B5\u18B7\u18B9;\u655D;\u655A;\u655C;\u6559\u0380;HLRhlr\u18CA\u18CB\u18CD\u18CF\u18D1\u18D3\u18D5\u6551;\u656C;\u6563;\u6560;\u656B;\u6562;\u655Fox;\u69C9\u0200LRlr\u18E4\u18E6\u18E8\u18EA;\u6555;\u6552;\u6510;\u650C\u0280;DUdu\u06BD\u18F7\u18F9\u18FB\u18FD;\u6565;\u6568;\u652C;\u6534inus;\u629Flus;\u629Eimes;\u62A0\u0200LRlr\u1919\u191B\u191D\u191F;\u655B;\u6558;\u6518;\u6514\u0380;HLRhlr\u1930\u1931\u1933\u1935\u1937\u1939\u193B\u6502;\u656A;\u6561;\u655E;\u653C;\u6524;\u651C\u0100ev\u0123\u1942bar\u803B\xA6\u40A6\u0200ceio\u1951\u1956\u195A\u1960r;\uC000\u{1D4B7}mi;\u604Fm\u0100;e\u171A\u171Cl\u0180;bh\u1968\u1969\u196B\u405C;\u69C5sub;\u67C8\u016C\u1974\u197El\u0100;e\u1979\u197A\u6022t\xBB\u197Ap\u0180;Ee\u012F\u1985\u1987;\u6AAE\u0100;q\u06DC\u06DB\u0CE1\u19A7\0\u19E8\u1A11\u1A15\u1A32\0\u1A37\u1A50\0\0\u1AB4\0\0\u1AC1\0\0\u1B21\u1B2E\u1B4D\u1B52\0\u1BFD\0\u1C0C\u0180cpr\u19AD\u19B2\u19DDute;\u4107\u0300;abcds\u19BF\u19C0\u19C4\u19CA\u19D5\u19D9\u6229nd;\u6A44rcup;\u6A49\u0100au\u19CF\u19D2p;\u6A4Bp;\u6A47ot;\u6A40;\uC000\u2229\uFE00\u0100eo\u19E2\u19E5t;\u6041\xEE\u0693\u0200aeiu\u19F0\u19FB\u1A01\u1A05\u01F0\u19F5\0\u19F8s;\u6A4Don;\u410Ddil\u803B\xE7\u40E7rc;\u4109ps\u0100;s\u1A0C\u1A0D\u6A4Cm;\u6A50ot;\u410B\u0180dmn\u1A1B\u1A20\u1A26il\u80BB\xB8\u01ADptyv;\u69B2t\u8100\xA2;e\u1A2D\u1A2E\u40A2r\xE4\u01B2r;\uC000\u{1D520}\u0180cei\u1A3D\u1A40\u1A4Dy;\u4447ck\u0100;m\u1A47\u1A48\u6713ark\xBB\u1A48;\u43C7r\u0380;Ecefms\u1A5F\u1A60\u1A62\u1A6B\u1AA4\u1AAA\u1AAE\u65CB;\u69C3\u0180;el\u1A69\u1A6A\u1A6D\u42C6q;\u6257e\u0261\u1A74\0\0\u1A88rrow\u0100lr\u1A7C\u1A81eft;\u61BAight;\u61BB\u0280RSacd\u1A92\u1A94\u1A96\u1A9A\u1A9F\xBB\u0F47;\u64C8st;\u629Birc;\u629Aash;\u629Dnint;\u6A10id;\u6AEFcir;\u69C2ubs\u0100;u\u1ABB\u1ABC\u6663it\xBB\u1ABC\u02EC\u1AC7\u1AD4\u1AFA\0\u1B0Aon\u0100;e\u1ACD\u1ACE\u403A\u0100;q\xC7\xC6\u026D\u1AD9\0\0\u1AE2a\u0100;t\u1ADE\u1ADF\u402C;\u4040\u0180;fl\u1AE8\u1AE9\u1AEB\u6201\xEE\u1160e\u0100mx\u1AF1\u1AF6ent\xBB\u1AE9e\xF3\u024D\u01E7\u1AFE\0\u1B07\u0100;d\u12BB\u1B02ot;\u6A6Dn\xF4\u0246\u0180fry\u1B10\u1B14\u1B17;\uC000\u{1D554}o\xE4\u0254\u8100\xA9;s\u0155\u1B1Dr;\u6117\u0100ao\u1B25\u1B29rr;\u61B5ss;\u6717\u0100cu\u1B32\u1B37r;\uC000\u{1D4B8}\u0100bp\u1B3C\u1B44\u0100;e\u1B41\u1B42\u6ACF;\u6AD1\u0100;e\u1B49\u1B4A\u6AD0;\u6AD2dot;\u62EF\u0380delprvw\u1B60\u1B6C\u1B77\u1B82\u1BAC\u1BD4\u1BF9arr\u0100lr\u1B68\u1B6A;\u6938;\u6935\u0270\u1B72\0\0\u1B75r;\u62DEc;\u62DFarr\u0100;p\u1B7F\u1B80\u61B6;\u693D\u0300;bcdos\u1B8F\u1B90\u1B96\u1BA1\u1BA5\u1BA8\u622Arcap;\u6A48\u0100au\u1B9B\u1B9Ep;\u6A46p;\u6A4Aot;\u628Dr;\u6A45;\uC000\u222A\uFE00\u0200alrv\u1BB5\u1BBF\u1BDE\u1BE3rr\u0100;m\u1BBC\u1BBD\u61B7;\u693Cy\u0180evw\u1BC7\u1BD4\u1BD8q\u0270\u1BCE\0\0\u1BD2re\xE3\u1B73u\xE3\u1B75ee;\u62CEedge;\u62CFen\u803B\xA4\u40A4earrow\u0100lr\u1BEE\u1BF3eft\xBB\u1B80ight\xBB\u1BBDe\xE4\u1BDD\u0100ci\u1C01\u1C07onin\xF4\u01F7nt;\u6231lcty;\u632D\u0980AHabcdefhijlorstuwz\u1C38\u1C3B\u1C3F\u1C5D\u1C69\u1C75\u1C8A\u1C9E\u1CAC\u1CB7\u1CFB\u1CFF\u1D0D\u1D7B\u1D91\u1DAB\u1DBB\u1DC6\u1DCDr\xF2\u0381ar;\u6965\u0200glrs\u1C48\u1C4D\u1C52\u1C54ger;\u6020eth;\u6138\xF2\u1133h\u0100;v\u1C5A\u1C5B\u6010\xBB\u090A\u016B\u1C61\u1C67arow;\u690Fa\xE3\u0315\u0100ay\u1C6E\u1C73ron;\u410F;\u4434\u0180;ao\u0332\u1C7C\u1C84\u0100gr\u02BF\u1C81r;\u61CAtseq;\u6A77\u0180glm\u1C91\u1C94\u1C98\u803B\xB0\u40B0ta;\u43B4ptyv;\u69B1\u0100ir\u1CA3\u1CA8sht;\u697F;\uC000\u{1D521}ar\u0100lr\u1CB3\u1CB5\xBB\u08DC\xBB\u101E\u0280aegsv\u1CC2\u0378\u1CD6\u1CDC\u1CE0m\u0180;os\u0326\u1CCA\u1CD4nd\u0100;s\u0326\u1CD1uit;\u6666amma;\u43DDin;\u62F2\u0180;io\u1CE7\u1CE8\u1CF8\u40F7de\u8100\xF7;o\u1CE7\u1CF0ntimes;\u62C7n\xF8\u1CF7cy;\u4452c\u026F\u1D06\0\0\u1D0Arn;\u631Eop;\u630D\u0280lptuw\u1D18\u1D1D\u1D22\u1D49\u1D55lar;\u4024f;\uC000\u{1D555}\u0280;emps\u030B\u1D2D\u1D37\u1D3D\u1D42q\u0100;d\u0352\u1D33ot;\u6251inus;\u6238lus;\u6214quare;\u62A1blebarwedg\xE5\xFAn\u0180adh\u112E\u1D5D\u1D67ownarrow\xF3\u1C83arpoon\u0100lr\u1D72\u1D76ef\xF4\u1CB4igh\xF4\u1CB6\u0162\u1D7F\u1D85karo\xF7\u0F42\u026F\u1D8A\0\0\u1D8Ern;\u631Fop;\u630C\u0180cot\u1D98\u1DA3\u1DA6\u0100ry\u1D9D\u1DA1;\uC000\u{1D4B9};\u4455l;\u69F6rok;\u4111\u0100dr\u1DB0\u1DB4ot;\u62F1i\u0100;f\u1DBA\u1816\u65BF\u0100ah\u1DC0\u1DC3r\xF2\u0429a\xF2\u0FA6angle;\u69A6\u0100ci\u1DD2\u1DD5y;\u445Fgrarr;\u67FF\u0900Dacdefglmnopqrstux\u1E01\u1E09\u1E19\u1E38\u0578\u1E3C\u1E49\u1E61\u1E7E\u1EA5\u1EAF\u1EBD\u1EE1\u1F2A\u1F37\u1F44\u1F4E\u1F5A\u0100Do\u1E06\u1D34o\xF4\u1C89\u0100cs\u1E0E\u1E14ute\u803B\xE9\u40E9ter;\u6A6E\u0200aioy\u1E22\u1E27\u1E31\u1E36ron;\u411Br\u0100;c\u1E2D\u1E2E\u6256\u803B\xEA\u40EAlon;\u6255;\u444Dot;\u4117\u0100Dr\u1E41\u1E45ot;\u6252;\uC000\u{1D522}\u0180;rs\u1E50\u1E51\u1E57\u6A9Aave\u803B\xE8\u40E8\u0100;d\u1E5C\u1E5D\u6A96ot;\u6A98\u0200;ils\u1E6A\u1E6B\u1E72\u1E74\u6A99nters;\u63E7;\u6113\u0100;d\u1E79\u1E7A\u6A95ot;\u6A97\u0180aps\u1E85\u1E89\u1E97cr;\u4113ty\u0180;sv\u1E92\u1E93\u1E95\u6205et\xBB\u1E93p\u01001;\u1E9D\u1EA4\u0133\u1EA1\u1EA3;\u6004;\u6005\u6003\u0100gs\u1EAA\u1EAC;\u414Bp;\u6002\u0100gp\u1EB4\u1EB8on;\u4119f;\uC000\u{1D556}\u0180als\u1EC4\u1ECE\u1ED2r\u0100;s\u1ECA\u1ECB\u62D5l;\u69E3us;\u6A71i\u0180;lv\u1EDA\u1EDB\u1EDF\u43B5on\xBB\u1EDB;\u43F5\u0200csuv\u1EEA\u1EF3\u1F0B\u1F23\u0100io\u1EEF\u1E31rc\xBB\u1E2E\u0269\u1EF9\0\0\u1EFB\xED\u0548ant\u0100gl\u1F02\u1F06tr\xBB\u1E5Dess\xBB\u1E7A\u0180aei\u1F12\u1F16\u1F1Als;\u403Dst;\u625Fv\u0100;D\u0235\u1F20D;\u6A78parsl;\u69E5\u0100Da\u1F2F\u1F33ot;\u6253rr;\u6971\u0180cdi\u1F3E\u1F41\u1EF8r;\u612Fo\xF4\u0352\u0100ah\u1F49\u1F4B;\u43B7\u803B\xF0\u40F0\u0100mr\u1F53\u1F57l\u803B\xEB\u40EBo;\u60AC\u0180cip\u1F61\u1F64\u1F67l;\u4021s\xF4\u056E\u0100eo\u1F6C\u1F74ctatio\xEE\u0559nential\xE5\u0579\u09E1\u1F92\0\u1F9E\0\u1FA1\u1FA7\0\0\u1FC6\u1FCC\0\u1FD3\0\u1FE6\u1FEA\u2000\0\u2008\u205Allingdotse\xF1\u1E44y;\u4444male;\u6640\u0180ilr\u1FAD\u1FB3\u1FC1lig;\u8000\uFB03\u0269\u1FB9\0\0\u1FBDg;\u8000\uFB00ig;\u8000\uFB04;\uC000\u{1D523}lig;\u8000\uFB01lig;\uC000fj\u0180alt\u1FD9\u1FDC\u1FE1t;\u666Dig;\u8000\uFB02ns;\u65B1of;\u4192\u01F0\u1FEE\0\u1FF3f;\uC000\u{1D557}\u0100ak\u05BF\u1FF7\u0100;v\u1FFC\u1FFD\u62D4;\u6AD9artint;\u6A0D\u0100ao\u200C\u2055\u0100cs\u2011\u2052\u03B1\u201A\u2030\u2038\u2045\u2048\0\u2050\u03B2\u2022\u2025\u2027\u202A\u202C\0\u202E\u803B\xBD\u40BD;\u6153\u803B\xBC\u40BC;\u6155;\u6159;\u615B\u01B3\u2034\0\u2036;\u6154;\u6156\u02B4\u203E\u2041\0\0\u2043\u803B\xBE\u40BE;\u6157;\u615C5;\u6158\u01B6\u204C\0\u204E;\u615A;\u615D8;\u615El;\u6044wn;\u6322cr;\uC000\u{1D4BB}\u0880Eabcdefgijlnorstv\u2082\u2089\u209F\u20A5\u20B0\u20B4\u20F0\u20F5\u20FA\u20FF\u2103\u2112\u2138\u0317\u213E\u2152\u219E\u0100;l\u064D\u2087;\u6A8C\u0180cmp\u2090\u2095\u209Dute;\u41F5ma\u0100;d\u209C\u1CDA\u43B3;\u6A86reve;\u411F\u0100iy\u20AA\u20AErc;\u411D;\u4433ot;\u4121\u0200;lqs\u063E\u0642\u20BD\u20C9\u0180;qs\u063E\u064C\u20C4lan\xF4\u0665\u0200;cdl\u0665\u20D2\u20D5\u20E5c;\u6AA9ot\u0100;o\u20DC\u20DD\u6A80\u0100;l\u20E2\u20E3\u6A82;\u6A84\u0100;e\u20EA\u20ED\uC000\u22DB\uFE00s;\u6A94r;\uC000\u{1D524}\u0100;g\u0673\u061Bmel;\u6137cy;\u4453\u0200;Eaj\u065A\u210C\u210E\u2110;\u6A92;\u6AA5;\u6AA4\u0200Eaes\u211B\u211D\u2129\u2134;\u6269p\u0100;p\u2123\u2124\u6A8Arox\xBB\u2124\u0100;q\u212E\u212F\u6A88\u0100;q\u212E\u211Bim;\u62E7pf;\uC000\u{1D558}\u0100ci\u2143\u2146r;\u610Am\u0180;el\u066B\u214E\u2150;\u6A8E;\u6A90\u8300>;cdlqr\u05EE\u2160\u216A\u216E\u2173\u2179\u0100ci\u2165\u2167;\u6AA7r;\u6A7Aot;\u62D7Par;\u6995uest;\u6A7C\u0280adels\u2184\u216A\u2190\u0656\u219B\u01F0\u2189\0\u218Epro\xF8\u209Er;\u6978q\u0100lq\u063F\u2196les\xF3\u2088i\xED\u066B\u0100en\u21A3\u21ADrtneqq;\uC000\u2269\uFE00\xC5\u21AA\u0500Aabcefkosy\u21C4\u21C7\u21F1\u21F5\u21FA\u2218\u221D\u222F\u2268\u227Dr\xF2\u03A0\u0200ilmr\u21D0\u21D4\u21D7\u21DBrs\xF0\u1484f\xBB\u2024il\xF4\u06A9\u0100dr\u21E0\u21E4cy;\u444A\u0180;cw\u08F4\u21EB\u21EFir;\u6948;\u61ADar;\u610Firc;\u4125\u0180alr\u2201\u220E\u2213rts\u0100;u\u2209\u220A\u6665it\xBB\u220Alip;\u6026con;\u62B9r;\uC000\u{1D525}s\u0100ew\u2223\u2229arow;\u6925arow;\u6926\u0280amopr\u223A\u223E\u2243\u225E\u2263rr;\u61FFtht;\u623Bk\u0100lr\u2249\u2253eftarrow;\u61A9ightarrow;\u61AAf;\uC000\u{1D559}bar;\u6015\u0180clt\u226F\u2274\u2278r;\uC000\u{1D4BD}as\xE8\u21F4rok;\u4127\u0100bp\u2282\u2287ull;\u6043hen\xBB\u1C5B\u0AE1\u22A3\0\u22AA\0\u22B8\u22C5\u22CE\0\u22D5\u22F3\0\0\u22F8\u2322\u2367\u2362\u237F\0\u2386\u23AA\u23B4cute\u803B\xED\u40ED\u0180;iy\u0771\u22B0\u22B5rc\u803B\xEE\u40EE;\u4438\u0100cx\u22BC\u22BFy;\u4435cl\u803B\xA1\u40A1\u0100fr\u039F\u22C9;\uC000\u{1D526}rave\u803B\xEC\u40EC\u0200;ino\u073E\u22DD\u22E9\u22EE\u0100in\u22E2\u22E6nt;\u6A0Ct;\u622Dfin;\u69DCta;\u6129lig;\u4133\u0180aop\u22FE\u231A\u231D\u0180cgt\u2305\u2308\u2317r;\u412B\u0180elp\u071F\u230F\u2313in\xE5\u078Ear\xF4\u0720h;\u4131f;\u62B7ed;\u41B5\u0280;cfot\u04F4\u232C\u2331\u233D\u2341are;\u6105in\u0100;t\u2338\u2339\u621Eie;\u69DDdo\xF4\u2319\u0280;celp\u0757\u234C\u2350\u235B\u2361al;\u62BA\u0100gr\u2355\u2359er\xF3\u1563\xE3\u234Darhk;\u6A17rod;\u6A3C\u0200cgpt\u236F\u2372\u2376\u237By;\u4451on;\u412Ff;\uC000\u{1D55A}a;\u43B9uest\u803B\xBF\u40BF\u0100ci\u238A\u238Fr;\uC000\u{1D4BE}n\u0280;Edsv\u04F4\u239B\u239D\u23A1\u04F3;\u62F9ot;\u62F5\u0100;v\u23A6\u23A7\u62F4;\u62F3\u0100;i\u0777\u23AElde;\u4129\u01EB\u23B8\0\u23BCcy;\u4456l\u803B\xEF\u40EF\u0300cfmosu\u23CC\u23D7\u23DC\u23E1\u23E7\u23F5\u0100iy\u23D1\u23D5rc;\u4135;\u4439r;\uC000\u{1D527}ath;\u4237pf;\uC000\u{1D55B}\u01E3\u23EC\0\u23F1r;\uC000\u{1D4BF}rcy;\u4458kcy;\u4454\u0400acfghjos\u240B\u2416\u2422\u2427\u242D\u2431\u2435\u243Bppa\u0100;v\u2413\u2414\u43BA;\u43F0\u0100ey\u241B\u2420dil;\u4137;\u443Ar;\uC000\u{1D528}reen;\u4138cy;\u4445cy;\u445Cpf;\uC000\u{1D55C}cr;\uC000\u{1D4C0}\u0B80ABEHabcdefghjlmnoprstuv\u2470\u2481\u2486\u248D\u2491\u250E\u253D\u255A\u2580\u264E\u265E\u2665\u2679\u267D\u269A\u26B2\u26D8\u275D\u2768\u278B\u27C0\u2801\u2812\u0180art\u2477\u247A\u247Cr\xF2\u09C6\xF2\u0395ail;\u691Barr;\u690E\u0100;g\u0994\u248B;\u6A8Bar;\u6962\u0963\u24A5\0\u24AA\0\u24B1\0\0\0\0\0\u24B5\u24BA\0\u24C6\u24C8\u24CD\0\u24F9ute;\u413Amptyv;\u69B4ra\xEE\u084Cbda;\u43BBg\u0180;dl\u088E\u24C1\u24C3;\u6991\xE5\u088E;\u6A85uo\u803B\xAB\u40ABr\u0400;bfhlpst\u0899\u24DE\u24E6\u24E9\u24EB\u24EE\u24F1\u24F5\u0100;f\u089D\u24E3s;\u691Fs;\u691D\xEB\u2252p;\u61ABl;\u6939im;\u6973l;\u61A2\u0180;ae\u24FF\u2500\u2504\u6AABil;\u6919\u0100;s\u2509\u250A\u6AAD;\uC000\u2AAD\uFE00\u0180abr\u2515\u2519\u251Drr;\u690Crk;\u6772\u0100ak\u2522\u252Cc\u0100ek\u2528\u252A;\u407B;\u405B\u0100es\u2531\u2533;\u698Bl\u0100du\u2539\u253B;\u698F;\u698D\u0200aeuy\u2546\u254B\u2556\u2558ron;\u413E\u0100di\u2550\u2554il;\u413C\xEC\u08B0\xE2\u2529;\u443B\u0200cqrs\u2563\u2566\u256D\u257Da;\u6936uo\u0100;r\u0E19\u1746\u0100du\u2572\u2577har;\u6967shar;\u694Bh;\u61B2\u0280;fgqs\u258B\u258C\u0989\u25F3\u25FF\u6264t\u0280ahlrt\u2598\u25A4\u25B7\u25C2\u25E8rrow\u0100;t\u0899\u25A1a\xE9\u24F6arpoon\u0100du\u25AF\u25B4own\xBB\u045Ap\xBB\u0966eftarrows;\u61C7ight\u0180ahs\u25CD\u25D6\u25DErrow\u0100;s\u08F4\u08A7arpoon\xF3\u0F98quigarro\xF7\u21F0hreetimes;\u62CB\u0180;qs\u258B\u0993\u25FAlan\xF4\u09AC\u0280;cdgs\u09AC\u260A\u260D\u261D\u2628c;\u6AA8ot\u0100;o\u2614\u2615\u6A7F\u0100;r\u261A\u261B\u6A81;\u6A83\u0100;e\u2622\u2625\uC000\u22DA\uFE00s;\u6A93\u0280adegs\u2633\u2639\u263D\u2649\u264Bppro\xF8\u24C6ot;\u62D6q\u0100gq\u2643\u2645\xF4\u0989gt\xF2\u248C\xF4\u099Bi\xED\u09B2\u0180ilr\u2655\u08E1\u265Asht;\u697C;\uC000\u{1D529}\u0100;E\u099C\u2663;\u6A91\u0161\u2669\u2676r\u0100du\u25B2\u266E\u0100;l\u0965\u2673;\u696Alk;\u6584cy;\u4459\u0280;acht\u0A48\u2688\u268B\u2691\u2696r\xF2\u25C1orne\xF2\u1D08ard;\u696Bri;\u65FA\u0100io\u269F\u26A4dot;\u4140ust\u0100;a\u26AC\u26AD\u63B0che\xBB\u26AD\u0200Eaes\u26BB\u26BD\u26C9\u26D4;\u6268p\u0100;p\u26C3\u26C4\u6A89rox\xBB\u26C4\u0100;q\u26CE\u26CF\u6A87\u0100;q\u26CE\u26BBim;\u62E6\u0400abnoptwz\u26E9\u26F4\u26F7\u271A\u272F\u2741\u2747\u2750\u0100nr\u26EE\u26F1g;\u67ECr;\u61FDr\xEB\u08C1g\u0180lmr\u26FF\u270D\u2714eft\u0100ar\u09E6\u2707ight\xE1\u09F2apsto;\u67FCight\xE1\u09FDparrow\u0100lr\u2725\u2729ef\xF4\u24EDight;\u61AC\u0180afl\u2736\u2739\u273Dr;\u6985;\uC000\u{1D55D}us;\u6A2Dimes;\u6A34\u0161\u274B\u274Fst;\u6217\xE1\u134E\u0180;ef\u2757\u2758\u1800\u65CAnge\xBB\u2758ar\u0100;l\u2764\u2765\u4028t;\u6993\u0280achmt\u2773\u2776\u277C\u2785\u2787r\xF2\u08A8orne\xF2\u1D8Car\u0100;d\u0F98\u2783;\u696D;\u600Eri;\u62BF\u0300achiqt\u2798\u279D\u0A40\u27A2\u27AE\u27BBquo;\u6039r;\uC000\u{1D4C1}m\u0180;eg\u09B2\u27AA\u27AC;\u6A8D;\u6A8F\u0100bu\u252A\u27B3o\u0100;r\u0E1F\u27B9;\u601Arok;\u4142\u8400<;cdhilqr\u082B\u27D2\u2639\u27DC\u27E0\u27E5\u27EA\u27F0\u0100ci\u27D7\u27D9;\u6AA6r;\u6A79re\xE5\u25F2mes;\u62C9arr;\u6976uest;\u6A7B\u0100Pi\u27F5\u27F9ar;\u6996\u0180;ef\u2800\u092D\u181B\u65C3r\u0100du\u2807\u280Dshar;\u694Ahar;\u6966\u0100en\u2817\u2821rtneqq;\uC000\u2268\uFE00\xC5\u281E\u0700Dacdefhilnopsu\u2840\u2845\u2882\u288E\u2893\u28A0\u28A5\u28A8\u28DA\u28E2\u28E4\u0A83\u28F3\u2902Dot;\u623A\u0200clpr\u284E\u2852\u2863\u287Dr\u803B\xAF\u40AF\u0100et\u2857\u2859;\u6642\u0100;e\u285E\u285F\u6720se\xBB\u285F\u0100;s\u103B\u2868to\u0200;dlu\u103B\u2873\u2877\u287Bow\xEE\u048Cef\xF4\u090F\xF0\u13D1ker;\u65AE\u0100oy\u2887\u288Cmma;\u6A29;\u443Cash;\u6014asuredangle\xBB\u1626r;\uC000\u{1D52A}o;\u6127\u0180cdn\u28AF\u28B4\u28C9ro\u803B\xB5\u40B5\u0200;acd\u1464\u28BD\u28C0\u28C4s\xF4\u16A7ir;\u6AF0ot\u80BB\xB7\u01B5us\u0180;bd\u28D2\u1903\u28D3\u6212\u0100;u\u1D3C\u28D8;\u6A2A\u0163\u28DE\u28E1p;\u6ADB\xF2\u2212\xF0\u0A81\u0100dp\u28E9\u28EEels;\u62A7f;\uC000\u{1D55E}\u0100ct\u28F8\u28FDr;\uC000\u{1D4C2}pos\xBB\u159D\u0180;lm\u2909\u290A\u290D\u43BCtimap;\u62B8\u0C00GLRVabcdefghijlmoprstuvw\u2942\u2953\u297E\u2989\u2998\u29DA\u29E9\u2A15\u2A1A\u2A58\u2A5D\u2A83\u2A95\u2AA4\u2AA8\u2B04\u2B07\u2B44\u2B7F\u2BAE\u2C34\u2C67\u2C7C\u2CE9\u0100gt\u2947\u294B;\uC000\u22D9\u0338\u0100;v\u2950\u0BCF\uC000\u226B\u20D2\u0180elt\u295A\u2972\u2976ft\u0100ar\u2961\u2967rrow;\u61CDightarrow;\u61CE;\uC000\u22D8\u0338\u0100;v\u297B\u0C47\uC000\u226A\u20D2ightarrow;\u61CF\u0100Dd\u298E\u2993ash;\u62AFash;\u62AE\u0280bcnpt\u29A3\u29A7\u29AC\u29B1\u29CCla\xBB\u02DEute;\u4144g;\uC000\u2220\u20D2\u0280;Eiop\u0D84\u29BC\u29C0\u29C5\u29C8;\uC000\u2A70\u0338d;\uC000\u224B\u0338s;\u4149ro\xF8\u0D84ur\u0100;a\u29D3\u29D4\u666El\u0100;s\u29D3\u0B38\u01F3\u29DF\0\u29E3p\u80BB\xA0\u0B37mp\u0100;e\u0BF9\u0C00\u0280aeouy\u29F4\u29FE\u2A03\u2A10\u2A13\u01F0\u29F9\0\u29FB;\u6A43on;\u4148dil;\u4146ng\u0100;d\u0D7E\u2A0Aot;\uC000\u2A6D\u0338p;\u6A42;\u443Dash;\u6013\u0380;Aadqsx\u0B92\u2A29\u2A2D\u2A3B\u2A41\u2A45\u2A50rr;\u61D7r\u0100hr\u2A33\u2A36k;\u6924\u0100;o\u13F2\u13F0ot;\uC000\u2250\u0338ui\xF6\u0B63\u0100ei\u2A4A\u2A4Ear;\u6928\xED\u0B98ist\u0100;s\u0BA0\u0B9Fr;\uC000\u{1D52B}\u0200Eest\u0BC5\u2A66\u2A79\u2A7C\u0180;qs\u0BBC\u2A6D\u0BE1\u0180;qs\u0BBC\u0BC5\u2A74lan\xF4\u0BE2i\xED\u0BEA\u0100;r\u0BB6\u2A81\xBB\u0BB7\u0180Aap\u2A8A\u2A8D\u2A91r\xF2\u2971rr;\u61AEar;\u6AF2\u0180;sv\u0F8D\u2A9C\u0F8C\u0100;d\u2AA1\u2AA2\u62FC;\u62FAcy;\u445A\u0380AEadest\u2AB7\u2ABA\u2ABE\u2AC2\u2AC5\u2AF6\u2AF9r\xF2\u2966;\uC000\u2266\u0338rr;\u619Ar;\u6025\u0200;fqs\u0C3B\u2ACE\u2AE3\u2AEFt\u0100ar\u2AD4\u2AD9rro\xF7\u2AC1ightarro\xF7\u2A90\u0180;qs\u0C3B\u2ABA\u2AEAlan\xF4\u0C55\u0100;s\u0C55\u2AF4\xBB\u0C36i\xED\u0C5D\u0100;r\u0C35\u2AFEi\u0100;e\u0C1A\u0C25i\xE4\u0D90\u0100pt\u2B0C\u2B11f;\uC000\u{1D55F}\u8180\xAC;in\u2B19\u2B1A\u2B36\u40ACn\u0200;Edv\u0B89\u2B24\u2B28\u2B2E;\uC000\u22F9\u0338ot;\uC000\u22F5\u0338\u01E1\u0B89\u2B33\u2B35;\u62F7;\u62F6i\u0100;v\u0CB8\u2B3C\u01E1\u0CB8\u2B41\u2B43;\u62FE;\u62FD\u0180aor\u2B4B\u2B63\u2B69r\u0200;ast\u0B7B\u2B55\u2B5A\u2B5Flle\xEC\u0B7Bl;\uC000\u2AFD\u20E5;\uC000\u2202\u0338lint;\u6A14\u0180;ce\u0C92\u2B70\u2B73u\xE5\u0CA5\u0100;c\u0C98\u2B78\u0100;e\u0C92\u2B7D\xF1\u0C98\u0200Aait\u2B88\u2B8B\u2B9D\u2BA7r\xF2\u2988rr\u0180;cw\u2B94\u2B95\u2B99\u619B;\uC000\u2933\u0338;\uC000\u219D\u0338ghtarrow\xBB\u2B95ri\u0100;e\u0CCB\u0CD6\u0380chimpqu\u2BBD\u2BCD\u2BD9\u2B04\u0B78\u2BE4\u2BEF\u0200;cer\u0D32\u2BC6\u0D37\u2BC9u\xE5\u0D45;\uC000\u{1D4C3}ort\u026D\u2B05\0\0\u2BD6ar\xE1\u2B56m\u0100;e\u0D6E\u2BDF\u0100;q\u0D74\u0D73su\u0100bp\u2BEB\u2BED\xE5\u0CF8\xE5\u0D0B\u0180bcp\u2BF6\u2C11\u2C19\u0200;Ees\u2BFF\u2C00\u0D22\u2C04\u6284;\uC000\u2AC5\u0338et\u0100;e\u0D1B\u2C0Bq\u0100;q\u0D23\u2C00c\u0100;e\u0D32\u2C17\xF1\u0D38\u0200;Ees\u2C22\u2C23\u0D5F\u2C27\u6285;\uC000\u2AC6\u0338et\u0100;e\u0D58\u2C2Eq\u0100;q\u0D60\u2C23\u0200gilr\u2C3D\u2C3F\u2C45\u2C47\xEC\u0BD7lde\u803B\xF1\u40F1\xE7\u0C43iangle\u0100lr\u2C52\u2C5Ceft\u0100;e\u0C1A\u2C5A\xF1\u0C26ight\u0100;e\u0CCB\u2C65\xF1\u0CD7\u0100;m\u2C6C\u2C6D\u43BD\u0180;es\u2C74\u2C75\u2C79\u4023ro;\u6116p;\u6007\u0480DHadgilrs\u2C8F\u2C94\u2C99\u2C9E\u2CA3\u2CB0\u2CB6\u2CD3\u2CE3ash;\u62ADarr;\u6904p;\uC000\u224D\u20D2ash;\u62AC\u0100et\u2CA8\u2CAC;\uC000\u2265\u20D2;\uC000>\u20D2nfin;\u69DE\u0180Aet\u2CBD\u2CC1\u2CC5rr;\u6902;\uC000\u2264\u20D2\u0100;r\u2CCA\u2CCD\uC000<\u20D2ie;\uC000\u22B4\u20D2\u0100At\u2CD8\u2CDCrr;\u6903rie;\uC000\u22B5\u20D2im;\uC000\u223C\u20D2\u0180Aan\u2CF0\u2CF4\u2D02rr;\u61D6r\u0100hr\u2CFA\u2CFDk;\u6923\u0100;o\u13E7\u13E5ear;\u6927\u1253\u1A95\0\0\0\0\0\0\0\0\0\0\0\0\0\u2D2D\0\u2D38\u2D48\u2D60\u2D65\u2D72\u2D84\u1B07\0\0\u2D8D\u2DAB\0\u2DC8\u2DCE\0\u2DDC\u2E19\u2E2B\u2E3E\u2E43\u0100cs\u2D31\u1A97ute\u803B\xF3\u40F3\u0100iy\u2D3C\u2D45r\u0100;c\u1A9E\u2D42\u803B\xF4\u40F4;\u443E\u0280abios\u1AA0\u2D52\u2D57\u01C8\u2D5Alac;\u4151v;\u6A38old;\u69BClig;\u4153\u0100cr\u2D69\u2D6Dir;\u69BF;\uC000\u{1D52C}\u036F\u2D79\0\0\u2D7C\0\u2D82n;\u42DBave\u803B\xF2\u40F2;\u69C1\u0100bm\u2D88\u0DF4ar;\u69B5\u0200acit\u2D95\u2D98\u2DA5\u2DA8r\xF2\u1A80\u0100ir\u2D9D\u2DA0r;\u69BEoss;\u69BBn\xE5\u0E52;\u69C0\u0180aei\u2DB1\u2DB5\u2DB9cr;\u414Dga;\u43C9\u0180cdn\u2DC0\u2DC5\u01CDron;\u43BF;\u69B6pf;\uC000\u{1D560}\u0180ael\u2DD4\u2DD7\u01D2r;\u69B7rp;\u69B9\u0380;adiosv\u2DEA\u2DEB\u2DEE\u2E08\u2E0D\u2E10\u2E16\u6228r\xF2\u1A86\u0200;efm\u2DF7\u2DF8\u2E02\u2E05\u6A5Dr\u0100;o\u2DFE\u2DFF\u6134f\xBB\u2DFF\u803B\xAA\u40AA\u803B\xBA\u40BAgof;\u62B6r;\u6A56lope;\u6A57;\u6A5B\u0180clo\u2E1F\u2E21\u2E27\xF2\u2E01ash\u803B\xF8\u40F8l;\u6298i\u016C\u2E2F\u2E34de\u803B\xF5\u40F5es\u0100;a\u01DB\u2E3As;\u6A36ml\u803B\xF6\u40F6bar;\u633D\u0AE1\u2E5E\0\u2E7D\0\u2E80\u2E9D\0\u2EA2\u2EB9\0\0\u2ECB\u0E9C\0\u2F13\0\0\u2F2B\u2FBC\0\u2FC8r\u0200;ast\u0403\u2E67\u2E72\u0E85\u8100\xB6;l\u2E6D\u2E6E\u40B6le\xEC\u0403\u0269\u2E78\0\0\u2E7Bm;\u6AF3;\u6AFDy;\u443Fr\u0280cimpt\u2E8B\u2E8F\u2E93\u1865\u2E97nt;\u4025od;\u402Eil;\u6030enk;\u6031r;\uC000\u{1D52D}\u0180imo\u2EA8\u2EB0\u2EB4\u0100;v\u2EAD\u2EAE\u43C6;\u43D5ma\xF4\u0A76ne;\u660E\u0180;tv\u2EBF\u2EC0\u2EC8\u43C0chfork\xBB\u1FFD;\u43D6\u0100au\u2ECF\u2EDFn\u0100ck\u2ED5\u2EDDk\u0100;h\u21F4\u2EDB;\u610E\xF6\u21F4s\u0480;abcdemst\u2EF3\u2EF4\u1908\u2EF9\u2EFD\u2F04\u2F06\u2F0A\u2F0E\u402Bcir;\u6A23ir;\u6A22\u0100ou\u1D40\u2F02;\u6A25;\u6A72n\u80BB\xB1\u0E9Dim;\u6A26wo;\u6A27\u0180ipu\u2F19\u2F20\u2F25ntint;\u6A15f;\uC000\u{1D561}nd\u803B\xA3\u40A3\u0500;Eaceinosu\u0EC8\u2F3F\u2F41\u2F44\u2F47\u2F81\u2F89\u2F92\u2F7E\u2FB6;\u6AB3p;\u6AB7u\xE5\u0ED9\u0100;c\u0ECE\u2F4C\u0300;acens\u0EC8\u2F59\u2F5F\u2F66\u2F68\u2F7Eppro\xF8\u2F43urlye\xF1\u0ED9\xF1\u0ECE\u0180aes\u2F6F\u2F76\u2F7Approx;\u6AB9qq;\u6AB5im;\u62E8i\xED\u0EDFme\u0100;s\u2F88\u0EAE\u6032\u0180Eas\u2F78\u2F90\u2F7A\xF0\u2F75\u0180dfp\u0EEC\u2F99\u2FAF\u0180als\u2FA0\u2FA5\u2FAAlar;\u632Eine;\u6312urf;\u6313\u0100;t\u0EFB\u2FB4\xEF\u0EFBrel;\u62B0\u0100ci\u2FC0\u2FC5r;\uC000\u{1D4C5};\u43C8ncsp;\u6008\u0300fiopsu\u2FDA\u22E2\u2FDF\u2FE5\u2FEB\u2FF1r;\uC000\u{1D52E}pf;\uC000\u{1D562}rime;\u6057cr;\uC000\u{1D4C6}\u0180aeo\u2FF8\u3009\u3013t\u0100ei\u2FFE\u3005rnion\xF3\u06B0nt;\u6A16st\u0100;e\u3010\u3011\u403F\xF1\u1F19\xF4\u0F14\u0A80ABHabcdefhilmnoprstux\u3040\u3051\u3055\u3059\u30E0\u310E\u312B\u3147\u3162\u3172\u318E\u3206\u3215\u3224\u3229\u3258\u326E\u3272\u3290\u32B0\u32B7\u0180art\u3047\u304A\u304Cr\xF2\u10B3\xF2\u03DDail;\u691Car\xF2\u1C65ar;\u6964\u0380cdenqrt\u3068\u3075\u3078\u307F\u308F\u3094\u30CC\u0100eu\u306D\u3071;\uC000\u223D\u0331te;\u4155i\xE3\u116Emptyv;\u69B3g\u0200;del\u0FD1\u3089\u308B\u308D;\u6992;\u69A5\xE5\u0FD1uo\u803B\xBB\u40BBr\u0580;abcfhlpstw\u0FDC\u30AC\u30AF\u30B7\u30B9\u30BC\u30BE\u30C0\u30C3\u30C7\u30CAp;\u6975\u0100;f\u0FE0\u30B4s;\u6920;\u6933s;\u691E\xEB\u225D\xF0\u272El;\u6945im;\u6974l;\u61A3;\u619D\u0100ai\u30D1\u30D5il;\u691Ao\u0100;n\u30DB\u30DC\u6236al\xF3\u0F1E\u0180abr\u30E7\u30EA\u30EEr\xF2\u17E5rk;\u6773\u0100ak\u30F3\u30FDc\u0100ek\u30F9\u30FB;\u407D;\u405D\u0100es\u3102\u3104;\u698Cl\u0100du\u310A\u310C;\u698E;\u6990\u0200aeuy\u3117\u311C\u3127\u3129ron;\u4159\u0100di\u3121\u3125il;\u4157\xEC\u0FF2\xE2\u30FA;\u4440\u0200clqs\u3134\u3137\u313D\u3144a;\u6937dhar;\u6969uo\u0100;r\u020E\u020Dh;\u61B3\u0180acg\u314E\u315F\u0F44l\u0200;ips\u0F78\u3158\u315B\u109Cn\xE5\u10BBar\xF4\u0FA9t;\u65AD\u0180ilr\u3169\u1023\u316Esht;\u697D;\uC000\u{1D52F}\u0100ao\u3177\u3186r\u0100du\u317D\u317F\xBB\u047B\u0100;l\u1091\u3184;\u696C\u0100;v\u318B\u318C\u43C1;\u43F1\u0180gns\u3195\u31F9\u31FCht\u0300ahlrst\u31A4\u31B0\u31C2\u31D8\u31E4\u31EErrow\u0100;t\u0FDC\u31ADa\xE9\u30C8arpoon\u0100du\u31BB\u31BFow\xEE\u317Ep\xBB\u1092eft\u0100ah\u31CA\u31D0rrow\xF3\u0FEAarpoon\xF3\u0551ightarrows;\u61C9quigarro\xF7\u30CBhreetimes;\u62CCg;\u42DAingdotse\xF1\u1F32\u0180ahm\u320D\u3210\u3213r\xF2\u0FEAa\xF2\u0551;\u600Foust\u0100;a\u321E\u321F\u63B1che\xBB\u321Fmid;\u6AEE\u0200abpt\u3232\u323D\u3240\u3252\u0100nr\u3237\u323Ag;\u67EDr;\u61FEr\xEB\u1003\u0180afl\u3247\u324A\u324Er;\u6986;\uC000\u{1D563}us;\u6A2Eimes;\u6A35\u0100ap\u325D\u3267r\u0100;g\u3263\u3264\u4029t;\u6994olint;\u6A12ar\xF2\u31E3\u0200achq\u327B\u3280\u10BC\u3285quo;\u603Ar;\uC000\u{1D4C7}\u0100bu\u30FB\u328Ao\u0100;r\u0214\u0213\u0180hir\u3297\u329B\u32A0re\xE5\u31F8mes;\u62CAi\u0200;efl\u32AA\u1059\u1821\u32AB\u65B9tri;\u69CEluhar;\u6968;\u611E\u0D61\u32D5\u32DB\u32DF\u332C\u3338\u3371\0\u337A\u33A4\0\0\u33EC\u33F0\0\u3428\u3448\u345A\u34AD\u34B1\u34CA\u34F1\0\u3616\0\0\u3633cute;\u415Bqu\xEF\u27BA\u0500;Eaceinpsy\u11ED\u32F3\u32F5\u32FF\u3302\u330B\u330F\u331F\u3326\u3329;\u6AB4\u01F0\u32FA\0\u32FC;\u6AB8on;\u4161u\xE5\u11FE\u0100;d\u11F3\u3307il;\u415Frc;\u415D\u0180Eas\u3316\u3318\u331B;\u6AB6p;\u6ABAim;\u62E9olint;\u6A13i\xED\u1204;\u4441ot\u0180;be\u3334\u1D47\u3335\u62C5;\u6A66\u0380Aacmstx\u3346\u334A\u3357\u335B\u335E\u3363\u336Drr;\u61D8r\u0100hr\u3350\u3352\xEB\u2228\u0100;o\u0A36\u0A34t\u803B\xA7\u40A7i;\u403Bwar;\u6929m\u0100in\u3369\xF0nu\xF3\xF1t;\u6736r\u0100;o\u3376\u2055\uC000\u{1D530}\u0200acoy\u3382\u3386\u3391\u33A0rp;\u666F\u0100hy\u338B\u338Fcy;\u4449;\u4448rt\u026D\u3399\0\0\u339Ci\xE4\u1464ara\xEC\u2E6F\u803B\xAD\u40AD\u0100gm\u33A8\u33B4ma\u0180;fv\u33B1\u33B2\u33B2\u43C3;\u43C2\u0400;deglnpr\u12AB\u33C5\u33C9\u33CE\u33D6\u33DE\u33E1\u33E6ot;\u6A6A\u0100;q\u12B1\u12B0\u0100;E\u33D3\u33D4\u6A9E;\u6AA0\u0100;E\u33DB\u33DC\u6A9D;\u6A9Fe;\u6246lus;\u6A24arr;\u6972ar\xF2\u113D\u0200aeit\u33F8\u3408\u340F\u3417\u0100ls\u33FD\u3404lsetm\xE9\u336Ahp;\u6A33parsl;\u69E4\u0100dl\u1463\u3414e;\u6323\u0100;e\u341C\u341D\u6AAA\u0100;s\u3422\u3423\u6AAC;\uC000\u2AAC\uFE00\u0180flp\u342E\u3433\u3442tcy;\u444C\u0100;b\u3438\u3439\u402F\u0100;a\u343E\u343F\u69C4r;\u633Ff;\uC000\u{1D564}a\u0100dr\u344D\u0402es\u0100;u\u3454\u3455\u6660it\xBB\u3455\u0180csu\u3460\u3479\u349F\u0100au\u3465\u346Fp\u0100;s\u1188\u346B;\uC000\u2293\uFE00p\u0100;s\u11B4\u3475;\uC000\u2294\uFE00u\u0100bp\u347F\u348F\u0180;es\u1197\u119C\u3486et\u0100;e\u1197\u348D\xF1\u119D\u0180;es\u11A8\u11AD\u3496et\u0100;e\u11A8\u349D\xF1\u11AE\u0180;af\u117B\u34A6\u05B0r\u0165\u34AB\u05B1\xBB\u117Car\xF2\u1148\u0200cemt\u34B9\u34BE\u34C2\u34C5r;\uC000\u{1D4C8}tm\xEE\xF1i\xEC\u3415ar\xE6\u11BE\u0100ar\u34CE\u34D5r\u0100;f\u34D4\u17BF\u6606\u0100an\u34DA\u34EDight\u0100ep\u34E3\u34EApsilo\xEE\u1EE0h\xE9\u2EAFs\xBB\u2852\u0280bcmnp\u34FB\u355E\u1209\u358B\u358E\u0480;Edemnprs\u350E\u350F\u3511\u3515\u351E\u3523\u352C\u3531\u3536\u6282;\u6AC5ot;\u6ABD\u0100;d\u11DA\u351Aot;\u6AC3ult;\u6AC1\u0100Ee\u3528\u352A;\u6ACB;\u628Alus;\u6ABFarr;\u6979\u0180eiu\u353D\u3552\u3555t\u0180;en\u350E\u3545\u354Bq\u0100;q\u11DA\u350Feq\u0100;q\u352B\u3528m;\u6AC7\u0100bp\u355A\u355C;\u6AD5;\u6AD3c\u0300;acens\u11ED\u356C\u3572\u3579\u357B\u3326ppro\xF8\u32FAurlye\xF1\u11FE\xF1\u11F3\u0180aes\u3582\u3588\u331Bppro\xF8\u331Aq\xF1\u3317g;\u666A\u0680123;Edehlmnps\u35A9\u35AC\u35AF\u121C\u35B2\u35B4\u35C0\u35C9\u35D5\u35DA\u35DF\u35E8\u35ED\u803B\xB9\u40B9\u803B\xB2\u40B2\u803B\xB3\u40B3;\u6AC6\u0100os\u35B9\u35BCt;\u6ABEub;\u6AD8\u0100;d\u1222\u35C5ot;\u6AC4s\u0100ou\u35CF\u35D2l;\u67C9b;\u6AD7arr;\u697Bult;\u6AC2\u0100Ee\u35E4\u35E6;\u6ACC;\u628Blus;\u6AC0\u0180eiu\u35F4\u3609\u360Ct\u0180;en\u121C\u35FC\u3602q\u0100;q\u1222\u35B2eq\u0100;q\u35E7\u35E4m;\u6AC8\u0100bp\u3611\u3613;\u6AD4;\u6AD6\u0180Aan\u361C\u3620\u362Drr;\u61D9r\u0100hr\u3626\u3628\xEB\u222E\u0100;o\u0A2B\u0A29war;\u692Alig\u803B\xDF\u40DF\u0BE1\u3651\u365D\u3660\u12CE\u3673\u3679\0\u367E\u36C2\0\0\0\0\0\u36DB\u3703\0\u3709\u376C\0\0\0\u3787\u0272\u3656\0\0\u365Bget;\u6316;\u43C4r\xEB\u0E5F\u0180aey\u3666\u366B\u3670ron;\u4165dil;\u4163;\u4442lrec;\u6315r;\uC000\u{1D531}\u0200eiko\u3686\u369D\u36B5\u36BC\u01F2\u368B\0\u3691e\u01004f\u1284\u1281a\u0180;sv\u3698\u3699\u369B\u43B8ym;\u43D1\u0100cn\u36A2\u36B2k\u0100as\u36A8\u36AEppro\xF8\u12C1im\xBB\u12ACs\xF0\u129E\u0100as\u36BA\u36AE\xF0\u12C1rn\u803B\xFE\u40FE\u01EC\u031F\u36C6\u22E7es\u8180\xD7;bd\u36CF\u36D0\u36D8\u40D7\u0100;a\u190F\u36D5r;\u6A31;\u6A30\u0180eps\u36E1\u36E3\u3700\xE1\u2A4D\u0200;bcf\u0486\u36EC\u36F0\u36F4ot;\u6336ir;\u6AF1\u0100;o\u36F9\u36FC\uC000\u{1D565}rk;\u6ADA\xE1\u3362rime;\u6034\u0180aip\u370F\u3712\u3764d\xE5\u1248\u0380adempst\u3721\u374D\u3740\u3751\u3757\u375C\u375Fngle\u0280;dlqr\u3730\u3731\u3736\u3740\u3742\u65B5own\xBB\u1DBBeft\u0100;e\u2800\u373E\xF1\u092E;\u625Cight\u0100;e\u32AA\u374B\xF1\u105Aot;\u65ECinus;\u6A3Alus;\u6A39b;\u69CDime;\u6A3Bezium;\u63E2\u0180cht\u3772\u377D\u3781\u0100ry\u3777\u377B;\uC000\u{1D4C9};\u4446cy;\u445Brok;\u4167\u0100io\u378B\u378Ex\xF4\u1777head\u0100lr\u3797\u37A0eftarro\xF7\u084Fightarrow\xBB\u0F5D\u0900AHabcdfghlmoprstuw\u37D0\u37D3\u37D7\u37E4\u37F0\u37FC\u380E\u381C\u3823\u3834\u3851\u385D\u386B\u38A9\u38CC\u38D2\u38EA\u38F6r\xF2\u03EDar;\u6963\u0100cr\u37DC\u37E2ute\u803B\xFA\u40FA\xF2\u1150r\u01E3\u37EA\0\u37EDy;\u445Eve;\u416D\u0100iy\u37F5\u37FArc\u803B\xFB\u40FB;\u4443\u0180abh\u3803\u3806\u380Br\xF2\u13ADlac;\u4171a\xF2\u13C3\u0100ir\u3813\u3818sht;\u697E;\uC000\u{1D532}rave\u803B\xF9\u40F9\u0161\u3827\u3831r\u0100lr\u382C\u382E\xBB\u0957\xBB\u1083lk;\u6580\u0100ct\u3839\u384D\u026F\u383F\0\0\u384Arn\u0100;e\u3845\u3846\u631Cr\xBB\u3846op;\u630Fri;\u65F8\u0100al\u3856\u385Acr;\u416B\u80BB\xA8\u0349\u0100gp\u3862\u3866on;\u4173f;\uC000\u{1D566}\u0300adhlsu\u114B\u3878\u387D\u1372\u3891\u38A0own\xE1\u13B3arpoon\u0100lr\u3888\u388Cef\xF4\u382Digh\xF4\u382Fi\u0180;hl\u3899\u389A\u389C\u43C5\xBB\u13FAon\xBB\u389Aparrows;\u61C8\u0180cit\u38B0\u38C4\u38C8\u026F\u38B6\0\0\u38C1rn\u0100;e\u38BC\u38BD\u631Dr\xBB\u38BDop;\u630Eng;\u416Fri;\u65F9cr;\uC000\u{1D4CA}\u0180dir\u38D9\u38DD\u38E2ot;\u62F0lde;\u4169i\u0100;f\u3730\u38E8\xBB\u1813\u0100am\u38EF\u38F2r\xF2\u38A8l\u803B\xFC\u40FCangle;\u69A7\u0780ABDacdeflnoprsz\u391C\u391F\u3929\u392D\u39B5\u39B8\u39BD\u39DF\u39E4\u39E8\u39F3\u39F9\u39FD\u3A01\u3A20r\xF2\u03F7ar\u0100;v\u3926\u3927\u6AE8;\u6AE9as\xE8\u03E1\u0100nr\u3932\u3937grt;\u699C\u0380eknprst\u34E3\u3946\u394B\u3952\u395D\u3964\u3996app\xE1\u2415othin\xE7\u1E96\u0180hir\u34EB\u2EC8\u3959op\xF4\u2FB5\u0100;h\u13B7\u3962\xEF\u318D\u0100iu\u3969\u396Dgm\xE1\u33B3\u0100bp\u3972\u3984setneq\u0100;q\u397D\u3980\uC000\u228A\uFE00;\uC000\u2ACB\uFE00setneq\u0100;q\u398F\u3992\uC000\u228B\uFE00;\uC000\u2ACC\uFE00\u0100hr\u399B\u399Fet\xE1\u369Ciangle\u0100lr\u39AA\u39AFeft\xBB\u0925ight\xBB\u1051y;\u4432ash\xBB\u1036\u0180elr\u39C4\u39D2\u39D7\u0180;be\u2DEA\u39CB\u39CFar;\u62BBq;\u625Alip;\u62EE\u0100bt\u39DC\u1468a\xF2\u1469r;\uC000\u{1D533}tr\xE9\u39AEsu\u0100bp\u39EF\u39F1\xBB\u0D1C\xBB\u0D59pf;\uC000\u{1D567}ro\xF0\u0EFBtr\xE9\u39B4\u0100cu\u3A06\u3A0Br;\uC000\u{1D4CB}\u0100bp\u3A10\u3A18n\u0100Ee\u3980\u3A16\xBB\u397En\u0100Ee\u3992\u3A1E\xBB\u3990igzag;\u699A\u0380cefoprs\u3A36\u3A3B\u3A56\u3A5B\u3A54\u3A61\u3A6Airc;\u4175\u0100di\u3A40\u3A51\u0100bg\u3A45\u3A49ar;\u6A5Fe\u0100;q\u15FA\u3A4F;\u6259erp;\u6118r;\uC000\u{1D534}pf;\uC000\u{1D568}\u0100;e\u1479\u3A66at\xE8\u1479cr;\uC000\u{1D4CC}\u0AE3\u178E\u3A87\0\u3A8B\0\u3A90\u3A9B\0\0\u3A9D\u3AA8\u3AAB\u3AAF\0\0\u3AC3\u3ACE\0\u3AD8\u17DC\u17DFtr\xE9\u17D1r;\uC000\u{1D535}\u0100Aa\u3A94\u3A97r\xF2\u03C3r\xF2\u09F6;\u43BE\u0100Aa\u3AA1\u3AA4r\xF2\u03B8r\xF2\u09EBa\xF0\u2713is;\u62FB\u0180dpt\u17A4\u3AB5\u3ABE\u0100fl\u3ABA\u17A9;\uC000\u{1D569}im\xE5\u17B2\u0100Aa\u3AC7\u3ACAr\xF2\u03CEr\xF2\u0A01\u0100cq\u3AD2\u17B8r;\uC000\u{1D4CD}\u0100pt\u17D6\u3ADCr\xE9\u17D4\u0400acefiosu\u3AF0\u3AFD\u3B08\u3B0C\u3B11\u3B15\u3B1B\u3B21c\u0100uy\u3AF6\u3AFBte\u803B\xFD\u40FD;\u444F\u0100iy\u3B02\u3B06rc;\u4177;\u444Bn\u803B\xA5\u40A5r;\uC000\u{1D536}cy;\u4457pf;\uC000\u{1D56A}cr;\uC000\u{1D4CE}\u0100cm\u3B26\u3B29y;\u444El\u803B\xFF\u40FF\u0500acdefhiosw\u3B42\u3B48\u3B54\u3B58\u3B64\u3B69\u3B6D\u3B74\u3B7A\u3B80cute;\u417A\u0100ay\u3B4D\u3B52ron;\u417E;\u4437ot;\u417C\u0100et\u3B5D\u3B61tr\xE6\u155Fa;\u43B6r;\uC000\u{1D537}cy;\u4436grarr;\u61DDpf;\uC000\u{1D56B}cr;\uC000\u{1D4CF}\u0100jn\u3B85\u3B87;\u600Dj;\u600C'.split("").map(u=>u.charCodeAt(0)));var ma=new Uint16Array("\u0200aglq	\x1B\u026D\0\0p;\u4026os;\u4027t;\u403Et;\u403Cuot;\u4022".split("").map(u=>u.charCodeAt(0)));var $t,Rs=new Map([[0,65533],[128,8364],[130,8218],[131,402],[132,8222],[133,8230],[134,8224],[135,8225],[136,710],[137,8240],[138,352],[139,8249],[140,338],[142,381],[145,8216],[146,8217],[147,8220],[148,8221],[149,8226],[150,8211],[151,8212],[152,732],[153,8482],[154,353],[155,8250],[156,339],[158,382],[159,376]]),Zt=($t=String.fromCodePoint)!==null&&$t!==void 0?$t:function(u){let e="";return u>65535&&(u-=65536,e+=String.fromCharCode(u>>>10&1023|55296),u=56320|u&1023),e+=String.fromCharCode(u),e};function Jt(u){var e;return u>=55296&&u<=57343||u>1114111?65533:(e=Rs.get(u))!==null&&e!==void 0?e:u}var H;(function(u){u[u.NUM=35]="NUM",u[u.SEMI=59]="SEMI",u[u.EQUALS=61]="EQUALS",u[u.ZERO=48]="ZERO",u[u.NINE=57]="NINE",u[u.LOWER_A=97]="LOWER_A",u[u.LOWER_F=102]="LOWER_F",u[u.LOWER_X=120]="LOWER_X",u[u.LOWER_Z=122]="LOWER_Z",u[u.UPPER_A=65]="UPPER_A",u[u.UPPER_F=70]="UPPER_F",u[u.UPPER_Z=90]="UPPER_Z"})(H||(H={}));var ys=32,_u;(function(u){u[u.VALUE_LENGTH=49152]="VALUE_LENGTH",u[u.BRANCH_LENGTH=16256]="BRANCH_LENGTH",u[u.JUMP_TABLE=127]="JUMP_TABLE"})(_u||(_u={}));function u0(u){return u>=H.ZERO&&u<=H.NINE}function Ps(u){return u>=H.UPPER_A&&u<=H.UPPER_F||u>=H.LOWER_A&&u<=H.LOWER_F}function Ms(u){return u>=H.UPPER_A&&u<=H.UPPER_Z||u>=H.LOWER_A&&u<=H.LOWER_Z||u0(u)}function ks(u){return u===H.EQUALS||Ms(u)}var U;(function(u){u[u.EntityStart=0]="EntityStart",u[u.NumericStart=1]="NumericStart",u[u.NumericDecimal=2]="NumericDecimal",u[u.NumericHex=3]="NumericHex",u[u.NamedEntity=4]="NamedEntity"})(U||(U={}));var lu;(function(u){u[u.Legacy=0]="Legacy",u[u.Strict=1]="Strict",u[u.Attribute=2]="Attribute"})(lu||(lu={}));var Ve=class{constructor(e,t,a){this.decodeTree=e,this.emitCodePoint=t,this.errors=a,this.state=U.EntityStart,this.consumed=1,this.result=0,this.treeIndex=0,this.excess=1,this.decodeMode=lu.Strict}startEntity(e){this.decodeMode=e,this.state=U.EntityStart,this.result=0,this.treeIndex=0,this.excess=1,this.consumed=1}write(e,t){switch(this.state){case U.EntityStart:return e.charCodeAt(t)===H.NUM?(this.state=U.NumericStart,this.consumed+=1,this.stateNumericStart(e,t+1)):(this.state=U.NamedEntity,this.stateNamedEntity(e,t));case U.NumericStart:return this.stateNumericStart(e,t);case U.NumericDecimal:return this.stateNumericDecimal(e,t);case U.NumericHex:return this.stateNumericHex(e,t);case U.NamedEntity:return this.stateNamedEntity(e,t)}}stateNumericStart(e,t){return t>=e.length?-1:(e.charCodeAt(t)|ys)===H.LOWER_X?(this.state=U.NumericHex,this.consumed+=1,this.stateNumericHex(e,t+1)):(this.state=U.NumericDecimal,this.stateNumericDecimal(e,t))}addToNumericResult(e,t,a,s){if(t!==a){let i=a-t;this.result=this.result*Math.pow(s,i)+parseInt(e.substr(t,i),s),this.consumed+=i}}stateNumericHex(e,t){let a=t;for(;t<e.length;){let s=e.charCodeAt(t);if(u0(s)||Ps(s))t+=1;else return this.addToNumericResult(e,a,t,16),this.emitNumericEntity(s,3)}return this.addToNumericResult(e,a,t,16),-1}stateNumericDecimal(e,t){let a=t;for(;t<e.length;){let s=e.charCodeAt(t);if(u0(s))t+=1;else return this.addToNumericResult(e,a,t,10),this.emitNumericEntity(s,2)}return this.addToNumericResult(e,a,t,10),-1}emitNumericEntity(e,t){var a;if(this.consumed<=t)return(a=this.errors)===null||a===void 0||a.absenceOfDigitsInNumericCharacterReference(this.consumed),0;if(e===H.SEMI)this.consumed+=1;else if(this.decodeMode===lu.Strict)return 0;return this.emitCodePoint(Jt(this.result),this.consumed),this.errors&&(e!==H.SEMI&&this.errors.missingSemicolonAfterCharacterReference(),this.errors.validateNumericCharacterReference(this.result)),this.consumed}stateNamedEntity(e,t){let{decodeTree:a}=this,s=a[this.treeIndex],i=(s&_u.VALUE_LENGTH)>>14;for(;t<e.length;t++,this.excess++){let n=e.charCodeAt(t);if(this.treeIndex=ws(a,s,this.treeIndex+Math.max(1,i),n),this.treeIndex<0)return this.result===0||this.decodeMode===lu.Attribute&&(i===0||ks(n))?0:this.emitNotTerminatedNamedEntity();if(s=a[this.treeIndex],i=(s&_u.VALUE_LENGTH)>>14,i!==0){if(n===H.SEMI)return this.emitNamedEntityData(this.treeIndex,i,this.consumed+this.excess);this.decodeMode!==lu.Strict&&(this.result=this.treeIndex,this.consumed+=this.excess,this.excess=0)}}return-1}emitNotTerminatedNamedEntity(){var e;let{result:t,decodeTree:a}=this,s=(a[t]&_u.VALUE_LENGTH)>>14;return this.emitNamedEntityData(t,s,this.consumed),(e=this.errors)===null||e===void 0||e.missingSemicolonAfterCharacterReference(),this.consumed}emitNamedEntityData(e,t,a){let{decodeTree:s}=this;return this.emitCodePoint(t===1?s[e]&~_u.VALUE_LENGTH:s[e+1],a),t===3&&this.emitCodePoint(s[e+2],a),a}end(){var e;switch(this.state){case U.NamedEntity:return this.result!==0&&(this.decodeMode!==lu.Attribute||this.result===this.treeIndex)?this.emitNotTerminatedNamedEntity():0;case U.NumericDecimal:return this.emitNumericEntity(0,2);case U.NumericHex:return this.emitNumericEntity(0,3);case U.NumericStart:return(e=this.errors)===null||e===void 0||e.absenceOfDigitsInNumericCharacterReference(this.consumed),0;case U.EntityStart:return 0}}};function Ea(u){let e="",t=new Ve(u,a=>e+=Zt(a));return function(s,i){let n=0,d=0;for(;(d=s.indexOf("&",d))>=0;){e+=s.slice(n,d),t.startEntity(i);let h=t.write(s,d+1);if(h<0){n=d+t.end();break}n=d+h,d=h===0?n+1:n}let l=e+s.slice(n);return e="",l}}function ws(u,e,t,a){let s=(e&_u.BRANCH_LENGTH)>>7,i=e&_u.JUMP_TABLE;if(s===0)return i!==0&&a===i?t:-1;if(i){let l=a-i;return l<0||l>=s?-1:u[t+l]-1}let n=t,d=n+s-1;for(;n<=d;){let l=n+d>>>1,h=u[l];if(h<a)n=l+1;else if(h>a)d=l-1;else return u[l+s]}return-1}var bd=Ea(ha),hd=Ea(ma);function Ge(u){for(let e=1;e<u.length;e++)u[e][0]+=u[e-1][0]+1;return u}var Bs=new Map(Ge([[9,"&Tab;"],[0,"&NewLine;"],[22,"&excl;"],[0,"&quot;"],[0,"&num;"],[0,"&dollar;"],[0,"&percnt;"],[0,"&amp;"],[0,"&apos;"],[0,"&lpar;"],[0,"&rpar;"],[0,"&ast;"],[0,"&plus;"],[0,"&comma;"],[1,"&period;"],[0,"&sol;"],[10,"&colon;"],[0,"&semi;"],[0,{v:"&lt;",n:8402,o:"&nvlt;"}],[0,{v:"&equals;",n:8421,o:"&bne;"}],[0,{v:"&gt;",n:8402,o:"&nvgt;"}],[0,"&quest;"],[0,"&commat;"],[26,"&lbrack;"],[0,"&bsol;"],[0,"&rbrack;"],[0,"&Hat;"],[0,"&lowbar;"],[0,"&DiacriticalGrave;"],[5,{n:106,o:"&fjlig;"}],[20,"&lbrace;"],[0,"&verbar;"],[0,"&rbrace;"],[34,"&nbsp;"],[0,"&iexcl;"],[0,"&cent;"],[0,"&pound;"],[0,"&curren;"],[0,"&yen;"],[0,"&brvbar;"],[0,"&sect;"],[0,"&die;"],[0,"&copy;"],[0,"&ordf;"],[0,"&laquo;"],[0,"&not;"],[0,"&shy;"],[0,"&circledR;"],[0,"&macr;"],[0,"&deg;"],[0,"&PlusMinus;"],[0,"&sup2;"],[0,"&sup3;"],[0,"&acute;"],[0,"&micro;"],[0,"&para;"],[0,"&centerdot;"],[0,"&cedil;"],[0,"&sup1;"],[0,"&ordm;"],[0,"&raquo;"],[0,"&frac14;"],[0,"&frac12;"],[0,"&frac34;"],[0,"&iquest;"],[0,"&Agrave;"],[0,"&Aacute;"],[0,"&Acirc;"],[0,"&Atilde;"],[0,"&Auml;"],[0,"&angst;"],[0,"&AElig;"],[0,"&Ccedil;"],[0,"&Egrave;"],[0,"&Eacute;"],[0,"&Ecirc;"],[0,"&Euml;"],[0,"&Igrave;"],[0,"&Iacute;"],[0,"&Icirc;"],[0,"&Iuml;"],[0,"&ETH;"],[0,"&Ntilde;"],[0,"&Ograve;"],[0,"&Oacute;"],[0,"&Ocirc;"],[0,"&Otilde;"],[0,"&Ouml;"],[0,"&times;"],[0,"&Oslash;"],[0,"&Ugrave;"],[0,"&Uacute;"],[0,"&Ucirc;"],[0,"&Uuml;"],[0,"&Yacute;"],[0,"&THORN;"],[0,"&szlig;"],[0,"&agrave;"],[0,"&aacute;"],[0,"&acirc;"],[0,"&atilde;"],[0,"&auml;"],[0,"&aring;"],[0,"&aelig;"],[0,"&ccedil;"],[0,"&egrave;"],[0,"&eacute;"],[0,"&ecirc;"],[0,"&euml;"],[0,"&igrave;"],[0,"&iacute;"],[0,"&icirc;"],[0,"&iuml;"],[0,"&eth;"],[0,"&ntilde;"],[0,"&ograve;"],[0,"&oacute;"],[0,"&ocirc;"],[0,"&otilde;"],[0,"&ouml;"],[0,"&div;"],[0,"&oslash;"],[0,"&ugrave;"],[0,"&uacute;"],[0,"&ucirc;"],[0,"&uuml;"],[0,"&yacute;"],[0,"&thorn;"],[0,"&yuml;"],[0,"&Amacr;"],[0,"&amacr;"],[0,"&Abreve;"],[0,"&abreve;"],[0,"&Aogon;"],[0,"&aogon;"],[0,"&Cacute;"],[0,"&cacute;"],[0,"&Ccirc;"],[0,"&ccirc;"],[0,"&Cdot;"],[0,"&cdot;"],[0,"&Ccaron;"],[0,"&ccaron;"],[0,"&Dcaron;"],[0,"&dcaron;"],[0,"&Dstrok;"],[0,"&dstrok;"],[0,"&Emacr;"],[0,"&emacr;"],[2,"&Edot;"],[0,"&edot;"],[0,"&Eogon;"],[0,"&eogon;"],[0,"&Ecaron;"],[0,"&ecaron;"],[0,"&Gcirc;"],[0,"&gcirc;"],[0,"&Gbreve;"],[0,"&gbreve;"],[0,"&Gdot;"],[0,"&gdot;"],[0,"&Gcedil;"],[1,"&Hcirc;"],[0,"&hcirc;"],[0,"&Hstrok;"],[0,"&hstrok;"],[0,"&Itilde;"],[0,"&itilde;"],[0,"&Imacr;"],[0,"&imacr;"],[2,"&Iogon;"],[0,"&iogon;"],[0,"&Idot;"],[0,"&imath;"],[0,"&IJlig;"],[0,"&ijlig;"],[0,"&Jcirc;"],[0,"&jcirc;"],[0,"&Kcedil;"],[0,"&kcedil;"],[0,"&kgreen;"],[0,"&Lacute;"],[0,"&lacute;"],[0,"&Lcedil;"],[0,"&lcedil;"],[0,"&Lcaron;"],[0,"&lcaron;"],[0,"&Lmidot;"],[0,"&lmidot;"],[0,"&Lstrok;"],[0,"&lstrok;"],[0,"&Nacute;"],[0,"&nacute;"],[0,"&Ncedil;"],[0,"&ncedil;"],[0,"&Ncaron;"],[0,"&ncaron;"],[0,"&napos;"],[0,"&ENG;"],[0,"&eng;"],[0,"&Omacr;"],[0,"&omacr;"],[2,"&Odblac;"],[0,"&odblac;"],[0,"&OElig;"],[0,"&oelig;"],[0,"&Racute;"],[0,"&racute;"],[0,"&Rcedil;"],[0,"&rcedil;"],[0,"&Rcaron;"],[0,"&rcaron;"],[0,"&Sacute;"],[0,"&sacute;"],[0,"&Scirc;"],[0,"&scirc;"],[0,"&Scedil;"],[0,"&scedil;"],[0,"&Scaron;"],[0,"&scaron;"],[0,"&Tcedil;"],[0,"&tcedil;"],[0,"&Tcaron;"],[0,"&tcaron;"],[0,"&Tstrok;"],[0,"&tstrok;"],[0,"&Utilde;"],[0,"&utilde;"],[0,"&Umacr;"],[0,"&umacr;"],[0,"&Ubreve;"],[0,"&ubreve;"],[0,"&Uring;"],[0,"&uring;"],[0,"&Udblac;"],[0,"&udblac;"],[0,"&Uogon;"],[0,"&uogon;"],[0,"&Wcirc;"],[0,"&wcirc;"],[0,"&Ycirc;"],[0,"&ycirc;"],[0,"&Yuml;"],[0,"&Zacute;"],[0,"&zacute;"],[0,"&Zdot;"],[0,"&zdot;"],[0,"&Zcaron;"],[0,"&zcaron;"],[19,"&fnof;"],[34,"&imped;"],[63,"&gacute;"],[65,"&jmath;"],[142,"&circ;"],[0,"&caron;"],[16,"&breve;"],[0,"&DiacriticalDot;"],[0,"&ring;"],[0,"&ogon;"],[0,"&DiacriticalTilde;"],[0,"&dblac;"],[51,"&DownBreve;"],[127,"&Alpha;"],[0,"&Beta;"],[0,"&Gamma;"],[0,"&Delta;"],[0,"&Epsilon;"],[0,"&Zeta;"],[0,"&Eta;"],[0,"&Theta;"],[0,"&Iota;"],[0,"&Kappa;"],[0,"&Lambda;"],[0,"&Mu;"],[0,"&Nu;"],[0,"&Xi;"],[0,"&Omicron;"],[0,"&Pi;"],[0,"&Rho;"],[1,"&Sigma;"],[0,"&Tau;"],[0,"&Upsilon;"],[0,"&Phi;"],[0,"&Chi;"],[0,"&Psi;"],[0,"&ohm;"],[7,"&alpha;"],[0,"&beta;"],[0,"&gamma;"],[0,"&delta;"],[0,"&epsi;"],[0,"&zeta;"],[0,"&eta;"],[0,"&theta;"],[0,"&iota;"],[0,"&kappa;"],[0,"&lambda;"],[0,"&mu;"],[0,"&nu;"],[0,"&xi;"],[0,"&omicron;"],[0,"&pi;"],[0,"&rho;"],[0,"&sigmaf;"],[0,"&sigma;"],[0,"&tau;"],[0,"&upsi;"],[0,"&phi;"],[0,"&chi;"],[0,"&psi;"],[0,"&omega;"],[7,"&thetasym;"],[0,"&Upsi;"],[2,"&phiv;"],[0,"&piv;"],[5,"&Gammad;"],[0,"&digamma;"],[18,"&kappav;"],[0,"&rhov;"],[3,"&epsiv;"],[0,"&backepsilon;"],[10,"&IOcy;"],[0,"&DJcy;"],[0,"&GJcy;"],[0,"&Jukcy;"],[0,"&DScy;"],[0,"&Iukcy;"],[0,"&YIcy;"],[0,"&Jsercy;"],[0,"&LJcy;"],[0,"&NJcy;"],[0,"&TSHcy;"],[0,"&KJcy;"],[1,"&Ubrcy;"],[0,"&DZcy;"],[0,"&Acy;"],[0,"&Bcy;"],[0,"&Vcy;"],[0,"&Gcy;"],[0,"&Dcy;"],[0,"&IEcy;"],[0,"&ZHcy;"],[0,"&Zcy;"],[0,"&Icy;"],[0,"&Jcy;"],[0,"&Kcy;"],[0,"&Lcy;"],[0,"&Mcy;"],[0,"&Ncy;"],[0,"&Ocy;"],[0,"&Pcy;"],[0,"&Rcy;"],[0,"&Scy;"],[0,"&Tcy;"],[0,"&Ucy;"],[0,"&Fcy;"],[0,"&KHcy;"],[0,"&TScy;"],[0,"&CHcy;"],[0,"&SHcy;"],[0,"&SHCHcy;"],[0,"&HARDcy;"],[0,"&Ycy;"],[0,"&SOFTcy;"],[0,"&Ecy;"],[0,"&YUcy;"],[0,"&YAcy;"],[0,"&acy;"],[0,"&bcy;"],[0,"&vcy;"],[0,"&gcy;"],[0,"&dcy;"],[0,"&iecy;"],[0,"&zhcy;"],[0,"&zcy;"],[0,"&icy;"],[0,"&jcy;"],[0,"&kcy;"],[0,"&lcy;"],[0,"&mcy;"],[0,"&ncy;"],[0,"&ocy;"],[0,"&pcy;"],[0,"&rcy;"],[0,"&scy;"],[0,"&tcy;"],[0,"&ucy;"],[0,"&fcy;"],[0,"&khcy;"],[0,"&tscy;"],[0,"&chcy;"],[0,"&shcy;"],[0,"&shchcy;"],[0,"&hardcy;"],[0,"&ycy;"],[0,"&softcy;"],[0,"&ecy;"],[0,"&yucy;"],[0,"&yacy;"],[1,"&iocy;"],[0,"&djcy;"],[0,"&gjcy;"],[0,"&jukcy;"],[0,"&dscy;"],[0,"&iukcy;"],[0,"&yicy;"],[0,"&jsercy;"],[0,"&ljcy;"],[0,"&njcy;"],[0,"&tshcy;"],[0,"&kjcy;"],[1,"&ubrcy;"],[0,"&dzcy;"],[7074,"&ensp;"],[0,"&emsp;"],[0,"&emsp13;"],[0,"&emsp14;"],[1,"&numsp;"],[0,"&puncsp;"],[0,"&ThinSpace;"],[0,"&hairsp;"],[0,"&NegativeMediumSpace;"],[0,"&zwnj;"],[0,"&zwj;"],[0,"&lrm;"],[0,"&rlm;"],[0,"&dash;"],[2,"&ndash;"],[0,"&mdash;"],[0,"&horbar;"],[0,"&Verbar;"],[1,"&lsquo;"],[0,"&CloseCurlyQuote;"],[0,"&lsquor;"],[1,"&ldquo;"],[0,"&CloseCurlyDoubleQuote;"],[0,"&bdquo;"],[1,"&dagger;"],[0,"&Dagger;"],[0,"&bull;"],[2,"&nldr;"],[0,"&hellip;"],[9,"&permil;"],[0,"&pertenk;"],[0,"&prime;"],[0,"&Prime;"],[0,"&tprime;"],[0,"&backprime;"],[3,"&lsaquo;"],[0,"&rsaquo;"],[3,"&oline;"],[2,"&caret;"],[1,"&hybull;"],[0,"&frasl;"],[10,"&bsemi;"],[7,"&qprime;"],[7,{v:"&MediumSpace;",n:8202,o:"&ThickSpace;"}],[0,"&NoBreak;"],[0,"&af;"],[0,"&InvisibleTimes;"],[0,"&ic;"],[72,"&euro;"],[46,"&tdot;"],[0,"&DotDot;"],[37,"&complexes;"],[2,"&incare;"],[4,"&gscr;"],[0,"&hamilt;"],[0,"&Hfr;"],[0,"&Hopf;"],[0,"&planckh;"],[0,"&hbar;"],[0,"&imagline;"],[0,"&Ifr;"],[0,"&lagran;"],[0,"&ell;"],[1,"&naturals;"],[0,"&numero;"],[0,"&copysr;"],[0,"&weierp;"],[0,"&Popf;"],[0,"&Qopf;"],[0,"&realine;"],[0,"&real;"],[0,"&reals;"],[0,"&rx;"],[3,"&trade;"],[1,"&integers;"],[2,"&mho;"],[0,"&zeetrf;"],[0,"&iiota;"],[2,"&bernou;"],[0,"&Cayleys;"],[1,"&escr;"],[0,"&Escr;"],[0,"&Fouriertrf;"],[1,"&Mellintrf;"],[0,"&order;"],[0,"&alefsym;"],[0,"&beth;"],[0,"&gimel;"],[0,"&daleth;"],[12,"&CapitalDifferentialD;"],[0,"&dd;"],[0,"&ee;"],[0,"&ii;"],[10,"&frac13;"],[0,"&frac23;"],[0,"&frac15;"],[0,"&frac25;"],[0,"&frac35;"],[0,"&frac45;"],[0,"&frac16;"],[0,"&frac56;"],[0,"&frac18;"],[0,"&frac38;"],[0,"&frac58;"],[0,"&frac78;"],[49,"&larr;"],[0,"&ShortUpArrow;"],[0,"&rarr;"],[0,"&darr;"],[0,"&harr;"],[0,"&updownarrow;"],[0,"&nwarr;"],[0,"&nearr;"],[0,"&LowerRightArrow;"],[0,"&LowerLeftArrow;"],[0,"&nlarr;"],[0,"&nrarr;"],[1,{v:"&rarrw;",n:824,o:"&nrarrw;"}],[0,"&Larr;"],[0,"&Uarr;"],[0,"&Rarr;"],[0,"&Darr;"],[0,"&larrtl;"],[0,"&rarrtl;"],[0,"&LeftTeeArrow;"],[0,"&mapstoup;"],[0,"&map;"],[0,"&DownTeeArrow;"],[1,"&hookleftarrow;"],[0,"&hookrightarrow;"],[0,"&larrlp;"],[0,"&looparrowright;"],[0,"&harrw;"],[0,"&nharr;"],[1,"&lsh;"],[0,"&rsh;"],[0,"&ldsh;"],[0,"&rdsh;"],[1,"&crarr;"],[0,"&cularr;"],[0,"&curarr;"],[2,"&circlearrowleft;"],[0,"&circlearrowright;"],[0,"&leftharpoonup;"],[0,"&DownLeftVector;"],[0,"&RightUpVector;"],[0,"&LeftUpVector;"],[0,"&rharu;"],[0,"&DownRightVector;"],[0,"&dharr;"],[0,"&dharl;"],[0,"&RightArrowLeftArrow;"],[0,"&udarr;"],[0,"&LeftArrowRightArrow;"],[0,"&leftleftarrows;"],[0,"&upuparrows;"],[0,"&rightrightarrows;"],[0,"&ddarr;"],[0,"&leftrightharpoons;"],[0,"&Equilibrium;"],[0,"&nlArr;"],[0,"&nhArr;"],[0,"&nrArr;"],[0,"&DoubleLeftArrow;"],[0,"&DoubleUpArrow;"],[0,"&DoubleRightArrow;"],[0,"&dArr;"],[0,"&DoubleLeftRightArrow;"],[0,"&DoubleUpDownArrow;"],[0,"&nwArr;"],[0,"&neArr;"],[0,"&seArr;"],[0,"&swArr;"],[0,"&lAarr;"],[0,"&rAarr;"],[1,"&zigrarr;"],[6,"&larrb;"],[0,"&rarrb;"],[15,"&DownArrowUpArrow;"],[7,"&loarr;"],[0,"&roarr;"],[0,"&hoarr;"],[0,"&forall;"],[0,"&comp;"],[0,{v:"&part;",n:824,o:"&npart;"}],[0,"&exist;"],[0,"&nexist;"],[0,"&empty;"],[1,"&Del;"],[0,"&Element;"],[0,"&NotElement;"],[1,"&ni;"],[0,"&notni;"],[2,"&prod;"],[0,"&coprod;"],[0,"&sum;"],[0,"&minus;"],[0,"&MinusPlus;"],[0,"&dotplus;"],[1,"&Backslash;"],[0,"&lowast;"],[0,"&compfn;"],[1,"&radic;"],[2,"&prop;"],[0,"&infin;"],[0,"&angrt;"],[0,{v:"&ang;",n:8402,o:"&nang;"}],[0,"&angmsd;"],[0,"&angsph;"],[0,"&mid;"],[0,"&nmid;"],[0,"&DoubleVerticalBar;"],[0,"&NotDoubleVerticalBar;"],[0,"&and;"],[0,"&or;"],[0,{v:"&cap;",n:65024,o:"&caps;"}],[0,{v:"&cup;",n:65024,o:"&cups;"}],[0,"&int;"],[0,"&Int;"],[0,"&iiint;"],[0,"&conint;"],[0,"&Conint;"],[0,"&Cconint;"],[0,"&cwint;"],[0,"&ClockwiseContourIntegral;"],[0,"&awconint;"],[0,"&there4;"],[0,"&becaus;"],[0,"&ratio;"],[0,"&Colon;"],[0,"&dotminus;"],[1,"&mDDot;"],[0,"&homtht;"],[0,{v:"&sim;",n:8402,o:"&nvsim;"}],[0,{v:"&backsim;",n:817,o:"&race;"}],[0,{v:"&ac;",n:819,o:"&acE;"}],[0,"&acd;"],[0,"&VerticalTilde;"],[0,"&NotTilde;"],[0,{v:"&eqsim;",n:824,o:"&nesim;"}],[0,"&sime;"],[0,"&NotTildeEqual;"],[0,"&cong;"],[0,"&simne;"],[0,"&ncong;"],[0,"&ap;"],[0,"&nap;"],[0,"&ape;"],[0,{v:"&apid;",n:824,o:"&napid;"}],[0,"&backcong;"],[0,{v:"&asympeq;",n:8402,o:"&nvap;"}],[0,{v:"&bump;",n:824,o:"&nbump;"}],[0,{v:"&bumpe;",n:824,o:"&nbumpe;"}],[0,{v:"&doteq;",n:824,o:"&nedot;"}],[0,"&doteqdot;"],[0,"&efDot;"],[0,"&erDot;"],[0,"&Assign;"],[0,"&ecolon;"],[0,"&ecir;"],[0,"&circeq;"],[1,"&wedgeq;"],[0,"&veeeq;"],[1,"&triangleq;"],[2,"&equest;"],[0,"&ne;"],[0,{v:"&Congruent;",n:8421,o:"&bnequiv;"}],[0,"&nequiv;"],[1,{v:"&le;",n:8402,o:"&nvle;"}],[0,{v:"&ge;",n:8402,o:"&nvge;"}],[0,{v:"&lE;",n:824,o:"&nlE;"}],[0,{v:"&gE;",n:824,o:"&ngE;"}],[0,{v:"&lnE;",n:65024,o:"&lvertneqq;"}],[0,{v:"&gnE;",n:65024,o:"&gvertneqq;"}],[0,{v:"&ll;",n:new Map(Ge([[824,"&nLtv;"],[7577,"&nLt;"]]))}],[0,{v:"&gg;",n:new Map(Ge([[824,"&nGtv;"],[7577,"&nGt;"]]))}],[0,"&between;"],[0,"&NotCupCap;"],[0,"&nless;"],[0,"&ngt;"],[0,"&nle;"],[0,"&nge;"],[0,"&lesssim;"],[0,"&GreaterTilde;"],[0,"&nlsim;"],[0,"&ngsim;"],[0,"&LessGreater;"],[0,"&gl;"],[0,"&NotLessGreater;"],[0,"&NotGreaterLess;"],[0,"&pr;"],[0,"&sc;"],[0,"&prcue;"],[0,"&sccue;"],[0,"&PrecedesTilde;"],[0,{v:"&scsim;",n:824,o:"&NotSucceedsTilde;"}],[0,"&NotPrecedes;"],[0,"&NotSucceeds;"],[0,{v:"&sub;",n:8402,o:"&NotSubset;"}],[0,{v:"&sup;",n:8402,o:"&NotSuperset;"}],[0,"&nsub;"],[0,"&nsup;"],[0,"&sube;"],[0,"&supe;"],[0,"&NotSubsetEqual;"],[0,"&NotSupersetEqual;"],[0,{v:"&subne;",n:65024,o:"&varsubsetneq;"}],[0,{v:"&supne;",n:65024,o:"&varsupsetneq;"}],[1,"&cupdot;"],[0,"&UnionPlus;"],[0,{v:"&sqsub;",n:824,o:"&NotSquareSubset;"}],[0,{v:"&sqsup;",n:824,o:"&NotSquareSuperset;"}],[0,"&sqsube;"],[0,"&sqsupe;"],[0,{v:"&sqcap;",n:65024,o:"&sqcaps;"}],[0,{v:"&sqcup;",n:65024,o:"&sqcups;"}],[0,"&CirclePlus;"],[0,"&CircleMinus;"],[0,"&CircleTimes;"],[0,"&osol;"],[0,"&CircleDot;"],[0,"&circledcirc;"],[0,"&circledast;"],[1,"&circleddash;"],[0,"&boxplus;"],[0,"&boxminus;"],[0,"&boxtimes;"],[0,"&dotsquare;"],[0,"&RightTee;"],[0,"&dashv;"],[0,"&DownTee;"],[0,"&bot;"],[1,"&models;"],[0,"&DoubleRightTee;"],[0,"&Vdash;"],[0,"&Vvdash;"],[0,"&VDash;"],[0,"&nvdash;"],[0,"&nvDash;"],[0,"&nVdash;"],[0,"&nVDash;"],[0,"&prurel;"],[1,"&LeftTriangle;"],[0,"&RightTriangle;"],[0,{v:"&LeftTriangleEqual;",n:8402,o:"&nvltrie;"}],[0,{v:"&RightTriangleEqual;",n:8402,o:"&nvrtrie;"}],[0,"&origof;"],[0,"&imof;"],[0,"&multimap;"],[0,"&hercon;"],[0,"&intcal;"],[0,"&veebar;"],[1,"&barvee;"],[0,"&angrtvb;"],[0,"&lrtri;"],[0,"&bigwedge;"],[0,"&bigvee;"],[0,"&bigcap;"],[0,"&bigcup;"],[0,"&diam;"],[0,"&sdot;"],[0,"&sstarf;"],[0,"&divideontimes;"],[0,"&bowtie;"],[0,"&ltimes;"],[0,"&rtimes;"],[0,"&leftthreetimes;"],[0,"&rightthreetimes;"],[0,"&backsimeq;"],[0,"&curlyvee;"],[0,"&curlywedge;"],[0,"&Sub;"],[0,"&Sup;"],[0,"&Cap;"],[0,"&Cup;"],[0,"&fork;"],[0,"&epar;"],[0,"&lessdot;"],[0,"&gtdot;"],[0,{v:"&Ll;",n:824,o:"&nLl;"}],[0,{v:"&Gg;",n:824,o:"&nGg;"}],[0,{v:"&leg;",n:65024,o:"&lesg;"}],[0,{v:"&gel;",n:65024,o:"&gesl;"}],[2,"&cuepr;"],[0,"&cuesc;"],[0,"&NotPrecedesSlantEqual;"],[0,"&NotSucceedsSlantEqual;"],[0,"&NotSquareSubsetEqual;"],[0,"&NotSquareSupersetEqual;"],[2,"&lnsim;"],[0,"&gnsim;"],[0,"&precnsim;"],[0,"&scnsim;"],[0,"&nltri;"],[0,"&NotRightTriangle;"],[0,"&nltrie;"],[0,"&NotRightTriangleEqual;"],[0,"&vellip;"],[0,"&ctdot;"],[0,"&utdot;"],[0,"&dtdot;"],[0,"&disin;"],[0,"&isinsv;"],[0,"&isins;"],[0,{v:"&isindot;",n:824,o:"&notindot;"}],[0,"&notinvc;"],[0,"&notinvb;"],[1,{v:"&isinE;",n:824,o:"&notinE;"}],[0,"&nisd;"],[0,"&xnis;"],[0,"&nis;"],[0,"&notnivc;"],[0,"&notnivb;"],[6,"&barwed;"],[0,"&Barwed;"],[1,"&lceil;"],[0,"&rceil;"],[0,"&LeftFloor;"],[0,"&rfloor;"],[0,"&drcrop;"],[0,"&dlcrop;"],[0,"&urcrop;"],[0,"&ulcrop;"],[0,"&bnot;"],[1,"&profline;"],[0,"&profsurf;"],[1,"&telrec;"],[0,"&target;"],[5,"&ulcorn;"],[0,"&urcorn;"],[0,"&dlcorn;"],[0,"&drcorn;"],[2,"&frown;"],[0,"&smile;"],[9,"&cylcty;"],[0,"&profalar;"],[7,"&topbot;"],[6,"&ovbar;"],[1,"&solbar;"],[60,"&angzarr;"],[51,"&lmoustache;"],[0,"&rmoustache;"],[2,"&OverBracket;"],[0,"&bbrk;"],[0,"&bbrktbrk;"],[37,"&OverParenthesis;"],[0,"&UnderParenthesis;"],[0,"&OverBrace;"],[0,"&UnderBrace;"],[2,"&trpezium;"],[4,"&elinters;"],[59,"&blank;"],[164,"&circledS;"],[55,"&boxh;"],[1,"&boxv;"],[9,"&boxdr;"],[3,"&boxdl;"],[3,"&boxur;"],[3,"&boxul;"],[3,"&boxvr;"],[7,"&boxvl;"],[7,"&boxhd;"],[7,"&boxhu;"],[7,"&boxvh;"],[19,"&boxH;"],[0,"&boxV;"],[0,"&boxdR;"],[0,"&boxDr;"],[0,"&boxDR;"],[0,"&boxdL;"],[0,"&boxDl;"],[0,"&boxDL;"],[0,"&boxuR;"],[0,"&boxUr;"],[0,"&boxUR;"],[0,"&boxuL;"],[0,"&boxUl;"],[0,"&boxUL;"],[0,"&boxvR;"],[0,"&boxVr;"],[0,"&boxVR;"],[0,"&boxvL;"],[0,"&boxVl;"],[0,"&boxVL;"],[0,"&boxHd;"],[0,"&boxhD;"],[0,"&boxHD;"],[0,"&boxHu;"],[0,"&boxhU;"],[0,"&boxHU;"],[0,"&boxvH;"],[0,"&boxVh;"],[0,"&boxVH;"],[19,"&uhblk;"],[3,"&lhblk;"],[3,"&block;"],[8,"&blk14;"],[0,"&blk12;"],[0,"&blk34;"],[13,"&square;"],[8,"&blacksquare;"],[0,"&EmptyVerySmallSquare;"],[1,"&rect;"],[0,"&marker;"],[2,"&fltns;"],[1,"&bigtriangleup;"],[0,"&blacktriangle;"],[0,"&triangle;"],[2,"&blacktriangleright;"],[0,"&rtri;"],[3,"&bigtriangledown;"],[0,"&blacktriangledown;"],[0,"&dtri;"],[2,"&blacktriangleleft;"],[0,"&ltri;"],[6,"&loz;"],[0,"&cir;"],[32,"&tridot;"],[2,"&bigcirc;"],[8,"&ultri;"],[0,"&urtri;"],[0,"&lltri;"],[0,"&EmptySmallSquare;"],[0,"&FilledSmallSquare;"],[8,"&bigstar;"],[0,"&star;"],[7,"&phone;"],[49,"&female;"],[1,"&male;"],[29,"&spades;"],[2,"&clubs;"],[1,"&hearts;"],[0,"&diamondsuit;"],[3,"&sung;"],[2,"&flat;"],[0,"&natural;"],[0,"&sharp;"],[163,"&check;"],[3,"&cross;"],[8,"&malt;"],[21,"&sext;"],[33,"&VerticalSeparator;"],[25,"&lbbrk;"],[0,"&rbbrk;"],[84,"&bsolhsub;"],[0,"&suphsol;"],[28,"&LeftDoubleBracket;"],[0,"&RightDoubleBracket;"],[0,"&lang;"],[0,"&rang;"],[0,"&Lang;"],[0,"&Rang;"],[0,"&loang;"],[0,"&roang;"],[7,"&longleftarrow;"],[0,"&longrightarrow;"],[0,"&longleftrightarrow;"],[0,"&DoubleLongLeftArrow;"],[0,"&DoubleLongRightArrow;"],[0,"&DoubleLongLeftRightArrow;"],[1,"&longmapsto;"],[2,"&dzigrarr;"],[258,"&nvlArr;"],[0,"&nvrArr;"],[0,"&nvHarr;"],[0,"&Map;"],[6,"&lbarr;"],[0,"&bkarow;"],[0,"&lBarr;"],[0,"&dbkarow;"],[0,"&drbkarow;"],[0,"&DDotrahd;"],[0,"&UpArrowBar;"],[0,"&DownArrowBar;"],[2,"&Rarrtl;"],[2,"&latail;"],[0,"&ratail;"],[0,"&lAtail;"],[0,"&rAtail;"],[0,"&larrfs;"],[0,"&rarrfs;"],[0,"&larrbfs;"],[0,"&rarrbfs;"],[2,"&nwarhk;"],[0,"&nearhk;"],[0,"&hksearow;"],[0,"&hkswarow;"],[0,"&nwnear;"],[0,"&nesear;"],[0,"&seswar;"],[0,"&swnwar;"],[8,{v:"&rarrc;",n:824,o:"&nrarrc;"}],[1,"&cudarrr;"],[0,"&ldca;"],[0,"&rdca;"],[0,"&cudarrl;"],[0,"&larrpl;"],[2,"&curarrm;"],[0,"&cularrp;"],[7,"&rarrpl;"],[2,"&harrcir;"],[0,"&Uarrocir;"],[0,"&lurdshar;"],[0,"&ldrushar;"],[2,"&LeftRightVector;"],[0,"&RightUpDownVector;"],[0,"&DownLeftRightVector;"],[0,"&LeftUpDownVector;"],[0,"&LeftVectorBar;"],[0,"&RightVectorBar;"],[0,"&RightUpVectorBar;"],[0,"&RightDownVectorBar;"],[0,"&DownLeftVectorBar;"],[0,"&DownRightVectorBar;"],[0,"&LeftUpVectorBar;"],[0,"&LeftDownVectorBar;"],[0,"&LeftTeeVector;"],[0,"&RightTeeVector;"],[0,"&RightUpTeeVector;"],[0,"&RightDownTeeVector;"],[0,"&DownLeftTeeVector;"],[0,"&DownRightTeeVector;"],[0,"&LeftUpTeeVector;"],[0,"&LeftDownTeeVector;"],[0,"&lHar;"],[0,"&uHar;"],[0,"&rHar;"],[0,"&dHar;"],[0,"&luruhar;"],[0,"&ldrdhar;"],[0,"&ruluhar;"],[0,"&rdldhar;"],[0,"&lharul;"],[0,"&llhard;"],[0,"&rharul;"],[0,"&lrhard;"],[0,"&udhar;"],[0,"&duhar;"],[0,"&RoundImplies;"],[0,"&erarr;"],[0,"&simrarr;"],[0,"&larrsim;"],[0,"&rarrsim;"],[0,"&rarrap;"],[0,"&ltlarr;"],[1,"&gtrarr;"],[0,"&subrarr;"],[1,"&suplarr;"],[0,"&lfisht;"],[0,"&rfisht;"],[0,"&ufisht;"],[0,"&dfisht;"],[5,"&lopar;"],[0,"&ropar;"],[4,"&lbrke;"],[0,"&rbrke;"],[0,"&lbrkslu;"],[0,"&rbrksld;"],[0,"&lbrksld;"],[0,"&rbrkslu;"],[0,"&langd;"],[0,"&rangd;"],[0,"&lparlt;"],[0,"&rpargt;"],[0,"&gtlPar;"],[0,"&ltrPar;"],[3,"&vzigzag;"],[1,"&vangrt;"],[0,"&angrtvbd;"],[6,"&ange;"],[0,"&range;"],[0,"&dwangle;"],[0,"&uwangle;"],[0,"&angmsdaa;"],[0,"&angmsdab;"],[0,"&angmsdac;"],[0,"&angmsdad;"],[0,"&angmsdae;"],[0,"&angmsdaf;"],[0,"&angmsdag;"],[0,"&angmsdah;"],[0,"&bemptyv;"],[0,"&demptyv;"],[0,"&cemptyv;"],[0,"&raemptyv;"],[0,"&laemptyv;"],[0,"&ohbar;"],[0,"&omid;"],[0,"&opar;"],[1,"&operp;"],[1,"&olcross;"],[0,"&odsold;"],[1,"&olcir;"],[0,"&ofcir;"],[0,"&olt;"],[0,"&ogt;"],[0,"&cirscir;"],[0,"&cirE;"],[0,"&solb;"],[0,"&bsolb;"],[3,"&boxbox;"],[3,"&trisb;"],[0,"&rtriltri;"],[0,{v:"&LeftTriangleBar;",n:824,o:"&NotLeftTriangleBar;"}],[0,{v:"&RightTriangleBar;",n:824,o:"&NotRightTriangleBar;"}],[11,"&iinfin;"],[0,"&infintie;"],[0,"&nvinfin;"],[4,"&eparsl;"],[0,"&smeparsl;"],[0,"&eqvparsl;"],[5,"&blacklozenge;"],[8,"&RuleDelayed;"],[1,"&dsol;"],[9,"&bigodot;"],[0,"&bigoplus;"],[0,"&bigotimes;"],[1,"&biguplus;"],[1,"&bigsqcup;"],[5,"&iiiint;"],[0,"&fpartint;"],[2,"&cirfnint;"],[0,"&awint;"],[0,"&rppolint;"],[0,"&scpolint;"],[0,"&npolint;"],[0,"&pointint;"],[0,"&quatint;"],[0,"&intlarhk;"],[10,"&pluscir;"],[0,"&plusacir;"],[0,"&simplus;"],[0,"&plusdu;"],[0,"&plussim;"],[0,"&plustwo;"],[1,"&mcomma;"],[0,"&minusdu;"],[2,"&loplus;"],[0,"&roplus;"],[0,"&Cross;"],[0,"&timesd;"],[0,"&timesbar;"],[1,"&smashp;"],[0,"&lotimes;"],[0,"&rotimes;"],[0,"&otimesas;"],[0,"&Otimes;"],[0,"&odiv;"],[0,"&triplus;"],[0,"&triminus;"],[0,"&tritime;"],[0,"&intprod;"],[2,"&amalg;"],[0,"&capdot;"],[1,"&ncup;"],[0,"&ncap;"],[0,"&capand;"],[0,"&cupor;"],[0,"&cupcap;"],[0,"&capcup;"],[0,"&cupbrcap;"],[0,"&capbrcup;"],[0,"&cupcup;"],[0,"&capcap;"],[0,"&ccups;"],[0,"&ccaps;"],[2,"&ccupssm;"],[2,"&And;"],[0,"&Or;"],[0,"&andand;"],[0,"&oror;"],[0,"&orslope;"],[0,"&andslope;"],[1,"&andv;"],[0,"&orv;"],[0,"&andd;"],[0,"&ord;"],[1,"&wedbar;"],[6,"&sdote;"],[3,"&simdot;"],[2,{v:"&congdot;",n:824,o:"&ncongdot;"}],[0,"&easter;"],[0,"&apacir;"],[0,{v:"&apE;",n:824,o:"&napE;"}],[0,"&eplus;"],[0,"&pluse;"],[0,"&Esim;"],[0,"&Colone;"],[0,"&Equal;"],[1,"&ddotseq;"],[0,"&equivDD;"],[0,"&ltcir;"],[0,"&gtcir;"],[0,"&ltquest;"],[0,"&gtquest;"],[0,{v:"&leqslant;",n:824,o:"&nleqslant;"}],[0,{v:"&geqslant;",n:824,o:"&ngeqslant;"}],[0,"&lesdot;"],[0,"&gesdot;"],[0,"&lesdoto;"],[0,"&gesdoto;"],[0,"&lesdotor;"],[0,"&gesdotol;"],[0,"&lap;"],[0,"&gap;"],[0,"&lne;"],[0,"&gne;"],[0,"&lnap;"],[0,"&gnap;"],[0,"&lEg;"],[0,"&gEl;"],[0,"&lsime;"],[0,"&gsime;"],[0,"&lsimg;"],[0,"&gsiml;"],[0,"&lgE;"],[0,"&glE;"],[0,"&lesges;"],[0,"&gesles;"],[0,"&els;"],[0,"&egs;"],[0,"&elsdot;"],[0,"&egsdot;"],[0,"&el;"],[0,"&eg;"],[2,"&siml;"],[0,"&simg;"],[0,"&simlE;"],[0,"&simgE;"],[0,{v:"&LessLess;",n:824,o:"&NotNestedLessLess;"}],[0,{v:"&GreaterGreater;",n:824,o:"&NotNestedGreaterGreater;"}],[1,"&glj;"],[0,"&gla;"],[0,"&ltcc;"],[0,"&gtcc;"],[0,"&lescc;"],[0,"&gescc;"],[0,"&smt;"],[0,"&lat;"],[0,{v:"&smte;",n:65024,o:"&smtes;"}],[0,{v:"&late;",n:65024,o:"&lates;"}],[0,"&bumpE;"],[0,{v:"&PrecedesEqual;",n:824,o:"&NotPrecedesEqual;"}],[0,{v:"&sce;",n:824,o:"&NotSucceedsEqual;"}],[2,"&prE;"],[0,"&scE;"],[0,"&precneqq;"],[0,"&scnE;"],[0,"&prap;"],[0,"&scap;"],[0,"&precnapprox;"],[0,"&scnap;"],[0,"&Pr;"],[0,"&Sc;"],[0,"&subdot;"],[0,"&supdot;"],[0,"&subplus;"],[0,"&supplus;"],[0,"&submult;"],[0,"&supmult;"],[0,"&subedot;"],[0,"&supedot;"],[0,{v:"&subE;",n:824,o:"&nsubE;"}],[0,{v:"&supE;",n:824,o:"&nsupE;"}],[0,"&subsim;"],[0,"&supsim;"],[2,{v:"&subnE;",n:65024,o:"&varsubsetneqq;"}],[0,{v:"&supnE;",n:65024,o:"&varsupsetneqq;"}],[2,"&csub;"],[0,"&csup;"],[0,"&csube;"],[0,"&csupe;"],[0,"&subsup;"],[0,"&supsub;"],[0,"&subsub;"],[0,"&supsup;"],[0,"&suphsub;"],[0,"&supdsub;"],[0,"&forkv;"],[0,"&topfork;"],[0,"&mlcp;"],[8,"&Dashv;"],[1,"&Vdashl;"],[0,"&Barv;"],[0,"&vBar;"],[0,"&vBarv;"],[1,"&Vbar;"],[0,"&Not;"],[0,"&bNot;"],[0,"&rnmid;"],[0,"&cirmid;"],[0,"&midcir;"],[0,"&topcir;"],[0,"&nhpar;"],[0,"&parsim;"],[9,{v:"&parsl;",n:8421,o:"&nparsl;"}],[44343,{n:new Map(Ge([[56476,"&Ascr;"],[1,"&Cscr;"],[0,"&Dscr;"],[2,"&Gscr;"],[2,"&Jscr;"],[0,"&Kscr;"],[2,"&Nscr;"],[0,"&Oscr;"],[0,"&Pscr;"],[0,"&Qscr;"],[1,"&Sscr;"],[0,"&Tscr;"],[0,"&Uscr;"],[0,"&Vscr;"],[0,"&Wscr;"],[0,"&Xscr;"],[0,"&Yscr;"],[0,"&Zscr;"],[0,"&ascr;"],[0,"&bscr;"],[0,"&cscr;"],[0,"&dscr;"],[1,"&fscr;"],[1,"&hscr;"],[0,"&iscr;"],[0,"&jscr;"],[0,"&kscr;"],[0,"&lscr;"],[0,"&mscr;"],[0,"&nscr;"],[1,"&pscr;"],[0,"&qscr;"],[0,"&rscr;"],[0,"&sscr;"],[0,"&tscr;"],[0,"&uscr;"],[0,"&vscr;"],[0,"&wscr;"],[0,"&xscr;"],[0,"&yscr;"],[0,"&zscr;"],[52,"&Afr;"],[0,"&Bfr;"],[1,"&Dfr;"],[0,"&Efr;"],[0,"&Ffr;"],[0,"&Gfr;"],[2,"&Jfr;"],[0,"&Kfr;"],[0,"&Lfr;"],[0,"&Mfr;"],[0,"&Nfr;"],[0,"&Ofr;"],[0,"&Pfr;"],[0,"&Qfr;"],[1,"&Sfr;"],[0,"&Tfr;"],[0,"&Ufr;"],[0,"&Vfr;"],[0,"&Wfr;"],[0,"&Xfr;"],[0,"&Yfr;"],[1,"&afr;"],[0,"&bfr;"],[0,"&cfr;"],[0,"&dfr;"],[0,"&efr;"],[0,"&ffr;"],[0,"&gfr;"],[0,"&hfr;"],[0,"&ifr;"],[0,"&jfr;"],[0,"&kfr;"],[0,"&lfr;"],[0,"&mfr;"],[0,"&nfr;"],[0,"&ofr;"],[0,"&pfr;"],[0,"&qfr;"],[0,"&rfr;"],[0,"&sfr;"],[0,"&tfr;"],[0,"&ufr;"],[0,"&vfr;"],[0,"&wfr;"],[0,"&xfr;"],[0,"&yfr;"],[0,"&zfr;"],[0,"&Aopf;"],[0,"&Bopf;"],[1,"&Dopf;"],[0,"&Eopf;"],[0,"&Fopf;"],[0,"&Gopf;"],[1,"&Iopf;"],[0,"&Jopf;"],[0,"&Kopf;"],[0,"&Lopf;"],[0,"&Mopf;"],[1,"&Oopf;"],[3,"&Sopf;"],[0,"&Topf;"],[0,"&Uopf;"],[0,"&Vopf;"],[0,"&Wopf;"],[0,"&Xopf;"],[0,"&Yopf;"],[1,"&aopf;"],[0,"&bopf;"],[0,"&copf;"],[0,"&dopf;"],[0,"&eopf;"],[0,"&fopf;"],[0,"&gopf;"],[0,"&hopf;"],[0,"&iopf;"],[0,"&jopf;"],[0,"&kopf;"],[0,"&lopf;"],[0,"&mopf;"],[0,"&nopf;"],[0,"&oopf;"],[0,"&popf;"],[0,"&qopf;"],[0,"&ropf;"],[0,"&sopf;"],[0,"&topf;"],[0,"&uopf;"],[0,"&vopf;"],[0,"&wopf;"],[0,"&xopf;"],[0,"&yopf;"],[0,"&zopf;"]]))}],[8906,"&fflig;"],[0,"&filig;"],[0,"&fllig;"],[0,"&ffilig;"],[0,"&ffllig;"]]));var e0=/["&'<>$\x80-\uFFFF]/g,pa=new Map([[34,"&quot;"],[38,"&amp;"],[39,"&apos;"],[60,"&lt;"],[62,"&gt;"]]),Ta=String.prototype.codePointAt!=null?(u,e)=>u.codePointAt(e):(u,e)=>(u.charCodeAt(e)&64512)===55296?(u.charCodeAt(e)-55296)*1024+u.charCodeAt(e+1)-56320+65536:u.charCodeAt(e);function fe(u){let e="",t=0,a;for(;(a=e0.exec(u))!==null;){let s=a.index,i=u.charCodeAt(s),n=pa.get(i);n!==void 0?(e+=u.substring(t,s)+n,t=s+1):(e+=`${u.substring(t,s)}&#x${Ta(u,s).toString(16)};`,t=e0.lastIndex+=Number((i&64512)===55296))}return e+u.substr(t)}function t0(u,e){return function(a){let s,i=0,n="";for(;s=u.exec(a);)i!==s.index&&(n+=a.substring(i,s.index)),n+=e.get(s[0].charCodeAt(0)),i=s.index+1;return n+a.substring(i)}}var ga=t0(/[&<>'"]/g,pa),We=t0(/["&\u00A0]/g,new Map([[34,"&quot;"],[38,"&amp;"],[160,"&nbsp;"]])),Qe=t0(/[&<>\u00A0]/g,new Map([[38,"&amp;"],[60,"&lt;"],[62,"&gt;"],[160,"&nbsp;"]]));var xa;(function(u){u[u.XML=0]="XML",u[u.HTML=1]="HTML"})(xa||(xa={}));var Aa;(function(u){u[u.UTF8=0]="UTF8",u[u.ASCII=1]="ASCII",u[u.Extensive=2]="Extensive",u[u.Attribute=3]="Attribute",u[u.Text=4]="Text"})(Aa||(Aa={}));var Sa=new Map(["altGlyph","altGlyphDef","altGlyphItem","animateColor","animateMotion","animateTransform","clipPath","feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","foreignObject","glyphRef","linearGradient","radialGradient","textPath"].map(u=>[u.toLowerCase(),u])),Ia=new Map(["definitionURL","attributeName","attributeType","baseFrequency","baseProfile","calcMode","clipPathUnits","diffuseConstant","edgeMode","filterUnits","glyphRef","gradientTransform","gradientUnits","kernelMatrix","kernelUnitLength","keyPoints","keySplines","keyTimes","lengthAdjust","limitingConeAngle","markerHeight","markerUnits","markerWidth","maskContentUnits","maskUnits","numOctaves","pathLength","patternContentUnits","patternTransform","patternUnits","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","refX","refY","repeatCount","repeatDur","requiredExtensions","requiredFeatures","specularConstant","specularExponent","spreadMethod","startOffset","stdDeviation","stitchTiles","surfaceScale","systemLanguage","tableValues","targetX","targetY","textLength","viewBox","viewTarget","xChannelSelector","yChannelSelector","zoomAndPan"].map(u=>[u.toLowerCase(),u]));var Us=new Set(["style","script","xmp","iframe","noembed","noframes","plaintext","noscript"]);function Hs(u){return u.replace(/"/g,"&quot;")}function Fs(u,e){var t;if(!u)return;let a=((t=e.encodeEntities)!==null&&t!==void 0?t:e.decodeEntities)===!1?Hs:e.xmlMode||e.encodeEntities!=="utf8"?fe:We;return Object.keys(u).map(s=>{var i,n;let d=(i=u[s])!==null&&i!==void 0?i:"";return e.xmlMode==="foreign"&&(s=(n=Ia.get(s))!==null&&n!==void 0?n:s),!e.emptyAttrs&&!e.xmlMode&&d===""?s:`${s}="${a(d)}"`}).join(" ")}var Ca=new Set(["area","base","basefont","br","col","command","embed","frame","hr","img","input","isindex","keygen","link","meta","param","source","track","wbr"]);function s0(u,e={}){let t="length"in u?u:[u],a="";for(let s=0;s<t.length;s++)a+=qs(t[s],e);return a}var Xe=s0;function qs(u,e){switch(u.type){case qt:return s0(u.children,e);case jt:case Vt:return Ws(u);case Gt:return Ks(u);case Kt:return Xs(u);case Wt:case Qt:case Xt:return Gs(u,e);case Yt:return Qs(u,e)}}var Ys=new Set(["mi","mo","mn","ms","mtext","annotation-xml","foreignObject","desc","title"]),Vs=new Set(["svg","math"]);function Gs(u,e){var t;e.xmlMode==="foreign"&&(u.name=(t=Sa.get(u.name))!==null&&t!==void 0?t:u.name,u.parent&&Ys.has(u.parent.name)&&(e={...e,xmlMode:!1})),!e.xmlMode&&Vs.has(u.name)&&(e={...e,xmlMode:"foreign"});let a=`<${u.name}`,s=Fs(u.attribs,e);return s&&(a+=` ${s}`),u.children.length===0&&(e.xmlMode?e.selfClosingTags!==!1:e.selfClosingTags&&Ca.has(u.name))?(e.xmlMode||(a+=" "),a+="/>"):(a+=">",u.children.length>0&&(a+=s0(u.children,e)),(e.xmlMode||!Ca.has(u.name))&&(a+=`</${u.name}>`)),a}function Ws(u){return`<${u.data}>`}function Qs(u,e){var t;let a=u.data||"";return((t=e.encodeEntities)!==null&&t!==void 0?t:e.decodeEntities)!==!1&&!(!e.xmlMode&&u.parent&&Us.has(u.parent.name))&&(a=e.xmlMode||e.encodeEntities!=="utf8"?fe(a):Qe(a)),a}function Xs(u){return`<![CDATA[${u.children[0].data}]]>`}function Ks(u){return`<!--${u.data}-->`}function La(u,e){return Xe(u,e)}function js(u,e){return R(u)?u.children.map(t=>La(t,e)).join(""):""}function Ke(u){return Array.isArray(u)?u.map(Ke).join(""):N(u)?u.name==="br"?`
`:Ke(u.children):Wu(u)?Ke(u.children):K(u)?u.data:""}function bu(u){return Array.isArray(u)?u.map(bu).join(""):R(u)&&!Uu(u)?bu(u.children):K(u)?u.data:""}function le(u){return Array.isArray(u)?u.map(le).join(""):R(u)&&(u.type===O.Tag||Wu(u))?le(u.children):K(u)?u.data:""}function Ku(u){return R(u)?u.children:[]}function Da(u){return u.parent||null}function i0(u){let e=Da(u);if(e!=null)return Ku(e);let t=[u],{prev:a,next:s}=u;for(;a!=null;)t.unshift(a),{prev:a}=a;for(;s!=null;)t.push(s),{next:s}=s;return t}function zs(u,e){var t;return(t=u.attribs)===null||t===void 0?void 0:t[e]}function $s(u,e){return u.attribs!=null&&Object.prototype.hasOwnProperty.call(u.attribs,e)&&u.attribs[e]!=null}function Zs(u){return u.name}function je(u){let{next:e}=u;for(;e!==null&&!N(e);)({next:e}=e);return e}function ze(u){let{prev:e}=u;for(;e!==null&&!N(e);)({prev:e}=e);return e}function hu(u){if(u.prev&&(u.prev.next=u.next),u.next&&(u.next.prev=u.prev),u.parent){let e=u.parent.children,t=e.lastIndexOf(u);t>=0&&e.splice(t,1)}u.next=null,u.prev=null,u.parent=null}function Js(u,e){let t=e.prev=u.prev;t&&(t.next=e);let a=e.next=u.next;a&&(a.prev=e);let s=e.parent=u.parent;if(s){let i=s.children;i[i.lastIndexOf(u)]=e,u.parent=null}}function ui(u,e){if(hu(e),e.next=null,e.parent=u,u.children.push(e)>1){let t=u.children[u.children.length-2];t.next=e,e.prev=t}else e.prev=null}function ei(u,e){hu(e);let{parent:t}=u,a=u.next;if(e.next=a,e.prev=u,u.next=e,e.parent=t,a){if(a.prev=e,t){let s=t.children;s.splice(s.lastIndexOf(a),0,e)}}else t&&t.children.push(e)}function ti(u,e){if(hu(e),e.parent=u,e.prev=null,u.children.unshift(e)!==1){let t=u.children[1];t.prev=e,e.next=t}else e.next=null}function ai(u,e){hu(e);let{parent:t}=u;if(t){let a=t.children;a.splice(a.indexOf(u),0,e)}u.prev&&(u.prev.next=e),e.parent=t,e.prev=u.prev,e.next=u,u.prev=e}function ju(u,e,t=!0,a=1/0){return $e(u,Array.isArray(e)?e:[e],t,a)}function $e(u,e,t,a){let s=[],i=[Array.isArray(e)?e:[e]],n=[0];for(;;){if(n[0]>=i[0].length){if(n.length===1)return s;i.shift(),n.shift();continue}let d=i[0][n[0]++];if(u(d)&&(s.push(d),--a<=0))return s;t&&R(d)&&d.children.length>0&&(n.unshift(0),i.unshift(d.children))}}function ri(u,e){return e.find(u)}function Ze(u,e,t=!0){let a=Array.isArray(e)?e:[e];for(let s=0;s<a.length;s++){let i=a[s];if(N(i)&&u(i))return i;if(t&&R(i)&&i.children.length>0){let n=Ze(u,i.children,!0);if(n)return n}}return null}function Oa(u,e){return(Array.isArray(e)?e:[e]).some(t=>N(t)&&u(t)||R(t)&&Oa(u,t.children))}function si(u,e){let t=[],a=[Array.isArray(e)?e:[e]],s=[0];for(;;){if(s[0]>=a[0].length){if(a.length===1)return t;a.shift(),s.shift();continue}let i=a[0][s[0]++];N(i)&&u(i)&&t.push(i),R(i)&&i.children.length>0&&(s.unshift(0),a.unshift(i.children))}}var Je={tag_name(u){return typeof u=="function"?e=>N(e)&&u(e.name):u==="*"?N:e=>N(e)&&e.name===u},tag_type(u){return typeof u=="function"?e=>u(e.type):e=>e.type===u},tag_contains(u){return typeof u=="function"?e=>K(e)&&u(e.data):e=>K(e)&&e.data===u}};function n0(u,e){return typeof e=="function"?t=>N(t)&&e(t.attribs[u]):t=>N(t)&&t.attribs[u]===e}function ii(u,e){return t=>u(t)||e(t)}function Ra(u){let e=Object.keys(u).map(t=>{let a=u[t];return Object.prototype.hasOwnProperty.call(Je,t)?Je[t](a):n0(t,a)});return e.length===0?null:e.reduce(ii)}function ni(u,e){let t=Ra(u);return t?t(e):!0}function ci(u,e,t,a=1/0){let s=Ra(u);return s?ju(s,e,t,a):[]}function oi(u,e,t=!0){return Array.isArray(e)||(e=[e]),Ze(n0("id",u),e,t)}function Hu(u,e,t=!0,a=1/0){return ju(Je.tag_name(u),e,t,a)}function di(u,e,t=!0,a=1/0){return ju(n0("class",u),e,t,a)}function fi(u,e,t=!0,a=1/0){return ju(Je.tag_type(u),e,t,a)}function li(u){let e=u.length;for(;--e>=0;){let t=u[e];if(e>0&&u.lastIndexOf(t,e-1)>=0){u.splice(e,1);continue}for(let a=t.parent;a;a=a.parent)if(u.includes(a)){u.splice(e,1);break}}return u}var eu;(function(u){u[u.DISCONNECTED=1]="DISCONNECTED",u[u.PRECEDING=2]="PRECEDING",u[u.FOLLOWING=4]="FOLLOWING",u[u.CONTAINS=8]="CONTAINS",u[u.CONTAINED_BY=16]="CONTAINED_BY"})(eu||(eu={}));function ya(u,e){let t=[],a=[];if(u===e)return 0;let s=R(u)?u:u.parent;for(;s;)t.unshift(s),s=s.parent;for(s=R(e)?e:e.parent;s;)a.unshift(s),s=s.parent;let i=Math.min(t.length,a.length),n=0;for(;n<i&&t[n]===a[n];)n++;if(n===0)return eu.DISCONNECTED;let d=t[n-1],l=d.children,h=t[n],p=a[n];return l.indexOf(h)>l.indexOf(p)?d===e?eu.FOLLOWING|eu.CONTAINED_BY:eu.FOLLOWING:d===u?eu.PRECEDING|eu.CONTAINS:eu.PRECEDING}function Nu(u){return u=u.filter((e,t,a)=>!a.includes(e,t+1)),u.sort((e,t)=>{let a=ya(e,t);return a&eu.PRECEDING?-1:a&eu.FOLLOWING?1:0}),u}function c0(u){let e=ut(pi,u);return e?e.name==="feed"?bi(e):hi(e):null}function bi(u){var e;let t=u.children,a={type:"atom",items:Hu("entry",t).map(n=>{var d;let{children:l}=n,h={media:Pa(l)};j(h,"id","id",l),j(h,"title","title",l);let p=(d=ut("link",l))===null||d===void 0?void 0:d.attribs.href;p&&(h.link=p);let x=Su("summary",l)||Su("content",l);x&&(h.description=x);let A=Su("updated",l);return A&&(h.pubDate=new Date(A)),h})};j(a,"id","id",t),j(a,"title","title",t);let s=(e=ut("link",t))===null||e===void 0?void 0:e.attribs.href;s&&(a.link=s),j(a,"description","subtitle",t);let i=Su("updated",t);return i&&(a.updated=new Date(i)),j(a,"author","email",t,!0),a}function hi(u){var e,t;let a=(t=(e=ut("channel",u.children))===null||e===void 0?void 0:e.children)!==null&&t!==void 0?t:[],s={type:u.name.substr(0,3),id:"",items:Hu("item",u.children).map(n=>{let{children:d}=n,l={media:Pa(d)};j(l,"id","guid",d),j(l,"title","title",d),j(l,"link","link",d),j(l,"description","description",d);let h=Su("pubDate",d)||Su("dc:date",d);return h&&(l.pubDate=new Date(h)),l})};j(s,"title","title",a),j(s,"link","link",a),j(s,"description","description",a);let i=Su("lastBuildDate",a);return i&&(s.updated=new Date(i)),j(s,"author","managingEditor",a,!0),s}var mi=["url","type","lang"],Ei=["fileSize","bitrate","framerate","samplingrate","channels","duration","height","width"];function Pa(u){return Hu("media:content",u).map(e=>{let{attribs:t}=e,a={medium:t.medium,isDefault:!!t.isDefault};for(let s of mi)t[s]&&(a[s]=t[s]);for(let s of Ei)t[s]&&(a[s]=parseInt(t[s],10));return t.expression&&(a.expression=t.expression),a})}function ut(u,e){return Hu(u,e,!0,1)[0]}function Su(u,e,t=!1){return bu(Hu(u,e,t,1)).trim()}function j(u,e,t,a,s=!1){let i=Su(t,a,s);i&&(u[e]=i)}function pi(u){return u==="rss"||u==="feed"||u==="rdf:RDF"}var Ti={_useHtmlParser2:!1};function be(u,e){if(!u)return e!=null?e:Ti;let t={_useHtmlParser2:!!u.xmlMode,...e,...u};return u.xml?(t._useHtmlParser2=!0,t.xmlMode=!0,u.xml!==!0&&Object.assign(t,u.xml)):u.xmlMode&&(t._useHtmlParser2=!0),t}function ka(u,e,t){return u?u(e!=null?e:u._root.children,null,void 0,t).toString():""}function gi(u,e){return!e&&typeof u=="object"&&u!=null&&!("length"in u)&&!("type"in u)}function xi(u,e){let t=gi(u)?(e=u,void 0):u,a={...this===null||this===void 0?void 0:this._options,...be(e)};return ka(this,t,a)}function Ai(u){let e={...this._options,xmlMode:!0};return ka(this,u,e)}function Fu(u){let e=u!=null?u:this?this.root():[],t="";for(let a=0;a<e.length;a++)t+=bu(e[a]);return t}function _i(u,e,t=typeof e=="boolean"?e:!1){if(!u||typeof u!="string")return null;typeof e=="boolean"&&(t=e);let a=this.load(u,this._options,!1);return t||a("script").remove(),[...a.root()[0].children]}function Ni(){return this(this._root)}function et(u,e){if(e===u)return!1;let t=e;for(;t&&t!==t.parent;)if(t=t.parent,t===u)return!0;return!1}function Si(u){return this.root().extract(u)}function wa(u,e){if(!Ma(u)||!Ma(e))return;let t=u.length,a=+e.length;for(let s=0;s<a;s++)u[t++]=e[s];return u.length=t,u}function Ma(u){if(Array.isArray(u))return!0;if(typeof u!="object"||u===null||!("length"in u)||typeof u.length!="number"||u.length<0)return!1;for(let e=0;e<u.length;e++)if(!(e in u))return!1;return!0}var p0={};$(p0,{addClass:()=>Ka,attr:()=>Bi,data:()=>Fi,hasClass:()=>Vi,prop:()=>vi,removeAttr:()=>Yi,removeClass:()=>ja,toggleClass:()=>za,val:()=>qi});function tu(u){return u.cheerio!=null}function Ba(u){return u.replace(/[._-](\w|$)/g,(e,t)=>t.toUpperCase())}function va(u){return u.replace(/[A-Z]/g,"-$&").toLowerCase()}function P(u,e){let t=u.length;for(let a=0;a<t;a++)e(u[a],a);return u}var qu;(function(u){u[u.LowerA=97]="LowerA",u[u.LowerZ=122]="LowerZ",u[u.UpperA=65]="UpperA",u[u.UpperZ=90]="UpperZ",u[u.Exclamation=33]="Exclamation"})(qu||(qu={}));function he(u){let e=u.indexOf("<");if(e===-1||e>u.length-3)return!1;let t=u.charCodeAt(e+1);return(t>=qu.LowerA&&t<=qu.LowerZ||t>=qu.UpperA&&t<=qu.UpperZ||t===qu.Exclamation)&&u.includes(">",e+2)}var tt=new Uint16Array('\u1D41<\xD5\u0131\u028A\u049D\u057B\u05D0\u0675\u06DE\u07A2\u07D6\u080F\u0A4A\u0A91\u0DA1\u0E6D\u0F09\u0F26\u10CA\u1228\u12E1\u1415\u149D\u14C3\u14DF\u1525\0\0\0\0\0\0\u156B\u16CD\u198D\u1C12\u1DDD\u1F7E\u2060\u21B0\u228D\u23C0\u23FB\u2442\u2824\u2912\u2D08\u2E48\u2FCE\u3016\u32BA\u3639\u37AC\u38FE\u3A28\u3A71\u3AE0\u3B2E\u0800EMabcfglmnoprstu\\bfms\x7F\x84\x8B\x90\x95\x98\xA6\xB3\xB9\xC8\xCFlig\u803B\xC6\u40C6P\u803B&\u4026cute\u803B\xC1\u40C1reve;\u4102\u0100iyx}rc\u803B\xC2\u40C2;\u4410r;\uC000\u{1D504}rave\u803B\xC0\u40C0pha;\u4391acr;\u4100d;\u6A53\u0100gp\x9D\xA1on;\u4104f;\uC000\u{1D538}plyFunction;\u6061ing\u803B\xC5\u40C5\u0100cs\xBE\xC3r;\uC000\u{1D49C}ign;\u6254ilde\u803B\xC3\u40C3ml\u803B\xC4\u40C4\u0400aceforsu\xE5\xFB\xFE\u0117\u011C\u0122\u0127\u012A\u0100cr\xEA\xF2kslash;\u6216\u0176\xF6\xF8;\u6AE7ed;\u6306y;\u4411\u0180crt\u0105\u010B\u0114ause;\u6235noullis;\u612Ca;\u4392r;\uC000\u{1D505}pf;\uC000\u{1D539}eve;\u42D8c\xF2\u0113mpeq;\u624E\u0700HOacdefhilorsu\u014D\u0151\u0156\u0180\u019E\u01A2\u01B5\u01B7\u01BA\u01DC\u0215\u0273\u0278\u027Ecy;\u4427PY\u803B\xA9\u40A9\u0180cpy\u015D\u0162\u017Aute;\u4106\u0100;i\u0167\u0168\u62D2talDifferentialD;\u6145leys;\u612D\u0200aeio\u0189\u018E\u0194\u0198ron;\u410Cdil\u803B\xC7\u40C7rc;\u4108nint;\u6230ot;\u410A\u0100dn\u01A7\u01ADilla;\u40B8terDot;\u40B7\xF2\u017Fi;\u43A7rcle\u0200DMPT\u01C7\u01CB\u01D1\u01D6ot;\u6299inus;\u6296lus;\u6295imes;\u6297o\u0100cs\u01E2\u01F8kwiseContourIntegral;\u6232eCurly\u0100DQ\u0203\u020FoubleQuote;\u601Duote;\u6019\u0200lnpu\u021E\u0228\u0247\u0255on\u0100;e\u0225\u0226\u6237;\u6A74\u0180git\u022F\u0236\u023Aruent;\u6261nt;\u622FourIntegral;\u622E\u0100fr\u024C\u024E;\u6102oduct;\u6210nterClockwiseContourIntegral;\u6233oss;\u6A2Fcr;\uC000\u{1D49E}p\u0100;C\u0284\u0285\u62D3ap;\u624D\u0580DJSZacefios\u02A0\u02AC\u02B0\u02B4\u02B8\u02CB\u02D7\u02E1\u02E6\u0333\u048D\u0100;o\u0179\u02A5trahd;\u6911cy;\u4402cy;\u4405cy;\u440F\u0180grs\u02BF\u02C4\u02C7ger;\u6021r;\u61A1hv;\u6AE4\u0100ay\u02D0\u02D5ron;\u410E;\u4414l\u0100;t\u02DD\u02DE\u6207a;\u4394r;\uC000\u{1D507}\u0100af\u02EB\u0327\u0100cm\u02F0\u0322ritical\u0200ADGT\u0300\u0306\u0316\u031Ccute;\u40B4o\u0174\u030B\u030D;\u42D9bleAcute;\u42DDrave;\u4060ilde;\u42DCond;\u62C4ferentialD;\u6146\u0470\u033D\0\0\0\u0342\u0354\0\u0405f;\uC000\u{1D53B}\u0180;DE\u0348\u0349\u034D\u40A8ot;\u60DCqual;\u6250ble\u0300CDLRUV\u0363\u0372\u0382\u03CF\u03E2\u03F8ontourIntegra\xEC\u0239o\u0274\u0379\0\0\u037B\xBB\u0349nArrow;\u61D3\u0100eo\u0387\u03A4ft\u0180ART\u0390\u0396\u03A1rrow;\u61D0ightArrow;\u61D4e\xE5\u02CAng\u0100LR\u03AB\u03C4eft\u0100AR\u03B3\u03B9rrow;\u67F8ightArrow;\u67FAightArrow;\u67F9ight\u0100AT\u03D8\u03DErrow;\u61D2ee;\u62A8p\u0241\u03E9\0\0\u03EFrrow;\u61D1ownArrow;\u61D5erticalBar;\u6225n\u0300ABLRTa\u0412\u042A\u0430\u045E\u047F\u037Crrow\u0180;BU\u041D\u041E\u0422\u6193ar;\u6913pArrow;\u61F5reve;\u4311eft\u02D2\u043A\0\u0446\0\u0450ightVector;\u6950eeVector;\u695Eector\u0100;B\u0459\u045A\u61BDar;\u6956ight\u01D4\u0467\0\u0471eeVector;\u695Fector\u0100;B\u047A\u047B\u61C1ar;\u6957ee\u0100;A\u0486\u0487\u62A4rrow;\u61A7\u0100ct\u0492\u0497r;\uC000\u{1D49F}rok;\u4110\u0800NTacdfglmopqstux\u04BD\u04C0\u04C4\u04CB\u04DE\u04E2\u04E7\u04EE\u04F5\u0521\u052F\u0536\u0552\u055D\u0560\u0565G;\u414AH\u803B\xD0\u40D0cute\u803B\xC9\u40C9\u0180aiy\u04D2\u04D7\u04DCron;\u411Arc\u803B\xCA\u40CA;\u442Dot;\u4116r;\uC000\u{1D508}rave\u803B\xC8\u40C8ement;\u6208\u0100ap\u04FA\u04FEcr;\u4112ty\u0253\u0506\0\0\u0512mallSquare;\u65FBerySmallSquare;\u65AB\u0100gp\u0526\u052Aon;\u4118f;\uC000\u{1D53C}silon;\u4395u\u0100ai\u053C\u0549l\u0100;T\u0542\u0543\u6A75ilde;\u6242librium;\u61CC\u0100ci\u0557\u055Ar;\u6130m;\u6A73a;\u4397ml\u803B\xCB\u40CB\u0100ip\u056A\u056Fsts;\u6203onentialE;\u6147\u0280cfios\u0585\u0588\u058D\u05B2\u05CCy;\u4424r;\uC000\u{1D509}lled\u0253\u0597\0\0\u05A3mallSquare;\u65FCerySmallSquare;\u65AA\u0370\u05BA\0\u05BF\0\0\u05C4f;\uC000\u{1D53D}All;\u6200riertrf;\u6131c\xF2\u05CB\u0600JTabcdfgorst\u05E8\u05EC\u05EF\u05FA\u0600\u0612\u0616\u061B\u061D\u0623\u066C\u0672cy;\u4403\u803B>\u403Emma\u0100;d\u05F7\u05F8\u4393;\u43DCreve;\u411E\u0180eiy\u0607\u060C\u0610dil;\u4122rc;\u411C;\u4413ot;\u4120r;\uC000\u{1D50A};\u62D9pf;\uC000\u{1D53E}eater\u0300EFGLST\u0635\u0644\u064E\u0656\u065B\u0666qual\u0100;L\u063E\u063F\u6265ess;\u62DBullEqual;\u6267reater;\u6AA2ess;\u6277lantEqual;\u6A7Eilde;\u6273cr;\uC000\u{1D4A2};\u626B\u0400Aacfiosu\u0685\u068B\u0696\u069B\u069E\u06AA\u06BE\u06CARDcy;\u442A\u0100ct\u0690\u0694ek;\u42C7;\u405Eirc;\u4124r;\u610ClbertSpace;\u610B\u01F0\u06AF\0\u06B2f;\u610DizontalLine;\u6500\u0100ct\u06C3\u06C5\xF2\u06A9rok;\u4126mp\u0144\u06D0\u06D8ownHum\xF0\u012Fqual;\u624F\u0700EJOacdfgmnostu\u06FA\u06FE\u0703\u0707\u070E\u071A\u071E\u0721\u0728\u0744\u0778\u078B\u078F\u0795cy;\u4415lig;\u4132cy;\u4401cute\u803B\xCD\u40CD\u0100iy\u0713\u0718rc\u803B\xCE\u40CE;\u4418ot;\u4130r;\u6111rave\u803B\xCC\u40CC\u0180;ap\u0720\u072F\u073F\u0100cg\u0734\u0737r;\u412AinaryI;\u6148lie\xF3\u03DD\u01F4\u0749\0\u0762\u0100;e\u074D\u074E\u622C\u0100gr\u0753\u0758ral;\u622Bsection;\u62C2isible\u0100CT\u076C\u0772omma;\u6063imes;\u6062\u0180gpt\u077F\u0783\u0788on;\u412Ef;\uC000\u{1D540}a;\u4399cr;\u6110ilde;\u4128\u01EB\u079A\0\u079Ecy;\u4406l\u803B\xCF\u40CF\u0280cfosu\u07AC\u07B7\u07BC\u07C2\u07D0\u0100iy\u07B1\u07B5rc;\u4134;\u4419r;\uC000\u{1D50D}pf;\uC000\u{1D541}\u01E3\u07C7\0\u07CCr;\uC000\u{1D4A5}rcy;\u4408kcy;\u4404\u0380HJacfos\u07E4\u07E8\u07EC\u07F1\u07FD\u0802\u0808cy;\u4425cy;\u440Cppa;\u439A\u0100ey\u07F6\u07FBdil;\u4136;\u441Ar;\uC000\u{1D50E}pf;\uC000\u{1D542}cr;\uC000\u{1D4A6}\u0580JTaceflmost\u0825\u0829\u082C\u0850\u0863\u09B3\u09B8\u09C7\u09CD\u0A37\u0A47cy;\u4409\u803B<\u403C\u0280cmnpr\u0837\u083C\u0841\u0844\u084Dute;\u4139bda;\u439Bg;\u67EAlacetrf;\u6112r;\u619E\u0180aey\u0857\u085C\u0861ron;\u413Ddil;\u413B;\u441B\u0100fs\u0868\u0970t\u0500ACDFRTUVar\u087E\u08A9\u08B1\u08E0\u08E6\u08FC\u092F\u095B\u0390\u096A\u0100nr\u0883\u088FgleBracket;\u67E8row\u0180;BR\u0899\u089A\u089E\u6190ar;\u61E4ightArrow;\u61C6eiling;\u6308o\u01F5\u08B7\0\u08C3bleBracket;\u67E6n\u01D4\u08C8\0\u08D2eeVector;\u6961ector\u0100;B\u08DB\u08DC\u61C3ar;\u6959loor;\u630Aight\u0100AV\u08EF\u08F5rrow;\u6194ector;\u694E\u0100er\u0901\u0917e\u0180;AV\u0909\u090A\u0910\u62A3rrow;\u61A4ector;\u695Aiangle\u0180;BE\u0924\u0925\u0929\u62B2ar;\u69CFqual;\u62B4p\u0180DTV\u0937\u0942\u094CownVector;\u6951eeVector;\u6960ector\u0100;B\u0956\u0957\u61BFar;\u6958ector\u0100;B\u0965\u0966\u61BCar;\u6952ight\xE1\u039Cs\u0300EFGLST\u097E\u098B\u0995\u099D\u09A2\u09ADqualGreater;\u62DAullEqual;\u6266reater;\u6276ess;\u6AA1lantEqual;\u6A7Dilde;\u6272r;\uC000\u{1D50F}\u0100;e\u09BD\u09BE\u62D8ftarrow;\u61DAidot;\u413F\u0180npw\u09D4\u0A16\u0A1Bg\u0200LRlr\u09DE\u09F7\u0A02\u0A10eft\u0100AR\u09E6\u09ECrrow;\u67F5ightArrow;\u67F7ightArrow;\u67F6eft\u0100ar\u03B3\u0A0Aight\xE1\u03BFight\xE1\u03CAf;\uC000\u{1D543}er\u0100LR\u0A22\u0A2CeftArrow;\u6199ightArrow;\u6198\u0180cht\u0A3E\u0A40\u0A42\xF2\u084C;\u61B0rok;\u4141;\u626A\u0400acefiosu\u0A5A\u0A5D\u0A60\u0A77\u0A7C\u0A85\u0A8B\u0A8Ep;\u6905y;\u441C\u0100dl\u0A65\u0A6FiumSpace;\u605Flintrf;\u6133r;\uC000\u{1D510}nusPlus;\u6213pf;\uC000\u{1D544}c\xF2\u0A76;\u439C\u0480Jacefostu\u0AA3\u0AA7\u0AAD\u0AC0\u0B14\u0B19\u0D91\u0D97\u0D9Ecy;\u440Acute;\u4143\u0180aey\u0AB4\u0AB9\u0ABEron;\u4147dil;\u4145;\u441D\u0180gsw\u0AC7\u0AF0\u0B0Eative\u0180MTV\u0AD3\u0ADF\u0AE8ediumSpace;\u600Bhi\u0100cn\u0AE6\u0AD8\xEB\u0AD9eryThi\xEE\u0AD9ted\u0100GL\u0AF8\u0B06reaterGreate\xF2\u0673essLes\xF3\u0A48Line;\u400Ar;\uC000\u{1D511}\u0200Bnpt\u0B22\u0B28\u0B37\u0B3Areak;\u6060BreakingSpace;\u40A0f;\u6115\u0680;CDEGHLNPRSTV\u0B55\u0B56\u0B6A\u0B7C\u0BA1\u0BEB\u0C04\u0C5E\u0C84\u0CA6\u0CD8\u0D61\u0D85\u6AEC\u0100ou\u0B5B\u0B64ngruent;\u6262pCap;\u626DoubleVerticalBar;\u6226\u0180lqx\u0B83\u0B8A\u0B9Bement;\u6209ual\u0100;T\u0B92\u0B93\u6260ilde;\uC000\u2242\u0338ists;\u6204reater\u0380;EFGLST\u0BB6\u0BB7\u0BBD\u0BC9\u0BD3\u0BD8\u0BE5\u626Fqual;\u6271ullEqual;\uC000\u2267\u0338reater;\uC000\u226B\u0338ess;\u6279lantEqual;\uC000\u2A7E\u0338ilde;\u6275ump\u0144\u0BF2\u0BFDownHump;\uC000\u224E\u0338qual;\uC000\u224F\u0338e\u0100fs\u0C0A\u0C27tTriangle\u0180;BE\u0C1A\u0C1B\u0C21\u62EAar;\uC000\u29CF\u0338qual;\u62ECs\u0300;EGLST\u0C35\u0C36\u0C3C\u0C44\u0C4B\u0C58\u626Equal;\u6270reater;\u6278ess;\uC000\u226A\u0338lantEqual;\uC000\u2A7D\u0338ilde;\u6274ested\u0100GL\u0C68\u0C79reaterGreater;\uC000\u2AA2\u0338essLess;\uC000\u2AA1\u0338recedes\u0180;ES\u0C92\u0C93\u0C9B\u6280qual;\uC000\u2AAF\u0338lantEqual;\u62E0\u0100ei\u0CAB\u0CB9verseElement;\u620CghtTriangle\u0180;BE\u0CCB\u0CCC\u0CD2\u62EBar;\uC000\u29D0\u0338qual;\u62ED\u0100qu\u0CDD\u0D0CuareSu\u0100bp\u0CE8\u0CF9set\u0100;E\u0CF0\u0CF3\uC000\u228F\u0338qual;\u62E2erset\u0100;E\u0D03\u0D06\uC000\u2290\u0338qual;\u62E3\u0180bcp\u0D13\u0D24\u0D4Eset\u0100;E\u0D1B\u0D1E\uC000\u2282\u20D2qual;\u6288ceeds\u0200;EST\u0D32\u0D33\u0D3B\u0D46\u6281qual;\uC000\u2AB0\u0338lantEqual;\u62E1ilde;\uC000\u227F\u0338erset\u0100;E\u0D58\u0D5B\uC000\u2283\u20D2qual;\u6289ilde\u0200;EFT\u0D6E\u0D6F\u0D75\u0D7F\u6241qual;\u6244ullEqual;\u6247ilde;\u6249erticalBar;\u6224cr;\uC000\u{1D4A9}ilde\u803B\xD1\u40D1;\u439D\u0700Eacdfgmoprstuv\u0DBD\u0DC2\u0DC9\u0DD5\u0DDB\u0DE0\u0DE7\u0DFC\u0E02\u0E20\u0E22\u0E32\u0E3F\u0E44lig;\u4152cute\u803B\xD3\u40D3\u0100iy\u0DCE\u0DD3rc\u803B\xD4\u40D4;\u441Eblac;\u4150r;\uC000\u{1D512}rave\u803B\xD2\u40D2\u0180aei\u0DEE\u0DF2\u0DF6cr;\u414Cga;\u43A9cron;\u439Fpf;\uC000\u{1D546}enCurly\u0100DQ\u0E0E\u0E1AoubleQuote;\u601Cuote;\u6018;\u6A54\u0100cl\u0E27\u0E2Cr;\uC000\u{1D4AA}ash\u803B\xD8\u40D8i\u016C\u0E37\u0E3Cde\u803B\xD5\u40D5es;\u6A37ml\u803B\xD6\u40D6er\u0100BP\u0E4B\u0E60\u0100ar\u0E50\u0E53r;\u603Eac\u0100ek\u0E5A\u0E5C;\u63DEet;\u63B4arenthesis;\u63DC\u0480acfhilors\u0E7F\u0E87\u0E8A\u0E8F\u0E92\u0E94\u0E9D\u0EB0\u0EFCrtialD;\u6202y;\u441Fr;\uC000\u{1D513}i;\u43A6;\u43A0usMinus;\u40B1\u0100ip\u0EA2\u0EADncareplan\xE5\u069Df;\u6119\u0200;eio\u0EB9\u0EBA\u0EE0\u0EE4\u6ABBcedes\u0200;EST\u0EC8\u0EC9\u0ECF\u0EDA\u627Aqual;\u6AAFlantEqual;\u627Cilde;\u627Eme;\u6033\u0100dp\u0EE9\u0EEEuct;\u620Fortion\u0100;a\u0225\u0EF9l;\u621D\u0100ci\u0F01\u0F06r;\uC000\u{1D4AB};\u43A8\u0200Ufos\u0F11\u0F16\u0F1B\u0F1FOT\u803B"\u4022r;\uC000\u{1D514}pf;\u611Acr;\uC000\u{1D4AC}\u0600BEacefhiorsu\u0F3E\u0F43\u0F47\u0F60\u0F73\u0FA7\u0FAA\u0FAD\u1096\u10A9\u10B4\u10BEarr;\u6910G\u803B\xAE\u40AE\u0180cnr\u0F4E\u0F53\u0F56ute;\u4154g;\u67EBr\u0100;t\u0F5C\u0F5D\u61A0l;\u6916\u0180aey\u0F67\u0F6C\u0F71ron;\u4158dil;\u4156;\u4420\u0100;v\u0F78\u0F79\u611Cerse\u0100EU\u0F82\u0F99\u0100lq\u0F87\u0F8Eement;\u620Builibrium;\u61CBpEquilibrium;\u696Fr\xBB\u0F79o;\u43A1ght\u0400ACDFTUVa\u0FC1\u0FEB\u0FF3\u1022\u1028\u105B\u1087\u03D8\u0100nr\u0FC6\u0FD2gleBracket;\u67E9row\u0180;BL\u0FDC\u0FDD\u0FE1\u6192ar;\u61E5eftArrow;\u61C4eiling;\u6309o\u01F5\u0FF9\0\u1005bleBracket;\u67E7n\u01D4\u100A\0\u1014eeVector;\u695Dector\u0100;B\u101D\u101E\u61C2ar;\u6955loor;\u630B\u0100er\u102D\u1043e\u0180;AV\u1035\u1036\u103C\u62A2rrow;\u61A6ector;\u695Biangle\u0180;BE\u1050\u1051\u1055\u62B3ar;\u69D0qual;\u62B5p\u0180DTV\u1063\u106E\u1078ownVector;\u694FeeVector;\u695Cector\u0100;B\u1082\u1083\u61BEar;\u6954ector\u0100;B\u1091\u1092\u61C0ar;\u6953\u0100pu\u109B\u109Ef;\u611DndImplies;\u6970ightarrow;\u61DB\u0100ch\u10B9\u10BCr;\u611B;\u61B1leDelayed;\u69F4\u0680HOacfhimoqstu\u10E4\u10F1\u10F7\u10FD\u1119\u111E\u1151\u1156\u1161\u1167\u11B5\u11BB\u11BF\u0100Cc\u10E9\u10EEHcy;\u4429y;\u4428FTcy;\u442Ccute;\u415A\u0280;aeiy\u1108\u1109\u110E\u1113\u1117\u6ABCron;\u4160dil;\u415Erc;\u415C;\u4421r;\uC000\u{1D516}ort\u0200DLRU\u112A\u1134\u113E\u1149ownArrow\xBB\u041EeftArrow\xBB\u089AightArrow\xBB\u0FDDpArrow;\u6191gma;\u43A3allCircle;\u6218pf;\uC000\u{1D54A}\u0272\u116D\0\0\u1170t;\u621Aare\u0200;ISU\u117B\u117C\u1189\u11AF\u65A1ntersection;\u6293u\u0100bp\u118F\u119Eset\u0100;E\u1197\u1198\u628Fqual;\u6291erset\u0100;E\u11A8\u11A9\u6290qual;\u6292nion;\u6294cr;\uC000\u{1D4AE}ar;\u62C6\u0200bcmp\u11C8\u11DB\u1209\u120B\u0100;s\u11CD\u11CE\u62D0et\u0100;E\u11CD\u11D5qual;\u6286\u0100ch\u11E0\u1205eeds\u0200;EST\u11ED\u11EE\u11F4\u11FF\u627Bqual;\u6AB0lantEqual;\u627Dilde;\u627FTh\xE1\u0F8C;\u6211\u0180;es\u1212\u1213\u1223\u62D1rset\u0100;E\u121C\u121D\u6283qual;\u6287et\xBB\u1213\u0580HRSacfhiors\u123E\u1244\u1249\u1255\u125E\u1271\u1276\u129F\u12C2\u12C8\u12D1ORN\u803B\xDE\u40DEADE;\u6122\u0100Hc\u124E\u1252cy;\u440By;\u4426\u0100bu\u125A\u125C;\u4009;\u43A4\u0180aey\u1265\u126A\u126Fron;\u4164dil;\u4162;\u4422r;\uC000\u{1D517}\u0100ei\u127B\u1289\u01F2\u1280\0\u1287efore;\u6234a;\u4398\u0100cn\u128E\u1298kSpace;\uC000\u205F\u200ASpace;\u6009lde\u0200;EFT\u12AB\u12AC\u12B2\u12BC\u623Cqual;\u6243ullEqual;\u6245ilde;\u6248pf;\uC000\u{1D54B}ipleDot;\u60DB\u0100ct\u12D6\u12DBr;\uC000\u{1D4AF}rok;\u4166\u0AE1\u12F7\u130E\u131A\u1326\0\u132C\u1331\0\0\0\0\0\u1338\u133D\u1377\u1385\0\u13FF\u1404\u140A\u1410\u0100cr\u12FB\u1301ute\u803B\xDA\u40DAr\u0100;o\u1307\u1308\u619Fcir;\u6949r\u01E3\u1313\0\u1316y;\u440Eve;\u416C\u0100iy\u131E\u1323rc\u803B\xDB\u40DB;\u4423blac;\u4170r;\uC000\u{1D518}rave\u803B\xD9\u40D9acr;\u416A\u0100di\u1341\u1369er\u0100BP\u1348\u135D\u0100ar\u134D\u1350r;\u405Fac\u0100ek\u1357\u1359;\u63DFet;\u63B5arenthesis;\u63DDon\u0100;P\u1370\u1371\u62C3lus;\u628E\u0100gp\u137B\u137Fon;\u4172f;\uC000\u{1D54C}\u0400ADETadps\u1395\u13AE\u13B8\u13C4\u03E8\u13D2\u13D7\u13F3rrow\u0180;BD\u1150\u13A0\u13A4ar;\u6912ownArrow;\u61C5ownArrow;\u6195quilibrium;\u696Eee\u0100;A\u13CB\u13CC\u62A5rrow;\u61A5own\xE1\u03F3er\u0100LR\u13DE\u13E8eftArrow;\u6196ightArrow;\u6197i\u0100;l\u13F9\u13FA\u43D2on;\u43A5ing;\u416Ecr;\uC000\u{1D4B0}ilde;\u4168ml\u803B\xDC\u40DC\u0480Dbcdefosv\u1427\u142C\u1430\u1433\u143E\u1485\u148A\u1490\u1496ash;\u62ABar;\u6AEBy;\u4412ash\u0100;l\u143B\u143C\u62A9;\u6AE6\u0100er\u1443\u1445;\u62C1\u0180bty\u144C\u1450\u147Aar;\u6016\u0100;i\u144F\u1455cal\u0200BLST\u1461\u1465\u146A\u1474ar;\u6223ine;\u407Ceparator;\u6758ilde;\u6240ThinSpace;\u600Ar;\uC000\u{1D519}pf;\uC000\u{1D54D}cr;\uC000\u{1D4B1}dash;\u62AA\u0280cefos\u14A7\u14AC\u14B1\u14B6\u14BCirc;\u4174dge;\u62C0r;\uC000\u{1D51A}pf;\uC000\u{1D54E}cr;\uC000\u{1D4B2}\u0200fios\u14CB\u14D0\u14D2\u14D8r;\uC000\u{1D51B};\u439Epf;\uC000\u{1D54F}cr;\uC000\u{1D4B3}\u0480AIUacfosu\u14F1\u14F5\u14F9\u14FD\u1504\u150F\u1514\u151A\u1520cy;\u442Fcy;\u4407cy;\u442Ecute\u803B\xDD\u40DD\u0100iy\u1509\u150Drc;\u4176;\u442Br;\uC000\u{1D51C}pf;\uC000\u{1D550}cr;\uC000\u{1D4B4}ml;\u4178\u0400Hacdefos\u1535\u1539\u153F\u154B\u154F\u155D\u1560\u1564cy;\u4416cute;\u4179\u0100ay\u1544\u1549ron;\u417D;\u4417ot;\u417B\u01F2\u1554\0\u155BoWidt\xE8\u0AD9a;\u4396r;\u6128pf;\u6124cr;\uC000\u{1D4B5}\u0BE1\u1583\u158A\u1590\0\u15B0\u15B6\u15BF\0\0\0\0\u15C6\u15DB\u15EB\u165F\u166D\0\u1695\u169B\u16B2\u16B9\0\u16BEcute\u803B\xE1\u40E1reve;\u4103\u0300;Ediuy\u159C\u159D\u15A1\u15A3\u15A8\u15AD\u623E;\uC000\u223E\u0333;\u623Frc\u803B\xE2\u40E2te\u80BB\xB4\u0306;\u4430lig\u803B\xE6\u40E6\u0100;r\xB2\u15BA;\uC000\u{1D51E}rave\u803B\xE0\u40E0\u0100ep\u15CA\u15D6\u0100fp\u15CF\u15D4sym;\u6135\xE8\u15D3ha;\u43B1\u0100ap\u15DFc\u0100cl\u15E4\u15E7r;\u4101g;\u6A3F\u0264\u15F0\0\0\u160A\u0280;adsv\u15FA\u15FB\u15FF\u1601\u1607\u6227nd;\u6A55;\u6A5Clope;\u6A58;\u6A5A\u0380;elmrsz\u1618\u1619\u161B\u161E\u163F\u164F\u1659\u6220;\u69A4e\xBB\u1619sd\u0100;a\u1625\u1626\u6221\u0461\u1630\u1632\u1634\u1636\u1638\u163A\u163C\u163E;\u69A8;\u69A9;\u69AA;\u69AB;\u69AC;\u69AD;\u69AE;\u69AFt\u0100;v\u1645\u1646\u621Fb\u0100;d\u164C\u164D\u62BE;\u699D\u0100pt\u1654\u1657h;\u6222\xBB\xB9arr;\u637C\u0100gp\u1663\u1667on;\u4105f;\uC000\u{1D552}\u0380;Eaeiop\u12C1\u167B\u167D\u1682\u1684\u1687\u168A;\u6A70cir;\u6A6F;\u624Ad;\u624Bs;\u4027rox\u0100;e\u12C1\u1692\xF1\u1683ing\u803B\xE5\u40E5\u0180cty\u16A1\u16A6\u16A8r;\uC000\u{1D4B6};\u402Amp\u0100;e\u12C1\u16AF\xF1\u0288ilde\u803B\xE3\u40E3ml\u803B\xE4\u40E4\u0100ci\u16C2\u16C8onin\xF4\u0272nt;\u6A11\u0800Nabcdefiklnoprsu\u16ED\u16F1\u1730\u173C\u1743\u1748\u1778\u177D\u17E0\u17E6\u1839\u1850\u170D\u193D\u1948\u1970ot;\u6AED\u0100cr\u16F6\u171Ek\u0200ceps\u1700\u1705\u170D\u1713ong;\u624Cpsilon;\u43F6rime;\u6035im\u0100;e\u171A\u171B\u623Dq;\u62CD\u0176\u1722\u1726ee;\u62BDed\u0100;g\u172C\u172D\u6305e\xBB\u172Drk\u0100;t\u135C\u1737brk;\u63B6\u0100oy\u1701\u1741;\u4431quo;\u601E\u0280cmprt\u1753\u175B\u1761\u1764\u1768aus\u0100;e\u010A\u0109ptyv;\u69B0s\xE9\u170Cno\xF5\u0113\u0180ahw\u176F\u1771\u1773;\u43B2;\u6136een;\u626Cr;\uC000\u{1D51F}g\u0380costuvw\u178D\u179D\u17B3\u17C1\u17D5\u17DB\u17DE\u0180aiu\u1794\u1796\u179A\xF0\u0760rc;\u65EFp\xBB\u1371\u0180dpt\u17A4\u17A8\u17ADot;\u6A00lus;\u6A01imes;\u6A02\u0271\u17B9\0\0\u17BEcup;\u6A06ar;\u6605riangle\u0100du\u17CD\u17D2own;\u65BDp;\u65B3plus;\u6A04e\xE5\u1444\xE5\u14ADarow;\u690D\u0180ako\u17ED\u1826\u1835\u0100cn\u17F2\u1823k\u0180lst\u17FA\u05AB\u1802ozenge;\u69EBriangle\u0200;dlr\u1812\u1813\u1818\u181D\u65B4own;\u65BEeft;\u65C2ight;\u65B8k;\u6423\u01B1\u182B\0\u1833\u01B2\u182F\0\u1831;\u6592;\u65914;\u6593ck;\u6588\u0100eo\u183E\u184D\u0100;q\u1843\u1846\uC000=\u20E5uiv;\uC000\u2261\u20E5t;\u6310\u0200ptwx\u1859\u185E\u1867\u186Cf;\uC000\u{1D553}\u0100;t\u13CB\u1863om\xBB\u13CCtie;\u62C8\u0600DHUVbdhmptuv\u1885\u1896\u18AA\u18BB\u18D7\u18DB\u18EC\u18FF\u1905\u190A\u1910\u1921\u0200LRlr\u188E\u1890\u1892\u1894;\u6557;\u6554;\u6556;\u6553\u0280;DUdu\u18A1\u18A2\u18A4\u18A6\u18A8\u6550;\u6566;\u6569;\u6564;\u6567\u0200LRlr\u18B3\u18B5\u18B7\u18B9;\u655D;\u655A;\u655C;\u6559\u0380;HLRhlr\u18CA\u18CB\u18CD\u18CF\u18D1\u18D3\u18D5\u6551;\u656C;\u6563;\u6560;\u656B;\u6562;\u655Fox;\u69C9\u0200LRlr\u18E4\u18E6\u18E8\u18EA;\u6555;\u6552;\u6510;\u650C\u0280;DUdu\u06BD\u18F7\u18F9\u18FB\u18FD;\u6565;\u6568;\u652C;\u6534inus;\u629Flus;\u629Eimes;\u62A0\u0200LRlr\u1919\u191B\u191D\u191F;\u655B;\u6558;\u6518;\u6514\u0380;HLRhlr\u1930\u1931\u1933\u1935\u1937\u1939\u193B\u6502;\u656A;\u6561;\u655E;\u653C;\u6524;\u651C\u0100ev\u0123\u1942bar\u803B\xA6\u40A6\u0200ceio\u1951\u1956\u195A\u1960r;\uC000\u{1D4B7}mi;\u604Fm\u0100;e\u171A\u171Cl\u0180;bh\u1968\u1969\u196B\u405C;\u69C5sub;\u67C8\u016C\u1974\u197El\u0100;e\u1979\u197A\u6022t\xBB\u197Ap\u0180;Ee\u012F\u1985\u1987;\u6AAE\u0100;q\u06DC\u06DB\u0CE1\u19A7\0\u19E8\u1A11\u1A15\u1A32\0\u1A37\u1A50\0\0\u1AB4\0\0\u1AC1\0\0\u1B21\u1B2E\u1B4D\u1B52\0\u1BFD\0\u1C0C\u0180cpr\u19AD\u19B2\u19DDute;\u4107\u0300;abcds\u19BF\u19C0\u19C4\u19CA\u19D5\u19D9\u6229nd;\u6A44rcup;\u6A49\u0100au\u19CF\u19D2p;\u6A4Bp;\u6A47ot;\u6A40;\uC000\u2229\uFE00\u0100eo\u19E2\u19E5t;\u6041\xEE\u0693\u0200aeiu\u19F0\u19FB\u1A01\u1A05\u01F0\u19F5\0\u19F8s;\u6A4Don;\u410Ddil\u803B\xE7\u40E7rc;\u4109ps\u0100;s\u1A0C\u1A0D\u6A4Cm;\u6A50ot;\u410B\u0180dmn\u1A1B\u1A20\u1A26il\u80BB\xB8\u01ADptyv;\u69B2t\u8100\xA2;e\u1A2D\u1A2E\u40A2r\xE4\u01B2r;\uC000\u{1D520}\u0180cei\u1A3D\u1A40\u1A4Dy;\u4447ck\u0100;m\u1A47\u1A48\u6713ark\xBB\u1A48;\u43C7r\u0380;Ecefms\u1A5F\u1A60\u1A62\u1A6B\u1AA4\u1AAA\u1AAE\u65CB;\u69C3\u0180;el\u1A69\u1A6A\u1A6D\u42C6q;\u6257e\u0261\u1A74\0\0\u1A88rrow\u0100lr\u1A7C\u1A81eft;\u61BAight;\u61BB\u0280RSacd\u1A92\u1A94\u1A96\u1A9A\u1A9F\xBB\u0F47;\u64C8st;\u629Birc;\u629Aash;\u629Dnint;\u6A10id;\u6AEFcir;\u69C2ubs\u0100;u\u1ABB\u1ABC\u6663it\xBB\u1ABC\u02EC\u1AC7\u1AD4\u1AFA\0\u1B0Aon\u0100;e\u1ACD\u1ACE\u403A\u0100;q\xC7\xC6\u026D\u1AD9\0\0\u1AE2a\u0100;t\u1ADE\u1ADF\u402C;\u4040\u0180;fl\u1AE8\u1AE9\u1AEB\u6201\xEE\u1160e\u0100mx\u1AF1\u1AF6ent\xBB\u1AE9e\xF3\u024D\u01E7\u1AFE\0\u1B07\u0100;d\u12BB\u1B02ot;\u6A6Dn\xF4\u0246\u0180fry\u1B10\u1B14\u1B17;\uC000\u{1D554}o\xE4\u0254\u8100\xA9;s\u0155\u1B1Dr;\u6117\u0100ao\u1B25\u1B29rr;\u61B5ss;\u6717\u0100cu\u1B32\u1B37r;\uC000\u{1D4B8}\u0100bp\u1B3C\u1B44\u0100;e\u1B41\u1B42\u6ACF;\u6AD1\u0100;e\u1B49\u1B4A\u6AD0;\u6AD2dot;\u62EF\u0380delprvw\u1B60\u1B6C\u1B77\u1B82\u1BAC\u1BD4\u1BF9arr\u0100lr\u1B68\u1B6A;\u6938;\u6935\u0270\u1B72\0\0\u1B75r;\u62DEc;\u62DFarr\u0100;p\u1B7F\u1B80\u61B6;\u693D\u0300;bcdos\u1B8F\u1B90\u1B96\u1BA1\u1BA5\u1BA8\u622Arcap;\u6A48\u0100au\u1B9B\u1B9Ep;\u6A46p;\u6A4Aot;\u628Dr;\u6A45;\uC000\u222A\uFE00\u0200alrv\u1BB5\u1BBF\u1BDE\u1BE3rr\u0100;m\u1BBC\u1BBD\u61B7;\u693Cy\u0180evw\u1BC7\u1BD4\u1BD8q\u0270\u1BCE\0\0\u1BD2re\xE3\u1B73u\xE3\u1B75ee;\u62CEedge;\u62CFen\u803B\xA4\u40A4earrow\u0100lr\u1BEE\u1BF3eft\xBB\u1B80ight\xBB\u1BBDe\xE4\u1BDD\u0100ci\u1C01\u1C07onin\xF4\u01F7nt;\u6231lcty;\u632D\u0980AHabcdefhijlorstuwz\u1C38\u1C3B\u1C3F\u1C5D\u1C69\u1C75\u1C8A\u1C9E\u1CAC\u1CB7\u1CFB\u1CFF\u1D0D\u1D7B\u1D91\u1DAB\u1DBB\u1DC6\u1DCDr\xF2\u0381ar;\u6965\u0200glrs\u1C48\u1C4D\u1C52\u1C54ger;\u6020eth;\u6138\xF2\u1133h\u0100;v\u1C5A\u1C5B\u6010\xBB\u090A\u016B\u1C61\u1C67arow;\u690Fa\xE3\u0315\u0100ay\u1C6E\u1C73ron;\u410F;\u4434\u0180;ao\u0332\u1C7C\u1C84\u0100gr\u02BF\u1C81r;\u61CAtseq;\u6A77\u0180glm\u1C91\u1C94\u1C98\u803B\xB0\u40B0ta;\u43B4ptyv;\u69B1\u0100ir\u1CA3\u1CA8sht;\u697F;\uC000\u{1D521}ar\u0100lr\u1CB3\u1CB5\xBB\u08DC\xBB\u101E\u0280aegsv\u1CC2\u0378\u1CD6\u1CDC\u1CE0m\u0180;os\u0326\u1CCA\u1CD4nd\u0100;s\u0326\u1CD1uit;\u6666amma;\u43DDin;\u62F2\u0180;io\u1CE7\u1CE8\u1CF8\u40F7de\u8100\xF7;o\u1CE7\u1CF0ntimes;\u62C7n\xF8\u1CF7cy;\u4452c\u026F\u1D06\0\0\u1D0Arn;\u631Eop;\u630D\u0280lptuw\u1D18\u1D1D\u1D22\u1D49\u1D55lar;\u4024f;\uC000\u{1D555}\u0280;emps\u030B\u1D2D\u1D37\u1D3D\u1D42q\u0100;d\u0352\u1D33ot;\u6251inus;\u6238lus;\u6214quare;\u62A1blebarwedg\xE5\xFAn\u0180adh\u112E\u1D5D\u1D67ownarrow\xF3\u1C83arpoon\u0100lr\u1D72\u1D76ef\xF4\u1CB4igh\xF4\u1CB6\u0162\u1D7F\u1D85karo\xF7\u0F42\u026F\u1D8A\0\0\u1D8Ern;\u631Fop;\u630C\u0180cot\u1D98\u1DA3\u1DA6\u0100ry\u1D9D\u1DA1;\uC000\u{1D4B9};\u4455l;\u69F6rok;\u4111\u0100dr\u1DB0\u1DB4ot;\u62F1i\u0100;f\u1DBA\u1816\u65BF\u0100ah\u1DC0\u1DC3r\xF2\u0429a\xF2\u0FA6angle;\u69A6\u0100ci\u1DD2\u1DD5y;\u445Fgrarr;\u67FF\u0900Dacdefglmnopqrstux\u1E01\u1E09\u1E19\u1E38\u0578\u1E3C\u1E49\u1E61\u1E7E\u1EA5\u1EAF\u1EBD\u1EE1\u1F2A\u1F37\u1F44\u1F4E\u1F5A\u0100Do\u1E06\u1D34o\xF4\u1C89\u0100cs\u1E0E\u1E14ute\u803B\xE9\u40E9ter;\u6A6E\u0200aioy\u1E22\u1E27\u1E31\u1E36ron;\u411Br\u0100;c\u1E2D\u1E2E\u6256\u803B\xEA\u40EAlon;\u6255;\u444Dot;\u4117\u0100Dr\u1E41\u1E45ot;\u6252;\uC000\u{1D522}\u0180;rs\u1E50\u1E51\u1E57\u6A9Aave\u803B\xE8\u40E8\u0100;d\u1E5C\u1E5D\u6A96ot;\u6A98\u0200;ils\u1E6A\u1E6B\u1E72\u1E74\u6A99nters;\u63E7;\u6113\u0100;d\u1E79\u1E7A\u6A95ot;\u6A97\u0180aps\u1E85\u1E89\u1E97cr;\u4113ty\u0180;sv\u1E92\u1E93\u1E95\u6205et\xBB\u1E93p\u01001;\u1E9D\u1EA4\u0133\u1EA1\u1EA3;\u6004;\u6005\u6003\u0100gs\u1EAA\u1EAC;\u414Bp;\u6002\u0100gp\u1EB4\u1EB8on;\u4119f;\uC000\u{1D556}\u0180als\u1EC4\u1ECE\u1ED2r\u0100;s\u1ECA\u1ECB\u62D5l;\u69E3us;\u6A71i\u0180;lv\u1EDA\u1EDB\u1EDF\u43B5on\xBB\u1EDB;\u43F5\u0200csuv\u1EEA\u1EF3\u1F0B\u1F23\u0100io\u1EEF\u1E31rc\xBB\u1E2E\u0269\u1EF9\0\0\u1EFB\xED\u0548ant\u0100gl\u1F02\u1F06tr\xBB\u1E5Dess\xBB\u1E7A\u0180aei\u1F12\u1F16\u1F1Als;\u403Dst;\u625Fv\u0100;D\u0235\u1F20D;\u6A78parsl;\u69E5\u0100Da\u1F2F\u1F33ot;\u6253rr;\u6971\u0180cdi\u1F3E\u1F41\u1EF8r;\u612Fo\xF4\u0352\u0100ah\u1F49\u1F4B;\u43B7\u803B\xF0\u40F0\u0100mr\u1F53\u1F57l\u803B\xEB\u40EBo;\u60AC\u0180cip\u1F61\u1F64\u1F67l;\u4021s\xF4\u056E\u0100eo\u1F6C\u1F74ctatio\xEE\u0559nential\xE5\u0579\u09E1\u1F92\0\u1F9E\0\u1FA1\u1FA7\0\0\u1FC6\u1FCC\0\u1FD3\0\u1FE6\u1FEA\u2000\0\u2008\u205Allingdotse\xF1\u1E44y;\u4444male;\u6640\u0180ilr\u1FAD\u1FB3\u1FC1lig;\u8000\uFB03\u0269\u1FB9\0\0\u1FBDg;\u8000\uFB00ig;\u8000\uFB04;\uC000\u{1D523}lig;\u8000\uFB01lig;\uC000fj\u0180alt\u1FD9\u1FDC\u1FE1t;\u666Dig;\u8000\uFB02ns;\u65B1of;\u4192\u01F0\u1FEE\0\u1FF3f;\uC000\u{1D557}\u0100ak\u05BF\u1FF7\u0100;v\u1FFC\u1FFD\u62D4;\u6AD9artint;\u6A0D\u0100ao\u200C\u2055\u0100cs\u2011\u2052\u03B1\u201A\u2030\u2038\u2045\u2048\0\u2050\u03B2\u2022\u2025\u2027\u202A\u202C\0\u202E\u803B\xBD\u40BD;\u6153\u803B\xBC\u40BC;\u6155;\u6159;\u615B\u01B3\u2034\0\u2036;\u6154;\u6156\u02B4\u203E\u2041\0\0\u2043\u803B\xBE\u40BE;\u6157;\u615C5;\u6158\u01B6\u204C\0\u204E;\u615A;\u615D8;\u615El;\u6044wn;\u6322cr;\uC000\u{1D4BB}\u0880Eabcdefgijlnorstv\u2082\u2089\u209F\u20A5\u20B0\u20B4\u20F0\u20F5\u20FA\u20FF\u2103\u2112\u2138\u0317\u213E\u2152\u219E\u0100;l\u064D\u2087;\u6A8C\u0180cmp\u2090\u2095\u209Dute;\u41F5ma\u0100;d\u209C\u1CDA\u43B3;\u6A86reve;\u411F\u0100iy\u20AA\u20AErc;\u411D;\u4433ot;\u4121\u0200;lqs\u063E\u0642\u20BD\u20C9\u0180;qs\u063E\u064C\u20C4lan\xF4\u0665\u0200;cdl\u0665\u20D2\u20D5\u20E5c;\u6AA9ot\u0100;o\u20DC\u20DD\u6A80\u0100;l\u20E2\u20E3\u6A82;\u6A84\u0100;e\u20EA\u20ED\uC000\u22DB\uFE00s;\u6A94r;\uC000\u{1D524}\u0100;g\u0673\u061Bmel;\u6137cy;\u4453\u0200;Eaj\u065A\u210C\u210E\u2110;\u6A92;\u6AA5;\u6AA4\u0200Eaes\u211B\u211D\u2129\u2134;\u6269p\u0100;p\u2123\u2124\u6A8Arox\xBB\u2124\u0100;q\u212E\u212F\u6A88\u0100;q\u212E\u211Bim;\u62E7pf;\uC000\u{1D558}\u0100ci\u2143\u2146r;\u610Am\u0180;el\u066B\u214E\u2150;\u6A8E;\u6A90\u8300>;cdlqr\u05EE\u2160\u216A\u216E\u2173\u2179\u0100ci\u2165\u2167;\u6AA7r;\u6A7Aot;\u62D7Par;\u6995uest;\u6A7C\u0280adels\u2184\u216A\u2190\u0656\u219B\u01F0\u2189\0\u218Epro\xF8\u209Er;\u6978q\u0100lq\u063F\u2196les\xF3\u2088i\xED\u066B\u0100en\u21A3\u21ADrtneqq;\uC000\u2269\uFE00\xC5\u21AA\u0500Aabcefkosy\u21C4\u21C7\u21F1\u21F5\u21FA\u2218\u221D\u222F\u2268\u227Dr\xF2\u03A0\u0200ilmr\u21D0\u21D4\u21D7\u21DBrs\xF0\u1484f\xBB\u2024il\xF4\u06A9\u0100dr\u21E0\u21E4cy;\u444A\u0180;cw\u08F4\u21EB\u21EFir;\u6948;\u61ADar;\u610Firc;\u4125\u0180alr\u2201\u220E\u2213rts\u0100;u\u2209\u220A\u6665it\xBB\u220Alip;\u6026con;\u62B9r;\uC000\u{1D525}s\u0100ew\u2223\u2229arow;\u6925arow;\u6926\u0280amopr\u223A\u223E\u2243\u225E\u2263rr;\u61FFtht;\u623Bk\u0100lr\u2249\u2253eftarrow;\u61A9ightarrow;\u61AAf;\uC000\u{1D559}bar;\u6015\u0180clt\u226F\u2274\u2278r;\uC000\u{1D4BD}as\xE8\u21F4rok;\u4127\u0100bp\u2282\u2287ull;\u6043hen\xBB\u1C5B\u0AE1\u22A3\0\u22AA\0\u22B8\u22C5\u22CE\0\u22D5\u22F3\0\0\u22F8\u2322\u2367\u2362\u237F\0\u2386\u23AA\u23B4cute\u803B\xED\u40ED\u0180;iy\u0771\u22B0\u22B5rc\u803B\xEE\u40EE;\u4438\u0100cx\u22BC\u22BFy;\u4435cl\u803B\xA1\u40A1\u0100fr\u039F\u22C9;\uC000\u{1D526}rave\u803B\xEC\u40EC\u0200;ino\u073E\u22DD\u22E9\u22EE\u0100in\u22E2\u22E6nt;\u6A0Ct;\u622Dfin;\u69DCta;\u6129lig;\u4133\u0180aop\u22FE\u231A\u231D\u0180cgt\u2305\u2308\u2317r;\u412B\u0180elp\u071F\u230F\u2313in\xE5\u078Ear\xF4\u0720h;\u4131f;\u62B7ed;\u41B5\u0280;cfot\u04F4\u232C\u2331\u233D\u2341are;\u6105in\u0100;t\u2338\u2339\u621Eie;\u69DDdo\xF4\u2319\u0280;celp\u0757\u234C\u2350\u235B\u2361al;\u62BA\u0100gr\u2355\u2359er\xF3\u1563\xE3\u234Darhk;\u6A17rod;\u6A3C\u0200cgpt\u236F\u2372\u2376\u237By;\u4451on;\u412Ff;\uC000\u{1D55A}a;\u43B9uest\u803B\xBF\u40BF\u0100ci\u238A\u238Fr;\uC000\u{1D4BE}n\u0280;Edsv\u04F4\u239B\u239D\u23A1\u04F3;\u62F9ot;\u62F5\u0100;v\u23A6\u23A7\u62F4;\u62F3\u0100;i\u0777\u23AElde;\u4129\u01EB\u23B8\0\u23BCcy;\u4456l\u803B\xEF\u40EF\u0300cfmosu\u23CC\u23D7\u23DC\u23E1\u23E7\u23F5\u0100iy\u23D1\u23D5rc;\u4135;\u4439r;\uC000\u{1D527}ath;\u4237pf;\uC000\u{1D55B}\u01E3\u23EC\0\u23F1r;\uC000\u{1D4BF}rcy;\u4458kcy;\u4454\u0400acfghjos\u240B\u2416\u2422\u2427\u242D\u2431\u2435\u243Bppa\u0100;v\u2413\u2414\u43BA;\u43F0\u0100ey\u241B\u2420dil;\u4137;\u443Ar;\uC000\u{1D528}reen;\u4138cy;\u4445cy;\u445Cpf;\uC000\u{1D55C}cr;\uC000\u{1D4C0}\u0B80ABEHabcdefghjlmnoprstuv\u2470\u2481\u2486\u248D\u2491\u250E\u253D\u255A\u2580\u264E\u265E\u2665\u2679\u267D\u269A\u26B2\u26D8\u275D\u2768\u278B\u27C0\u2801\u2812\u0180art\u2477\u247A\u247Cr\xF2\u09C6\xF2\u0395ail;\u691Barr;\u690E\u0100;g\u0994\u248B;\u6A8Bar;\u6962\u0963\u24A5\0\u24AA\0\u24B1\0\0\0\0\0\u24B5\u24BA\0\u24C6\u24C8\u24CD\0\u24F9ute;\u413Amptyv;\u69B4ra\xEE\u084Cbda;\u43BBg\u0180;dl\u088E\u24C1\u24C3;\u6991\xE5\u088E;\u6A85uo\u803B\xAB\u40ABr\u0400;bfhlpst\u0899\u24DE\u24E6\u24E9\u24EB\u24EE\u24F1\u24F5\u0100;f\u089D\u24E3s;\u691Fs;\u691D\xEB\u2252p;\u61ABl;\u6939im;\u6973l;\u61A2\u0180;ae\u24FF\u2500\u2504\u6AABil;\u6919\u0100;s\u2509\u250A\u6AAD;\uC000\u2AAD\uFE00\u0180abr\u2515\u2519\u251Drr;\u690Crk;\u6772\u0100ak\u2522\u252Cc\u0100ek\u2528\u252A;\u407B;\u405B\u0100es\u2531\u2533;\u698Bl\u0100du\u2539\u253B;\u698F;\u698D\u0200aeuy\u2546\u254B\u2556\u2558ron;\u413E\u0100di\u2550\u2554il;\u413C\xEC\u08B0\xE2\u2529;\u443B\u0200cqrs\u2563\u2566\u256D\u257Da;\u6936uo\u0100;r\u0E19\u1746\u0100du\u2572\u2577har;\u6967shar;\u694Bh;\u61B2\u0280;fgqs\u258B\u258C\u0989\u25F3\u25FF\u6264t\u0280ahlrt\u2598\u25A4\u25B7\u25C2\u25E8rrow\u0100;t\u0899\u25A1a\xE9\u24F6arpoon\u0100du\u25AF\u25B4own\xBB\u045Ap\xBB\u0966eftarrows;\u61C7ight\u0180ahs\u25CD\u25D6\u25DErrow\u0100;s\u08F4\u08A7arpoon\xF3\u0F98quigarro\xF7\u21F0hreetimes;\u62CB\u0180;qs\u258B\u0993\u25FAlan\xF4\u09AC\u0280;cdgs\u09AC\u260A\u260D\u261D\u2628c;\u6AA8ot\u0100;o\u2614\u2615\u6A7F\u0100;r\u261A\u261B\u6A81;\u6A83\u0100;e\u2622\u2625\uC000\u22DA\uFE00s;\u6A93\u0280adegs\u2633\u2639\u263D\u2649\u264Bppro\xF8\u24C6ot;\u62D6q\u0100gq\u2643\u2645\xF4\u0989gt\xF2\u248C\xF4\u099Bi\xED\u09B2\u0180ilr\u2655\u08E1\u265Asht;\u697C;\uC000\u{1D529}\u0100;E\u099C\u2663;\u6A91\u0161\u2669\u2676r\u0100du\u25B2\u266E\u0100;l\u0965\u2673;\u696Alk;\u6584cy;\u4459\u0280;acht\u0A48\u2688\u268B\u2691\u2696r\xF2\u25C1orne\xF2\u1D08ard;\u696Bri;\u65FA\u0100io\u269F\u26A4dot;\u4140ust\u0100;a\u26AC\u26AD\u63B0che\xBB\u26AD\u0200Eaes\u26BB\u26BD\u26C9\u26D4;\u6268p\u0100;p\u26C3\u26C4\u6A89rox\xBB\u26C4\u0100;q\u26CE\u26CF\u6A87\u0100;q\u26CE\u26BBim;\u62E6\u0400abnoptwz\u26E9\u26F4\u26F7\u271A\u272F\u2741\u2747\u2750\u0100nr\u26EE\u26F1g;\u67ECr;\u61FDr\xEB\u08C1g\u0180lmr\u26FF\u270D\u2714eft\u0100ar\u09E6\u2707ight\xE1\u09F2apsto;\u67FCight\xE1\u09FDparrow\u0100lr\u2725\u2729ef\xF4\u24EDight;\u61AC\u0180afl\u2736\u2739\u273Dr;\u6985;\uC000\u{1D55D}us;\u6A2Dimes;\u6A34\u0161\u274B\u274Fst;\u6217\xE1\u134E\u0180;ef\u2757\u2758\u1800\u65CAnge\xBB\u2758ar\u0100;l\u2764\u2765\u4028t;\u6993\u0280achmt\u2773\u2776\u277C\u2785\u2787r\xF2\u08A8orne\xF2\u1D8Car\u0100;d\u0F98\u2783;\u696D;\u600Eri;\u62BF\u0300achiqt\u2798\u279D\u0A40\u27A2\u27AE\u27BBquo;\u6039r;\uC000\u{1D4C1}m\u0180;eg\u09B2\u27AA\u27AC;\u6A8D;\u6A8F\u0100bu\u252A\u27B3o\u0100;r\u0E1F\u27B9;\u601Arok;\u4142\u8400<;cdhilqr\u082B\u27D2\u2639\u27DC\u27E0\u27E5\u27EA\u27F0\u0100ci\u27D7\u27D9;\u6AA6r;\u6A79re\xE5\u25F2mes;\u62C9arr;\u6976uest;\u6A7B\u0100Pi\u27F5\u27F9ar;\u6996\u0180;ef\u2800\u092D\u181B\u65C3r\u0100du\u2807\u280Dshar;\u694Ahar;\u6966\u0100en\u2817\u2821rtneqq;\uC000\u2268\uFE00\xC5\u281E\u0700Dacdefhilnopsu\u2840\u2845\u2882\u288E\u2893\u28A0\u28A5\u28A8\u28DA\u28E2\u28E4\u0A83\u28F3\u2902Dot;\u623A\u0200clpr\u284E\u2852\u2863\u287Dr\u803B\xAF\u40AF\u0100et\u2857\u2859;\u6642\u0100;e\u285E\u285F\u6720se\xBB\u285F\u0100;s\u103B\u2868to\u0200;dlu\u103B\u2873\u2877\u287Bow\xEE\u048Cef\xF4\u090F\xF0\u13D1ker;\u65AE\u0100oy\u2887\u288Cmma;\u6A29;\u443Cash;\u6014asuredangle\xBB\u1626r;\uC000\u{1D52A}o;\u6127\u0180cdn\u28AF\u28B4\u28C9ro\u803B\xB5\u40B5\u0200;acd\u1464\u28BD\u28C0\u28C4s\xF4\u16A7ir;\u6AF0ot\u80BB\xB7\u01B5us\u0180;bd\u28D2\u1903\u28D3\u6212\u0100;u\u1D3C\u28D8;\u6A2A\u0163\u28DE\u28E1p;\u6ADB\xF2\u2212\xF0\u0A81\u0100dp\u28E9\u28EEels;\u62A7f;\uC000\u{1D55E}\u0100ct\u28F8\u28FDr;\uC000\u{1D4C2}pos\xBB\u159D\u0180;lm\u2909\u290A\u290D\u43BCtimap;\u62B8\u0C00GLRVabcdefghijlmoprstuvw\u2942\u2953\u297E\u2989\u2998\u29DA\u29E9\u2A15\u2A1A\u2A58\u2A5D\u2A83\u2A95\u2AA4\u2AA8\u2B04\u2B07\u2B44\u2B7F\u2BAE\u2C34\u2C67\u2C7C\u2CE9\u0100gt\u2947\u294B;\uC000\u22D9\u0338\u0100;v\u2950\u0BCF\uC000\u226B\u20D2\u0180elt\u295A\u2972\u2976ft\u0100ar\u2961\u2967rrow;\u61CDightarrow;\u61CE;\uC000\u22D8\u0338\u0100;v\u297B\u0C47\uC000\u226A\u20D2ightarrow;\u61CF\u0100Dd\u298E\u2993ash;\u62AFash;\u62AE\u0280bcnpt\u29A3\u29A7\u29AC\u29B1\u29CCla\xBB\u02DEute;\u4144g;\uC000\u2220\u20D2\u0280;Eiop\u0D84\u29BC\u29C0\u29C5\u29C8;\uC000\u2A70\u0338d;\uC000\u224B\u0338s;\u4149ro\xF8\u0D84ur\u0100;a\u29D3\u29D4\u666El\u0100;s\u29D3\u0B38\u01F3\u29DF\0\u29E3p\u80BB\xA0\u0B37mp\u0100;e\u0BF9\u0C00\u0280aeouy\u29F4\u29FE\u2A03\u2A10\u2A13\u01F0\u29F9\0\u29FB;\u6A43on;\u4148dil;\u4146ng\u0100;d\u0D7E\u2A0Aot;\uC000\u2A6D\u0338p;\u6A42;\u443Dash;\u6013\u0380;Aadqsx\u0B92\u2A29\u2A2D\u2A3B\u2A41\u2A45\u2A50rr;\u61D7r\u0100hr\u2A33\u2A36k;\u6924\u0100;o\u13F2\u13F0ot;\uC000\u2250\u0338ui\xF6\u0B63\u0100ei\u2A4A\u2A4Ear;\u6928\xED\u0B98ist\u0100;s\u0BA0\u0B9Fr;\uC000\u{1D52B}\u0200Eest\u0BC5\u2A66\u2A79\u2A7C\u0180;qs\u0BBC\u2A6D\u0BE1\u0180;qs\u0BBC\u0BC5\u2A74lan\xF4\u0BE2i\xED\u0BEA\u0100;r\u0BB6\u2A81\xBB\u0BB7\u0180Aap\u2A8A\u2A8D\u2A91r\xF2\u2971rr;\u61AEar;\u6AF2\u0180;sv\u0F8D\u2A9C\u0F8C\u0100;d\u2AA1\u2AA2\u62FC;\u62FAcy;\u445A\u0380AEadest\u2AB7\u2ABA\u2ABE\u2AC2\u2AC5\u2AF6\u2AF9r\xF2\u2966;\uC000\u2266\u0338rr;\u619Ar;\u6025\u0200;fqs\u0C3B\u2ACE\u2AE3\u2AEFt\u0100ar\u2AD4\u2AD9rro\xF7\u2AC1ightarro\xF7\u2A90\u0180;qs\u0C3B\u2ABA\u2AEAlan\xF4\u0C55\u0100;s\u0C55\u2AF4\xBB\u0C36i\xED\u0C5D\u0100;r\u0C35\u2AFEi\u0100;e\u0C1A\u0C25i\xE4\u0D90\u0100pt\u2B0C\u2B11f;\uC000\u{1D55F}\u8180\xAC;in\u2B19\u2B1A\u2B36\u40ACn\u0200;Edv\u0B89\u2B24\u2B28\u2B2E;\uC000\u22F9\u0338ot;\uC000\u22F5\u0338\u01E1\u0B89\u2B33\u2B35;\u62F7;\u62F6i\u0100;v\u0CB8\u2B3C\u01E1\u0CB8\u2B41\u2B43;\u62FE;\u62FD\u0180aor\u2B4B\u2B63\u2B69r\u0200;ast\u0B7B\u2B55\u2B5A\u2B5Flle\xEC\u0B7Bl;\uC000\u2AFD\u20E5;\uC000\u2202\u0338lint;\u6A14\u0180;ce\u0C92\u2B70\u2B73u\xE5\u0CA5\u0100;c\u0C98\u2B78\u0100;e\u0C92\u2B7D\xF1\u0C98\u0200Aait\u2B88\u2B8B\u2B9D\u2BA7r\xF2\u2988rr\u0180;cw\u2B94\u2B95\u2B99\u619B;\uC000\u2933\u0338;\uC000\u219D\u0338ghtarrow\xBB\u2B95ri\u0100;e\u0CCB\u0CD6\u0380chimpqu\u2BBD\u2BCD\u2BD9\u2B04\u0B78\u2BE4\u2BEF\u0200;cer\u0D32\u2BC6\u0D37\u2BC9u\xE5\u0D45;\uC000\u{1D4C3}ort\u026D\u2B05\0\0\u2BD6ar\xE1\u2B56m\u0100;e\u0D6E\u2BDF\u0100;q\u0D74\u0D73su\u0100bp\u2BEB\u2BED\xE5\u0CF8\xE5\u0D0B\u0180bcp\u2BF6\u2C11\u2C19\u0200;Ees\u2BFF\u2C00\u0D22\u2C04\u6284;\uC000\u2AC5\u0338et\u0100;e\u0D1B\u2C0Bq\u0100;q\u0D23\u2C00c\u0100;e\u0D32\u2C17\xF1\u0D38\u0200;Ees\u2C22\u2C23\u0D5F\u2C27\u6285;\uC000\u2AC6\u0338et\u0100;e\u0D58\u2C2Eq\u0100;q\u0D60\u2C23\u0200gilr\u2C3D\u2C3F\u2C45\u2C47\xEC\u0BD7lde\u803B\xF1\u40F1\xE7\u0C43iangle\u0100lr\u2C52\u2C5Ceft\u0100;e\u0C1A\u2C5A\xF1\u0C26ight\u0100;e\u0CCB\u2C65\xF1\u0CD7\u0100;m\u2C6C\u2C6D\u43BD\u0180;es\u2C74\u2C75\u2C79\u4023ro;\u6116p;\u6007\u0480DHadgilrs\u2C8F\u2C94\u2C99\u2C9E\u2CA3\u2CB0\u2CB6\u2CD3\u2CE3ash;\u62ADarr;\u6904p;\uC000\u224D\u20D2ash;\u62AC\u0100et\u2CA8\u2CAC;\uC000\u2265\u20D2;\uC000>\u20D2nfin;\u69DE\u0180Aet\u2CBD\u2CC1\u2CC5rr;\u6902;\uC000\u2264\u20D2\u0100;r\u2CCA\u2CCD\uC000<\u20D2ie;\uC000\u22B4\u20D2\u0100At\u2CD8\u2CDCrr;\u6903rie;\uC000\u22B5\u20D2im;\uC000\u223C\u20D2\u0180Aan\u2CF0\u2CF4\u2D02rr;\u61D6r\u0100hr\u2CFA\u2CFDk;\u6923\u0100;o\u13E7\u13E5ear;\u6927\u1253\u1A95\0\0\0\0\0\0\0\0\0\0\0\0\0\u2D2D\0\u2D38\u2D48\u2D60\u2D65\u2D72\u2D84\u1B07\0\0\u2D8D\u2DAB\0\u2DC8\u2DCE\0\u2DDC\u2E19\u2E2B\u2E3E\u2E43\u0100cs\u2D31\u1A97ute\u803B\xF3\u40F3\u0100iy\u2D3C\u2D45r\u0100;c\u1A9E\u2D42\u803B\xF4\u40F4;\u443E\u0280abios\u1AA0\u2D52\u2D57\u01C8\u2D5Alac;\u4151v;\u6A38old;\u69BClig;\u4153\u0100cr\u2D69\u2D6Dir;\u69BF;\uC000\u{1D52C}\u036F\u2D79\0\0\u2D7C\0\u2D82n;\u42DBave\u803B\xF2\u40F2;\u69C1\u0100bm\u2D88\u0DF4ar;\u69B5\u0200acit\u2D95\u2D98\u2DA5\u2DA8r\xF2\u1A80\u0100ir\u2D9D\u2DA0r;\u69BEoss;\u69BBn\xE5\u0E52;\u69C0\u0180aei\u2DB1\u2DB5\u2DB9cr;\u414Dga;\u43C9\u0180cdn\u2DC0\u2DC5\u01CDron;\u43BF;\u69B6pf;\uC000\u{1D560}\u0180ael\u2DD4\u2DD7\u01D2r;\u69B7rp;\u69B9\u0380;adiosv\u2DEA\u2DEB\u2DEE\u2E08\u2E0D\u2E10\u2E16\u6228r\xF2\u1A86\u0200;efm\u2DF7\u2DF8\u2E02\u2E05\u6A5Dr\u0100;o\u2DFE\u2DFF\u6134f\xBB\u2DFF\u803B\xAA\u40AA\u803B\xBA\u40BAgof;\u62B6r;\u6A56lope;\u6A57;\u6A5B\u0180clo\u2E1F\u2E21\u2E27\xF2\u2E01ash\u803B\xF8\u40F8l;\u6298i\u016C\u2E2F\u2E34de\u803B\xF5\u40F5es\u0100;a\u01DB\u2E3As;\u6A36ml\u803B\xF6\u40F6bar;\u633D\u0AE1\u2E5E\0\u2E7D\0\u2E80\u2E9D\0\u2EA2\u2EB9\0\0\u2ECB\u0E9C\0\u2F13\0\0\u2F2B\u2FBC\0\u2FC8r\u0200;ast\u0403\u2E67\u2E72\u0E85\u8100\xB6;l\u2E6D\u2E6E\u40B6le\xEC\u0403\u0269\u2E78\0\0\u2E7Bm;\u6AF3;\u6AFDy;\u443Fr\u0280cimpt\u2E8B\u2E8F\u2E93\u1865\u2E97nt;\u4025od;\u402Eil;\u6030enk;\u6031r;\uC000\u{1D52D}\u0180imo\u2EA8\u2EB0\u2EB4\u0100;v\u2EAD\u2EAE\u43C6;\u43D5ma\xF4\u0A76ne;\u660E\u0180;tv\u2EBF\u2EC0\u2EC8\u43C0chfork\xBB\u1FFD;\u43D6\u0100au\u2ECF\u2EDFn\u0100ck\u2ED5\u2EDDk\u0100;h\u21F4\u2EDB;\u610E\xF6\u21F4s\u0480;abcdemst\u2EF3\u2EF4\u1908\u2EF9\u2EFD\u2F04\u2F06\u2F0A\u2F0E\u402Bcir;\u6A23ir;\u6A22\u0100ou\u1D40\u2F02;\u6A25;\u6A72n\u80BB\xB1\u0E9Dim;\u6A26wo;\u6A27\u0180ipu\u2F19\u2F20\u2F25ntint;\u6A15f;\uC000\u{1D561}nd\u803B\xA3\u40A3\u0500;Eaceinosu\u0EC8\u2F3F\u2F41\u2F44\u2F47\u2F81\u2F89\u2F92\u2F7E\u2FB6;\u6AB3p;\u6AB7u\xE5\u0ED9\u0100;c\u0ECE\u2F4C\u0300;acens\u0EC8\u2F59\u2F5F\u2F66\u2F68\u2F7Eppro\xF8\u2F43urlye\xF1\u0ED9\xF1\u0ECE\u0180aes\u2F6F\u2F76\u2F7Approx;\u6AB9qq;\u6AB5im;\u62E8i\xED\u0EDFme\u0100;s\u2F88\u0EAE\u6032\u0180Eas\u2F78\u2F90\u2F7A\xF0\u2F75\u0180dfp\u0EEC\u2F99\u2FAF\u0180als\u2FA0\u2FA5\u2FAAlar;\u632Eine;\u6312urf;\u6313\u0100;t\u0EFB\u2FB4\xEF\u0EFBrel;\u62B0\u0100ci\u2FC0\u2FC5r;\uC000\u{1D4C5};\u43C8ncsp;\u6008\u0300fiopsu\u2FDA\u22E2\u2FDF\u2FE5\u2FEB\u2FF1r;\uC000\u{1D52E}pf;\uC000\u{1D562}rime;\u6057cr;\uC000\u{1D4C6}\u0180aeo\u2FF8\u3009\u3013t\u0100ei\u2FFE\u3005rnion\xF3\u06B0nt;\u6A16st\u0100;e\u3010\u3011\u403F\xF1\u1F19\xF4\u0F14\u0A80ABHabcdefhilmnoprstux\u3040\u3051\u3055\u3059\u30E0\u310E\u312B\u3147\u3162\u3172\u318E\u3206\u3215\u3224\u3229\u3258\u326E\u3272\u3290\u32B0\u32B7\u0180art\u3047\u304A\u304Cr\xF2\u10B3\xF2\u03DDail;\u691Car\xF2\u1C65ar;\u6964\u0380cdenqrt\u3068\u3075\u3078\u307F\u308F\u3094\u30CC\u0100eu\u306D\u3071;\uC000\u223D\u0331te;\u4155i\xE3\u116Emptyv;\u69B3g\u0200;del\u0FD1\u3089\u308B\u308D;\u6992;\u69A5\xE5\u0FD1uo\u803B\xBB\u40BBr\u0580;abcfhlpstw\u0FDC\u30AC\u30AF\u30B7\u30B9\u30BC\u30BE\u30C0\u30C3\u30C7\u30CAp;\u6975\u0100;f\u0FE0\u30B4s;\u6920;\u6933s;\u691E\xEB\u225D\xF0\u272El;\u6945im;\u6974l;\u61A3;\u619D\u0100ai\u30D1\u30D5il;\u691Ao\u0100;n\u30DB\u30DC\u6236al\xF3\u0F1E\u0180abr\u30E7\u30EA\u30EEr\xF2\u17E5rk;\u6773\u0100ak\u30F3\u30FDc\u0100ek\u30F9\u30FB;\u407D;\u405D\u0100es\u3102\u3104;\u698Cl\u0100du\u310A\u310C;\u698E;\u6990\u0200aeuy\u3117\u311C\u3127\u3129ron;\u4159\u0100di\u3121\u3125il;\u4157\xEC\u0FF2\xE2\u30FA;\u4440\u0200clqs\u3134\u3137\u313D\u3144a;\u6937dhar;\u6969uo\u0100;r\u020E\u020Dh;\u61B3\u0180acg\u314E\u315F\u0F44l\u0200;ips\u0F78\u3158\u315B\u109Cn\xE5\u10BBar\xF4\u0FA9t;\u65AD\u0180ilr\u3169\u1023\u316Esht;\u697D;\uC000\u{1D52F}\u0100ao\u3177\u3186r\u0100du\u317D\u317F\xBB\u047B\u0100;l\u1091\u3184;\u696C\u0100;v\u318B\u318C\u43C1;\u43F1\u0180gns\u3195\u31F9\u31FCht\u0300ahlrst\u31A4\u31B0\u31C2\u31D8\u31E4\u31EErrow\u0100;t\u0FDC\u31ADa\xE9\u30C8arpoon\u0100du\u31BB\u31BFow\xEE\u317Ep\xBB\u1092eft\u0100ah\u31CA\u31D0rrow\xF3\u0FEAarpoon\xF3\u0551ightarrows;\u61C9quigarro\xF7\u30CBhreetimes;\u62CCg;\u42DAingdotse\xF1\u1F32\u0180ahm\u320D\u3210\u3213r\xF2\u0FEAa\xF2\u0551;\u600Foust\u0100;a\u321E\u321F\u63B1che\xBB\u321Fmid;\u6AEE\u0200abpt\u3232\u323D\u3240\u3252\u0100nr\u3237\u323Ag;\u67EDr;\u61FEr\xEB\u1003\u0180afl\u3247\u324A\u324Er;\u6986;\uC000\u{1D563}us;\u6A2Eimes;\u6A35\u0100ap\u325D\u3267r\u0100;g\u3263\u3264\u4029t;\u6994olint;\u6A12ar\xF2\u31E3\u0200achq\u327B\u3280\u10BC\u3285quo;\u603Ar;\uC000\u{1D4C7}\u0100bu\u30FB\u328Ao\u0100;r\u0214\u0213\u0180hir\u3297\u329B\u32A0re\xE5\u31F8mes;\u62CAi\u0200;efl\u32AA\u1059\u1821\u32AB\u65B9tri;\u69CEluhar;\u6968;\u611E\u0D61\u32D5\u32DB\u32DF\u332C\u3338\u3371\0\u337A\u33A4\0\0\u33EC\u33F0\0\u3428\u3448\u345A\u34AD\u34B1\u34CA\u34F1\0\u3616\0\0\u3633cute;\u415Bqu\xEF\u27BA\u0500;Eaceinpsy\u11ED\u32F3\u32F5\u32FF\u3302\u330B\u330F\u331F\u3326\u3329;\u6AB4\u01F0\u32FA\0\u32FC;\u6AB8on;\u4161u\xE5\u11FE\u0100;d\u11F3\u3307il;\u415Frc;\u415D\u0180Eas\u3316\u3318\u331B;\u6AB6p;\u6ABAim;\u62E9olint;\u6A13i\xED\u1204;\u4441ot\u0180;be\u3334\u1D47\u3335\u62C5;\u6A66\u0380Aacmstx\u3346\u334A\u3357\u335B\u335E\u3363\u336Drr;\u61D8r\u0100hr\u3350\u3352\xEB\u2228\u0100;o\u0A36\u0A34t\u803B\xA7\u40A7i;\u403Bwar;\u6929m\u0100in\u3369\xF0nu\xF3\xF1t;\u6736r\u0100;o\u3376\u2055\uC000\u{1D530}\u0200acoy\u3382\u3386\u3391\u33A0rp;\u666F\u0100hy\u338B\u338Fcy;\u4449;\u4448rt\u026D\u3399\0\0\u339Ci\xE4\u1464ara\xEC\u2E6F\u803B\xAD\u40AD\u0100gm\u33A8\u33B4ma\u0180;fv\u33B1\u33B2\u33B2\u43C3;\u43C2\u0400;deglnpr\u12AB\u33C5\u33C9\u33CE\u33D6\u33DE\u33E1\u33E6ot;\u6A6A\u0100;q\u12B1\u12B0\u0100;E\u33D3\u33D4\u6A9E;\u6AA0\u0100;E\u33DB\u33DC\u6A9D;\u6A9Fe;\u6246lus;\u6A24arr;\u6972ar\xF2\u113D\u0200aeit\u33F8\u3408\u340F\u3417\u0100ls\u33FD\u3404lsetm\xE9\u336Ahp;\u6A33parsl;\u69E4\u0100dl\u1463\u3414e;\u6323\u0100;e\u341C\u341D\u6AAA\u0100;s\u3422\u3423\u6AAC;\uC000\u2AAC\uFE00\u0180flp\u342E\u3433\u3442tcy;\u444C\u0100;b\u3438\u3439\u402F\u0100;a\u343E\u343F\u69C4r;\u633Ff;\uC000\u{1D564}a\u0100dr\u344D\u0402es\u0100;u\u3454\u3455\u6660it\xBB\u3455\u0180csu\u3460\u3479\u349F\u0100au\u3465\u346Fp\u0100;s\u1188\u346B;\uC000\u2293\uFE00p\u0100;s\u11B4\u3475;\uC000\u2294\uFE00u\u0100bp\u347F\u348F\u0180;es\u1197\u119C\u3486et\u0100;e\u1197\u348D\xF1\u119D\u0180;es\u11A8\u11AD\u3496et\u0100;e\u11A8\u349D\xF1\u11AE\u0180;af\u117B\u34A6\u05B0r\u0165\u34AB\u05B1\xBB\u117Car\xF2\u1148\u0200cemt\u34B9\u34BE\u34C2\u34C5r;\uC000\u{1D4C8}tm\xEE\xF1i\xEC\u3415ar\xE6\u11BE\u0100ar\u34CE\u34D5r\u0100;f\u34D4\u17BF\u6606\u0100an\u34DA\u34EDight\u0100ep\u34E3\u34EApsilo\xEE\u1EE0h\xE9\u2EAFs\xBB\u2852\u0280bcmnp\u34FB\u355E\u1209\u358B\u358E\u0480;Edemnprs\u350E\u350F\u3511\u3515\u351E\u3523\u352C\u3531\u3536\u6282;\u6AC5ot;\u6ABD\u0100;d\u11DA\u351Aot;\u6AC3ult;\u6AC1\u0100Ee\u3528\u352A;\u6ACB;\u628Alus;\u6ABFarr;\u6979\u0180eiu\u353D\u3552\u3555t\u0180;en\u350E\u3545\u354Bq\u0100;q\u11DA\u350Feq\u0100;q\u352B\u3528m;\u6AC7\u0100bp\u355A\u355C;\u6AD5;\u6AD3c\u0300;acens\u11ED\u356C\u3572\u3579\u357B\u3326ppro\xF8\u32FAurlye\xF1\u11FE\xF1\u11F3\u0180aes\u3582\u3588\u331Bppro\xF8\u331Aq\xF1\u3317g;\u666A\u0680123;Edehlmnps\u35A9\u35AC\u35AF\u121C\u35B2\u35B4\u35C0\u35C9\u35D5\u35DA\u35DF\u35E8\u35ED\u803B\xB9\u40B9\u803B\xB2\u40B2\u803B\xB3\u40B3;\u6AC6\u0100os\u35B9\u35BCt;\u6ABEub;\u6AD8\u0100;d\u1222\u35C5ot;\u6AC4s\u0100ou\u35CF\u35D2l;\u67C9b;\u6AD7arr;\u697Bult;\u6AC2\u0100Ee\u35E4\u35E6;\u6ACC;\u628Blus;\u6AC0\u0180eiu\u35F4\u3609\u360Ct\u0180;en\u121C\u35FC\u3602q\u0100;q\u1222\u35B2eq\u0100;q\u35E7\u35E4m;\u6AC8\u0100bp\u3611\u3613;\u6AD4;\u6AD6\u0180Aan\u361C\u3620\u362Drr;\u61D9r\u0100hr\u3626\u3628\xEB\u222E\u0100;o\u0A2B\u0A29war;\u692Alig\u803B\xDF\u40DF\u0BE1\u3651\u365D\u3660\u12CE\u3673\u3679\0\u367E\u36C2\0\0\0\0\0\u36DB\u3703\0\u3709\u376C\0\0\0\u3787\u0272\u3656\0\0\u365Bget;\u6316;\u43C4r\xEB\u0E5F\u0180aey\u3666\u366B\u3670ron;\u4165dil;\u4163;\u4442lrec;\u6315r;\uC000\u{1D531}\u0200eiko\u3686\u369D\u36B5\u36BC\u01F2\u368B\0\u3691e\u01004f\u1284\u1281a\u0180;sv\u3698\u3699\u369B\u43B8ym;\u43D1\u0100cn\u36A2\u36B2k\u0100as\u36A8\u36AEppro\xF8\u12C1im\xBB\u12ACs\xF0\u129E\u0100as\u36BA\u36AE\xF0\u12C1rn\u803B\xFE\u40FE\u01EC\u031F\u36C6\u22E7es\u8180\xD7;bd\u36CF\u36D0\u36D8\u40D7\u0100;a\u190F\u36D5r;\u6A31;\u6A30\u0180eps\u36E1\u36E3\u3700\xE1\u2A4D\u0200;bcf\u0486\u36EC\u36F0\u36F4ot;\u6336ir;\u6AF1\u0100;o\u36F9\u36FC\uC000\u{1D565}rk;\u6ADA\xE1\u3362rime;\u6034\u0180aip\u370F\u3712\u3764d\xE5\u1248\u0380adempst\u3721\u374D\u3740\u3751\u3757\u375C\u375Fngle\u0280;dlqr\u3730\u3731\u3736\u3740\u3742\u65B5own\xBB\u1DBBeft\u0100;e\u2800\u373E\xF1\u092E;\u625Cight\u0100;e\u32AA\u374B\xF1\u105Aot;\u65ECinus;\u6A3Alus;\u6A39b;\u69CDime;\u6A3Bezium;\u63E2\u0180cht\u3772\u377D\u3781\u0100ry\u3777\u377B;\uC000\u{1D4C9};\u4446cy;\u445Brok;\u4167\u0100io\u378B\u378Ex\xF4\u1777head\u0100lr\u3797\u37A0eftarro\xF7\u084Fightarrow\xBB\u0F5D\u0900AHabcdfghlmoprstuw\u37D0\u37D3\u37D7\u37E4\u37F0\u37FC\u380E\u381C\u3823\u3834\u3851\u385D\u386B\u38A9\u38CC\u38D2\u38EA\u38F6r\xF2\u03EDar;\u6963\u0100cr\u37DC\u37E2ute\u803B\xFA\u40FA\xF2\u1150r\u01E3\u37EA\0\u37EDy;\u445Eve;\u416D\u0100iy\u37F5\u37FArc\u803B\xFB\u40FB;\u4443\u0180abh\u3803\u3806\u380Br\xF2\u13ADlac;\u4171a\xF2\u13C3\u0100ir\u3813\u3818sht;\u697E;\uC000\u{1D532}rave\u803B\xF9\u40F9\u0161\u3827\u3831r\u0100lr\u382C\u382E\xBB\u0957\xBB\u1083lk;\u6580\u0100ct\u3839\u384D\u026F\u383F\0\0\u384Arn\u0100;e\u3845\u3846\u631Cr\xBB\u3846op;\u630Fri;\u65F8\u0100al\u3856\u385Acr;\u416B\u80BB\xA8\u0349\u0100gp\u3862\u3866on;\u4173f;\uC000\u{1D566}\u0300adhlsu\u114B\u3878\u387D\u1372\u3891\u38A0own\xE1\u13B3arpoon\u0100lr\u3888\u388Cef\xF4\u382Digh\xF4\u382Fi\u0180;hl\u3899\u389A\u389C\u43C5\xBB\u13FAon\xBB\u389Aparrows;\u61C8\u0180cit\u38B0\u38C4\u38C8\u026F\u38B6\0\0\u38C1rn\u0100;e\u38BC\u38BD\u631Dr\xBB\u38BDop;\u630Eng;\u416Fri;\u65F9cr;\uC000\u{1D4CA}\u0180dir\u38D9\u38DD\u38E2ot;\u62F0lde;\u4169i\u0100;f\u3730\u38E8\xBB\u1813\u0100am\u38EF\u38F2r\xF2\u38A8l\u803B\xFC\u40FCangle;\u69A7\u0780ABDacdeflnoprsz\u391C\u391F\u3929\u392D\u39B5\u39B8\u39BD\u39DF\u39E4\u39E8\u39F3\u39F9\u39FD\u3A01\u3A20r\xF2\u03F7ar\u0100;v\u3926\u3927\u6AE8;\u6AE9as\xE8\u03E1\u0100nr\u3932\u3937grt;\u699C\u0380eknprst\u34E3\u3946\u394B\u3952\u395D\u3964\u3996app\xE1\u2415othin\xE7\u1E96\u0180hir\u34EB\u2EC8\u3959op\xF4\u2FB5\u0100;h\u13B7\u3962\xEF\u318D\u0100iu\u3969\u396Dgm\xE1\u33B3\u0100bp\u3972\u3984setneq\u0100;q\u397D\u3980\uC000\u228A\uFE00;\uC000\u2ACB\uFE00setneq\u0100;q\u398F\u3992\uC000\u228B\uFE00;\uC000\u2ACC\uFE00\u0100hr\u399B\u399Fet\xE1\u369Ciangle\u0100lr\u39AA\u39AFeft\xBB\u0925ight\xBB\u1051y;\u4432ash\xBB\u1036\u0180elr\u39C4\u39D2\u39D7\u0180;be\u2DEA\u39CB\u39CFar;\u62BBq;\u625Alip;\u62EE\u0100bt\u39DC\u1468a\xF2\u1469r;\uC000\u{1D533}tr\xE9\u39AEsu\u0100bp\u39EF\u39F1\xBB\u0D1C\xBB\u0D59pf;\uC000\u{1D567}ro\xF0\u0EFBtr\xE9\u39B4\u0100cu\u3A06\u3A0Br;\uC000\u{1D4CB}\u0100bp\u3A10\u3A18n\u0100Ee\u3980\u3A16\xBB\u397En\u0100Ee\u3992\u3A1E\xBB\u3990igzag;\u699A\u0380cefoprs\u3A36\u3A3B\u3A56\u3A5B\u3A54\u3A61\u3A6Airc;\u4175\u0100di\u3A40\u3A51\u0100bg\u3A45\u3A49ar;\u6A5Fe\u0100;q\u15FA\u3A4F;\u6259erp;\u6118r;\uC000\u{1D534}pf;\uC000\u{1D568}\u0100;e\u1479\u3A66at\xE8\u1479cr;\uC000\u{1D4CC}\u0AE3\u178E\u3A87\0\u3A8B\0\u3A90\u3A9B\0\0\u3A9D\u3AA8\u3AAB\u3AAF\0\0\u3AC3\u3ACE\0\u3AD8\u17DC\u17DFtr\xE9\u17D1r;\uC000\u{1D535}\u0100Aa\u3A94\u3A97r\xF2\u03C3r\xF2\u09F6;\u43BE\u0100Aa\u3AA1\u3AA4r\xF2\u03B8r\xF2\u09EBa\xF0\u2713is;\u62FB\u0180dpt\u17A4\u3AB5\u3ABE\u0100fl\u3ABA\u17A9;\uC000\u{1D569}im\xE5\u17B2\u0100Aa\u3AC7\u3ACAr\xF2\u03CEr\xF2\u0A01\u0100cq\u3AD2\u17B8r;\uC000\u{1D4CD}\u0100pt\u17D6\u3ADCr\xE9\u17D4\u0400acefiosu\u3AF0\u3AFD\u3B08\u3B0C\u3B11\u3B15\u3B1B\u3B21c\u0100uy\u3AF6\u3AFBte\u803B\xFD\u40FD;\u444F\u0100iy\u3B02\u3B06rc;\u4177;\u444Bn\u803B\xA5\u40A5r;\uC000\u{1D536}cy;\u4457pf;\uC000\u{1D56A}cr;\uC000\u{1D4CE}\u0100cm\u3B26\u3B29y;\u444El\u803B\xFF\u40FF\u0500acdefhiosw\u3B42\u3B48\u3B54\u3B58\u3B64\u3B69\u3B6D\u3B74\u3B7A\u3B80cute;\u417A\u0100ay\u3B4D\u3B52ron;\u417E;\u4437ot;\u417C\u0100et\u3B5D\u3B61tr\xE6\u155Fa;\u43B6r;\uC000\u{1D537}cy;\u4436grarr;\u61DDpf;\uC000\u{1D56B}cr;\uC000\u{1D4CF}\u0100jn\u3B85\u3B87;\u600Dj;\u600C'.split("").map(u=>u.charCodeAt(0)));var at=new Uint16Array("\u0200aglq	\x1B\u026D\0\0p;\u4026os;\u4027t;\u403Et;\u403Cuot;\u4022".split("").map(u=>u.charCodeAt(0)));var d0,Ii=new Map([[0,65533],[128,8364],[130,8218],[131,402],[132,8222],[133,8230],[134,8224],[135,8225],[136,710],[137,8240],[138,352],[139,8249],[140,338],[142,381],[145,8216],[146,8217],[147,8220],[148,8221],[149,8226],[150,8211],[151,8212],[152,732],[153,8482],[154,353],[155,8250],[156,339],[158,382],[159,376]]),me=(d0=String.fromCodePoint)!==null&&d0!==void 0?d0:function(u){let e="";return u>65535&&(u-=65536,e+=String.fromCharCode(u>>>10&1023|55296),u=56320|u&1023),e+=String.fromCharCode(u),e};function f0(u){var e;return u>=55296&&u<=57343||u>1114111?65533:(e=Ii.get(u))!==null&&e!==void 0?e:u}var q;(function(u){u[u.NUM=35]="NUM",u[u.SEMI=59]="SEMI",u[u.EQUALS=61]="EQUALS",u[u.ZERO=48]="ZERO",u[u.NINE=57]="NINE",u[u.LOWER_A=97]="LOWER_A",u[u.LOWER_F=102]="LOWER_F",u[u.LOWER_X=120]="LOWER_X",u[u.LOWER_Z=122]="LOWER_Z",u[u.UPPER_A=65]="UPPER_A",u[u.UPPER_F=70]="UPPER_F",u[u.UPPER_Z=90]="UPPER_Z"})(q||(q={}));var Ci=32,Iu;(function(u){u[u.VALUE_LENGTH=49152]="VALUE_LENGTH",u[u.BRANCH_LENGTH=16256]="BRANCH_LENGTH",u[u.JUMP_TABLE=127]="JUMP_TABLE"})(Iu||(Iu={}));function l0(u){return u>=q.ZERO&&u<=q.NINE}function Li(u){return u>=q.UPPER_A&&u<=q.UPPER_F||u>=q.LOWER_A&&u<=q.LOWER_F}function Di(u){return u>=q.UPPER_A&&u<=q.UPPER_Z||u>=q.LOWER_A&&u<=q.LOWER_Z||l0(u)}function Oi(u){return u===q.EQUALS||Di(u)}var F;(function(u){u[u.EntityStart=0]="EntityStart",u[u.NumericStart=1]="NumericStart",u[u.NumericDecimal=2]="NumericDecimal",u[u.NumericHex=3]="NumericHex",u[u.NamedEntity=4]="NamedEntity"})(F||(F={}));var iu;(function(u){u[u.Legacy=0]="Legacy",u[u.Strict=1]="Strict",u[u.Attribute=2]="Attribute"})(iu||(iu={}));var rt=class{constructor(e,t,a){this.decodeTree=e,this.emitCodePoint=t,this.errors=a,this.state=F.EntityStart,this.consumed=1,this.result=0,this.treeIndex=0,this.excess=1,this.decodeMode=iu.Strict}startEntity(e){this.decodeMode=e,this.state=F.EntityStart,this.result=0,this.treeIndex=0,this.excess=1,this.consumed=1}write(e,t){switch(this.state){case F.EntityStart:return e.charCodeAt(t)===q.NUM?(this.state=F.NumericStart,this.consumed+=1,this.stateNumericStart(e,t+1)):(this.state=F.NamedEntity,this.stateNamedEntity(e,t));case F.NumericStart:return this.stateNumericStart(e,t);case F.NumericDecimal:return this.stateNumericDecimal(e,t);case F.NumericHex:return this.stateNumericHex(e,t);case F.NamedEntity:return this.stateNamedEntity(e,t)}}stateNumericStart(e,t){return t>=e.length?-1:(e.charCodeAt(t)|Ci)===q.LOWER_X?(this.state=F.NumericHex,this.consumed+=1,this.stateNumericHex(e,t+1)):(this.state=F.NumericDecimal,this.stateNumericDecimal(e,t))}addToNumericResult(e,t,a,s){if(t!==a){let i=a-t;this.result=this.result*Math.pow(s,i)+Number.parseInt(e.substr(t,i),s),this.consumed+=i}}stateNumericHex(e,t){let a=t;for(;t<e.length;){let s=e.charCodeAt(t);if(l0(s)||Li(s))t+=1;else return this.addToNumericResult(e,a,t,16),this.emitNumericEntity(s,3)}return this.addToNumericResult(e,a,t,16),-1}stateNumericDecimal(e,t){let a=t;for(;t<e.length;){let s=e.charCodeAt(t);if(l0(s))t+=1;else return this.addToNumericResult(e,a,t,10),this.emitNumericEntity(s,2)}return this.addToNumericResult(e,a,t,10),-1}emitNumericEntity(e,t){var a;if(this.consumed<=t)return(a=this.errors)===null||a===void 0||a.absenceOfDigitsInNumericCharacterReference(this.consumed),0;if(e===q.SEMI)this.consumed+=1;else if(this.decodeMode===iu.Strict)return 0;return this.emitCodePoint(f0(this.result),this.consumed),this.errors&&(e!==q.SEMI&&this.errors.missingSemicolonAfterCharacterReference(),this.errors.validateNumericCharacterReference(this.result)),this.consumed}stateNamedEntity(e,t){let{decodeTree:a}=this,s=a[this.treeIndex],i=(s&Iu.VALUE_LENGTH)>>14;for(;t<e.length;t++,this.excess++){let n=e.charCodeAt(t);if(this.treeIndex=Ri(a,s,this.treeIndex+Math.max(1,i),n),this.treeIndex<0)return this.result===0||this.decodeMode===iu.Attribute&&(i===0||Oi(n))?0:this.emitNotTerminatedNamedEntity();if(s=a[this.treeIndex],i=(s&Iu.VALUE_LENGTH)>>14,i!==0){if(n===q.SEMI)return this.emitNamedEntityData(this.treeIndex,i,this.consumed+this.excess);this.decodeMode!==iu.Strict&&(this.result=this.treeIndex,this.consumed+=this.excess,this.excess=0)}}return-1}emitNotTerminatedNamedEntity(){var e;let{result:t,decodeTree:a}=this,s=(a[t]&Iu.VALUE_LENGTH)>>14;return this.emitNamedEntityData(t,s,this.consumed),(e=this.errors)===null||e===void 0||e.missingSemicolonAfterCharacterReference(),this.consumed}emitNamedEntityData(e,t,a){let{decodeTree:s}=this;return this.emitCodePoint(t===1?s[e]&~Iu.VALUE_LENGTH:s[e+1],a),t===3&&this.emitCodePoint(s[e+2],a),a}end(){var e;switch(this.state){case F.NamedEntity:return this.result!==0&&(this.decodeMode!==iu.Attribute||this.result===this.treeIndex)?this.emitNotTerminatedNamedEntity():0;case F.NumericDecimal:return this.emitNumericEntity(0,2);case F.NumericHex:return this.emitNumericEntity(0,3);case F.NumericStart:return(e=this.errors)===null||e===void 0||e.absenceOfDigitsInNumericCharacterReference(this.consumed),0;case F.EntityStart:return 0}}};function Ri(u,e,t,a){let s=(e&Iu.BRANCH_LENGTH)>>7,i=e&Iu.JUMP_TABLE;if(s===0)return i!==0&&a===i?t:-1;if(i){let l=a-i;return l<0||l>=s?-1:u[t+l]-1}let n=t,d=n+s-1;for(;n<=d;){let l=n+d>>>1,h=u[l];if(h<a)n=l+1;else if(h>a)d=l-1;else return u[l+s]}return-1}var C;(function(u){u[u.Tab=9]="Tab",u[u.NewLine=10]="NewLine",u[u.FormFeed=12]="FormFeed",u[u.CarriageReturn=13]="CarriageReturn",u[u.Space=32]="Space",u[u.ExclamationMark=33]="ExclamationMark",u[u.Number=35]="Number",u[u.Amp=38]="Amp",u[u.SingleQuote=39]="SingleQuote",u[u.DoubleQuote=34]="DoubleQuote",u[u.Dash=45]="Dash",u[u.Slash=47]="Slash",u[u.Zero=48]="Zero",u[u.Nine=57]="Nine",u[u.Semi=59]="Semi",u[u.Lt=60]="Lt",u[u.Eq=61]="Eq",u[u.Gt=62]="Gt",u[u.Questionmark=63]="Questionmark",u[u.UpperA=65]="UpperA",u[u.LowerA=97]="LowerA",u[u.UpperF=70]="UpperF",u[u.LowerF=102]="LowerF",u[u.UpperZ=90]="UpperZ",u[u.LowerZ=122]="LowerZ",u[u.LowerX=120]="LowerX",u[u.OpeningSquareBracket=91]="OpeningSquareBracket"})(C||(C={}));var T;(function(u){u[u.Text=1]="Text",u[u.BeforeTagName=2]="BeforeTagName",u[u.InTagName=3]="InTagName",u[u.InSelfClosingTag=4]="InSelfClosingTag",u[u.BeforeClosingTagName=5]="BeforeClosingTagName",u[u.InClosingTagName=6]="InClosingTagName",u[u.AfterClosingTagName=7]="AfterClosingTagName",u[u.BeforeAttributeName=8]="BeforeAttributeName",u[u.InAttributeName=9]="InAttributeName",u[u.AfterAttributeName=10]="AfterAttributeName",u[u.BeforeAttributeValue=11]="BeforeAttributeValue",u[u.InAttributeValueDq=12]="InAttributeValueDq",u[u.InAttributeValueSq=13]="InAttributeValueSq",u[u.InAttributeValueNq=14]="InAttributeValueNq",u[u.BeforeDeclaration=15]="BeforeDeclaration",u[u.InDeclaration=16]="InDeclaration",u[u.InProcessingInstruction=17]="InProcessingInstruction",u[u.BeforeComment=18]="BeforeComment",u[u.CDATASequence=19]="CDATASequence",u[u.InSpecialComment=20]="InSpecialComment",u[u.InCommentLike=21]="InCommentLike",u[u.BeforeSpecialS=22]="BeforeSpecialS",u[u.BeforeSpecialT=23]="BeforeSpecialT",u[u.SpecialStartSequence=24]="SpecialStartSequence",u[u.InSpecialTag=25]="InSpecialTag",u[u.InEntity=26]="InEntity"})(T||(T={}));function Eu(u){return u===C.Space||u===C.NewLine||u===C.Tab||u===C.FormFeed||u===C.CarriageReturn}function st(u){return u===C.Slash||u===C.Gt||Eu(u)}function yi(u){return u>=C.LowerA&&u<=C.LowerZ||u>=C.UpperA&&u<=C.UpperZ}var au;(function(u){u[u.NoValue=0]="NoValue",u[u.Unquoted=1]="Unquoted",u[u.Single=2]="Single",u[u.Double=3]="Double"})(au||(au={}));var B={Cdata:new Uint8Array([67,68,65,84,65,91]),CdataEnd:new Uint8Array([93,93,62]),CommentEnd:new Uint8Array([45,45,62]),ScriptEnd:new Uint8Array([60,47,115,99,114,105,112,116]),StyleEnd:new Uint8Array([60,47,115,116,121,108,101]),TitleEnd:new Uint8Array([60,47,116,105,116,108,101]),TextareaEnd:new Uint8Array([60,47,116,101,120,116,97,114,101,97]),XmpEnd:new Uint8Array([60,47,120,109,112])},zu=class{constructor({xmlMode:e=!1,decodeEntities:t=!0},a){this.cbs=a,this.state=T.Text,this.buffer="",this.sectionStart=0,this.index=0,this.entityStart=0,this.baseState=T.Text,this.isSpecial=!1,this.running=!0,this.offset=0,this.currentSequence=void 0,this.sequenceIndex=0,this.xmlMode=e,this.decodeEntities=t,this.entityDecoder=new rt(e?at:tt,(s,i)=>this.emitCodePoint(s,i))}reset(){this.state=T.Text,this.buffer="",this.sectionStart=0,this.index=0,this.baseState=T.Text,this.currentSequence=void 0,this.running=!0,this.offset=0}write(e){this.offset+=this.buffer.length,this.buffer=e,this.parse()}end(){this.running&&this.finish()}pause(){this.running=!1}resume(){this.running=!0,this.index<this.buffer.length+this.offset&&this.parse()}stateText(e){e===C.Lt||!this.decodeEntities&&this.fastForwardTo(C.Lt)?(this.index>this.sectionStart&&this.cbs.ontext(this.sectionStart,this.index),this.state=T.BeforeTagName,this.sectionStart=this.index):this.decodeEntities&&e===C.Amp&&this.startEntity()}stateSpecialStartSequence(e){let t=this.sequenceIndex===this.currentSequence.length;if(!(t?st(e):(e|32)===this.currentSequence[this.sequenceIndex]))this.isSpecial=!1;else if(!t){this.sequenceIndex++;return}this.sequenceIndex=0,this.state=T.InTagName,this.stateInTagName(e)}stateInSpecialTag(e){if(this.sequenceIndex===this.currentSequence.length){if(e===C.Gt||Eu(e)){let t=this.index-this.currentSequence.length;if(this.sectionStart<t){let a=this.index;this.index=t,this.cbs.ontext(this.sectionStart,t),this.index=a}this.isSpecial=!1,this.sectionStart=t+2,this.stateInClosingTagName(e);return}this.sequenceIndex=0}(e|32)===this.currentSequence[this.sequenceIndex]?this.sequenceIndex+=1:this.sequenceIndex===0?this.currentSequence===B.TitleEnd?this.decodeEntities&&e===C.Amp&&this.startEntity():this.fastForwardTo(C.Lt)&&(this.sequenceIndex=1):this.sequenceIndex=Number(e===C.Lt)}stateCDATASequence(e){e===B.Cdata[this.sequenceIndex]?++this.sequenceIndex===B.Cdata.length&&(this.state=T.InCommentLike,this.currentSequence=B.CdataEnd,this.sequenceIndex=0,this.sectionStart=this.index+1):(this.sequenceIndex=0,this.state=T.InDeclaration,this.stateInDeclaration(e))}fastForwardTo(e){for(;++this.index<this.buffer.length+this.offset;)if(this.buffer.charCodeAt(this.index-this.offset)===e)return!0;return this.index=this.buffer.length+this.offset-1,!1}stateInCommentLike(e){e===this.currentSequence[this.sequenceIndex]?++this.sequenceIndex===this.currentSequence.length&&(this.currentSequence===B.CdataEnd?this.cbs.oncdata(this.sectionStart,this.index,2):this.cbs.oncomment(this.sectionStart,this.index,2),this.sequenceIndex=0,this.sectionStart=this.index+1,this.state=T.Text):this.sequenceIndex===0?this.fastForwardTo(this.currentSequence[0])&&(this.sequenceIndex=1):e!==this.currentSequence[this.sequenceIndex-1]&&(this.sequenceIndex=0)}isTagStartChar(e){return this.xmlMode?!st(e):yi(e)}startSpecial(e,t){this.isSpecial=!0,this.currentSequence=e,this.sequenceIndex=t,this.state=T.SpecialStartSequence}stateBeforeTagName(e){if(e===C.ExclamationMark)this.state=T.BeforeDeclaration,this.sectionStart=this.index+1;else if(e===C.Questionmark)this.state=T.InProcessingInstruction,this.sectionStart=this.index+1;else if(this.isTagStartChar(e)){let t=e|32;this.sectionStart=this.index,this.xmlMode?this.state=T.InTagName:t===B.ScriptEnd[2]?this.state=T.BeforeSpecialS:t===B.TitleEnd[2]||t===B.XmpEnd[2]?this.state=T.BeforeSpecialT:this.state=T.InTagName}else e===C.Slash?this.state=T.BeforeClosingTagName:(this.state=T.Text,this.stateText(e))}stateInTagName(e){st(e)&&(this.cbs.onopentagname(this.sectionStart,this.index),this.sectionStart=-1,this.state=T.BeforeAttributeName,this.stateBeforeAttributeName(e))}stateBeforeClosingTagName(e){Eu(e)||(e===C.Gt?this.state=T.Text:(this.state=this.isTagStartChar(e)?T.InClosingTagName:T.InSpecialComment,this.sectionStart=this.index))}stateInClosingTagName(e){(e===C.Gt||Eu(e))&&(this.cbs.onclosetag(this.sectionStart,this.index),this.sectionStart=-1,this.state=T.AfterClosingTagName,this.stateAfterClosingTagName(e))}stateAfterClosingTagName(e){(e===C.Gt||this.fastForwardTo(C.Gt))&&(this.state=T.Text,this.sectionStart=this.index+1)}stateBeforeAttributeName(e){e===C.Gt?(this.cbs.onopentagend(this.index),this.isSpecial?(this.state=T.InSpecialTag,this.sequenceIndex=0):this.state=T.Text,this.sectionStart=this.index+1):e===C.Slash?this.state=T.InSelfClosingTag:Eu(e)||(this.state=T.InAttributeName,this.sectionStart=this.index)}stateInSelfClosingTag(e){e===C.Gt?(this.cbs.onselfclosingtag(this.index),this.state=T.Text,this.sectionStart=this.index+1,this.isSpecial=!1):Eu(e)||(this.state=T.BeforeAttributeName,this.stateBeforeAttributeName(e))}stateInAttributeName(e){(e===C.Eq||st(e))&&(this.cbs.onattribname(this.sectionStart,this.index),this.sectionStart=this.index,this.state=T.AfterAttributeName,this.stateAfterAttributeName(e))}stateAfterAttributeName(e){e===C.Eq?this.state=T.BeforeAttributeValue:e===C.Slash||e===C.Gt?(this.cbs.onattribend(au.NoValue,this.sectionStart),this.sectionStart=-1,this.state=T.BeforeAttributeName,this.stateBeforeAttributeName(e)):Eu(e)||(this.cbs.onattribend(au.NoValue,this.sectionStart),this.state=T.InAttributeName,this.sectionStart=this.index)}stateBeforeAttributeValue(e){e===C.DoubleQuote?(this.state=T.InAttributeValueDq,this.sectionStart=this.index+1):e===C.SingleQuote?(this.state=T.InAttributeValueSq,this.sectionStart=this.index+1):Eu(e)||(this.sectionStart=this.index,this.state=T.InAttributeValueNq,this.stateInAttributeValueNoQuotes(e))}handleInAttributeValue(e,t){e===t||!this.decodeEntities&&this.fastForwardTo(t)?(this.cbs.onattribdata(this.sectionStart,this.index),this.sectionStart=-1,this.cbs.onattribend(t===C.DoubleQuote?au.Double:au.Single,this.index+1),this.state=T.BeforeAttributeName):this.decodeEntities&&e===C.Amp&&this.startEntity()}stateInAttributeValueDoubleQuotes(e){this.handleInAttributeValue(e,C.DoubleQuote)}stateInAttributeValueSingleQuotes(e){this.handleInAttributeValue(e,C.SingleQuote)}stateInAttributeValueNoQuotes(e){Eu(e)||e===C.Gt?(this.cbs.onattribdata(this.sectionStart,this.index),this.sectionStart=-1,this.cbs.onattribend(au.Unquoted,this.index),this.state=T.BeforeAttributeName,this.stateBeforeAttributeName(e)):this.decodeEntities&&e===C.Amp&&this.startEntity()}stateBeforeDeclaration(e){e===C.OpeningSquareBracket?(this.state=T.CDATASequence,this.sequenceIndex=0):this.state=e===C.Dash?T.BeforeComment:T.InDeclaration}stateInDeclaration(e){(e===C.Gt||this.fastForwardTo(C.Gt))&&(this.cbs.ondeclaration(this.sectionStart,this.index),this.state=T.Text,this.sectionStart=this.index+1)}stateInProcessingInstruction(e){(e===C.Gt||this.fastForwardTo(C.Gt))&&(this.cbs.onprocessinginstruction(this.sectionStart,this.index),this.state=T.Text,this.sectionStart=this.index+1)}stateBeforeComment(e){e===C.Dash?(this.state=T.InCommentLike,this.currentSequence=B.CommentEnd,this.sequenceIndex=2,this.sectionStart=this.index+1):this.state=T.InDeclaration}stateInSpecialComment(e){(e===C.Gt||this.fastForwardTo(C.Gt))&&(this.cbs.oncomment(this.sectionStart,this.index,0),this.state=T.Text,this.sectionStart=this.index+1)}stateBeforeSpecialS(e){let t=e|32;t===B.ScriptEnd[3]?this.startSpecial(B.ScriptEnd,4):t===B.StyleEnd[3]?this.startSpecial(B.StyleEnd,4):(this.state=T.InTagName,this.stateInTagName(e))}stateBeforeSpecialT(e){switch(e|32){case B.TitleEnd[3]:{this.startSpecial(B.TitleEnd,4);break}case B.TextareaEnd[3]:{this.startSpecial(B.TextareaEnd,4);break}case B.XmpEnd[3]:{this.startSpecial(B.XmpEnd,4);break}default:this.state=T.InTagName,this.stateInTagName(e)}}startEntity(){this.baseState=this.state,this.state=T.InEntity,this.entityStart=this.index,this.entityDecoder.startEntity(this.xmlMode?iu.Strict:this.baseState===T.Text||this.baseState===T.InSpecialTag?iu.Legacy:iu.Attribute)}stateInEntity(){let e=this.entityDecoder.write(this.buffer,this.index-this.offset);e>=0?(this.state=this.baseState,e===0&&(this.index=this.entityStart)):this.index=this.offset+this.buffer.length-1}cleanup(){this.running&&this.sectionStart!==this.index&&(this.state===T.Text||this.state===T.InSpecialTag&&this.sequenceIndex===0?(this.cbs.ontext(this.sectionStart,this.index),this.sectionStart=this.index):(this.state===T.InAttributeValueDq||this.state===T.InAttributeValueSq||this.state===T.InAttributeValueNq)&&(this.cbs.onattribdata(this.sectionStart,this.index),this.sectionStart=this.index))}shouldContinue(){return this.index<this.buffer.length+this.offset&&this.running}parse(){for(;this.shouldContinue();){let e=this.buffer.charCodeAt(this.index-this.offset);switch(this.state){case T.Text:{this.stateText(e);break}case T.SpecialStartSequence:{this.stateSpecialStartSequence(e);break}case T.InSpecialTag:{this.stateInSpecialTag(e);break}case T.CDATASequence:{this.stateCDATASequence(e);break}case T.InAttributeValueDq:{this.stateInAttributeValueDoubleQuotes(e);break}case T.InAttributeName:{this.stateInAttributeName(e);break}case T.InCommentLike:{this.stateInCommentLike(e);break}case T.InSpecialComment:{this.stateInSpecialComment(e);break}case T.BeforeAttributeName:{this.stateBeforeAttributeName(e);break}case T.InTagName:{this.stateInTagName(e);break}case T.InClosingTagName:{this.stateInClosingTagName(e);break}case T.BeforeTagName:{this.stateBeforeTagName(e);break}case T.AfterAttributeName:{this.stateAfterAttributeName(e);break}case T.InAttributeValueSq:{this.stateInAttributeValueSingleQuotes(e);break}case T.BeforeAttributeValue:{this.stateBeforeAttributeValue(e);break}case T.BeforeClosingTagName:{this.stateBeforeClosingTagName(e);break}case T.AfterClosingTagName:{this.stateAfterClosingTagName(e);break}case T.BeforeSpecialS:{this.stateBeforeSpecialS(e);break}case T.BeforeSpecialT:{this.stateBeforeSpecialT(e);break}case T.InAttributeValueNq:{this.stateInAttributeValueNoQuotes(e);break}case T.InSelfClosingTag:{this.stateInSelfClosingTag(e);break}case T.InDeclaration:{this.stateInDeclaration(e);break}case T.BeforeDeclaration:{this.stateBeforeDeclaration(e);break}case T.BeforeComment:{this.stateBeforeComment(e);break}case T.InProcessingInstruction:{this.stateInProcessingInstruction(e);break}case T.InEntity:{this.stateInEntity();break}}this.index++}this.cleanup()}finish(){this.state===T.InEntity&&(this.entityDecoder.end(),this.state=this.baseState),this.handleTrailingData(),this.cbs.onend()}handleTrailingData(){let e=this.buffer.length+this.offset;this.sectionStart>=e||(this.state===T.InCommentLike?this.currentSequence===B.CdataEnd?this.cbs.oncdata(this.sectionStart,e,0):this.cbs.oncomment(this.sectionStart,e,0):this.state===T.InTagName||this.state===T.BeforeAttributeName||this.state===T.BeforeAttributeValue||this.state===T.AfterAttributeName||this.state===T.InAttributeName||this.state===T.InAttributeValueSq||this.state===T.InAttributeValueDq||this.state===T.InAttributeValueNq||this.state===T.InClosingTagName||this.cbs.ontext(this.sectionStart,e))}emitCodePoint(e,t){this.baseState!==T.Text&&this.baseState!==T.InSpecialTag?(this.sectionStart<this.entityStart&&this.cbs.onattribdata(this.sectionStart,this.entityStart),this.sectionStart=this.entityStart+t,this.index=this.sectionStart-1,this.cbs.onattribentity(e)):(this.sectionStart<this.entityStart&&this.cbs.ontext(this.sectionStart,this.entityStart),this.sectionStart=this.entityStart+t,this.index=this.sectionStart-1,this.cbs.ontextentity(e,this.sectionStart))}};var $u=new Set(["input","option","optgroup","select","button","datalist","textarea"]),y=new Set(["p"]),Ua=new Set(["thead","tbody"]),Ha=new Set(["dd","dt"]),Fa=new Set(["rt","rp"]),Pi=new Map([["tr",new Set(["tr","th","td"])],["th",new Set(["th"])],["td",new Set(["thead","th","td"])],["body",new Set(["head","link","script"])],["li",new Set(["li"])],["p",y],["h1",y],["h2",y],["h3",y],["h4",y],["h5",y],["h6",y],["select",$u],["input",$u],["output",$u],["button",$u],["datalist",$u],["textarea",$u],["option",new Set(["option"])],["optgroup",new Set(["optgroup","option"])],["dd",Ha],["dt",Ha],["address",y],["article",y],["aside",y],["blockquote",y],["details",y],["div",y],["dl",y],["fieldset",y],["figcaption",y],["figure",y],["footer",y],["form",y],["header",y],["hr",y],["main",y],["nav",y],["ol",y],["pre",y],["section",y],["table",y],["ul",y],["rt",Fa],["rp",Fa],["tbody",Ua],["tfoot",Ua]]),Mi=new Set(["area","base","basefont","br","col","command","embed","frame","hr","img","input","isindex","keygen","link","meta","param","source","track","wbr"]),qa=new Set(["math","svg"]),Ya=new Set(["mi","mo","mn","ms","mtext","annotation-xml","foreignobject","desc","title"]),ki=/\s|\//,Ee=class{constructor(e,t={}){var a,s,i,n,d,l;this.options=t,this.startIndex=0,this.endIndex=0,this.openTagStart=0,this.tagname="",this.attribname="",this.attribvalue="",this.attribs=null,this.stack=[],this.buffers=[],this.bufferOffset=0,this.writeIndex=0,this.ended=!1,this.cbs=e!=null?e:{},this.htmlMode=!this.options.xmlMode,this.lowerCaseTagNames=(a=t.lowerCaseTags)!==null&&a!==void 0?a:this.htmlMode,this.lowerCaseAttributeNames=(s=t.lowerCaseAttributeNames)!==null&&s!==void 0?s:this.htmlMode,this.recognizeSelfClosing=(i=t.recognizeSelfClosing)!==null&&i!==void 0?i:!this.htmlMode,this.tokenizer=new((n=t.Tokenizer)!==null&&n!==void 0?n:zu)(this.options,this),this.foreignContext=[!this.htmlMode],(l=(d=this.cbs).onparserinit)===null||l===void 0||l.call(d,this)}ontext(e,t){var a,s;let i=this.getSlice(e,t);this.endIndex=t-1,(s=(a=this.cbs).ontext)===null||s===void 0||s.call(a,i),this.startIndex=t}ontextentity(e,t){var a,s;this.endIndex=t-1,(s=(a=this.cbs).ontext)===null||s===void 0||s.call(a,me(e)),this.startIndex=t}isVoidElement(e){return this.htmlMode&&Mi.has(e)}onopentagname(e,t){this.endIndex=t;let a=this.getSlice(e,t);this.lowerCaseTagNames&&(a=a.toLowerCase()),this.emitOpenTag(a)}emitOpenTag(e){var t,a,s,i;this.openTagStart=this.startIndex,this.tagname=e;let n=this.htmlMode&&Pi.get(e);if(n)for(;this.stack.length>0&&n.has(this.stack[0]);){let d=this.stack.shift();(a=(t=this.cbs).onclosetag)===null||a===void 0||a.call(t,d,!0)}this.isVoidElement(e)||(this.stack.unshift(e),this.htmlMode&&(qa.has(e)?this.foreignContext.unshift(!0):Ya.has(e)&&this.foreignContext.unshift(!1))),(i=(s=this.cbs).onopentagname)===null||i===void 0||i.call(s,e),this.cbs.onopentag&&(this.attribs={})}endOpenTag(e){var t,a;this.startIndex=this.openTagStart,this.attribs&&((a=(t=this.cbs).onopentag)===null||a===void 0||a.call(t,this.tagname,this.attribs,e),this.attribs=null),this.cbs.onclosetag&&this.isVoidElement(this.tagname)&&this.cbs.onclosetag(this.tagname,!0),this.tagname=""}onopentagend(e){this.endIndex=e,this.endOpenTag(!1),this.startIndex=e+1}onclosetag(e,t){var a,s,i,n,d,l,h,p;this.endIndex=t;let x=this.getSlice(e,t);if(this.lowerCaseTagNames&&(x=x.toLowerCase()),this.htmlMode&&(qa.has(x)||Ya.has(x))&&this.foreignContext.shift(),this.isVoidElement(x))this.htmlMode&&x==="br"&&((n=(i=this.cbs).onopentagname)===null||n===void 0||n.call(i,"br"),(l=(d=this.cbs).onopentag)===null||l===void 0||l.call(d,"br",{},!0),(p=(h=this.cbs).onclosetag)===null||p===void 0||p.call(h,"br",!1));else{let A=this.stack.indexOf(x);if(A!==-1)for(let S=0;S<=A;S++){let _=this.stack.shift();(s=(a=this.cbs).onclosetag)===null||s===void 0||s.call(a,_,S!==A)}else this.htmlMode&&x==="p"&&(this.emitOpenTag("p"),this.closeCurrentTag(!0))}this.startIndex=t+1}onselfclosingtag(e){this.endIndex=e,this.recognizeSelfClosing||this.foreignContext[0]?(this.closeCurrentTag(!1),this.startIndex=e+1):this.onopentagend(e)}closeCurrentTag(e){var t,a;let s=this.tagname;this.endOpenTag(e),this.stack[0]===s&&((a=(t=this.cbs).onclosetag)===null||a===void 0||a.call(t,s,!e),this.stack.shift())}onattribname(e,t){this.startIndex=e;let a=this.getSlice(e,t);this.attribname=this.lowerCaseAttributeNames?a.toLowerCase():a}onattribdata(e,t){this.attribvalue+=this.getSlice(e,t)}onattribentity(e){this.attribvalue+=me(e)}onattribend(e,t){var a,s;this.endIndex=t,(s=(a=this.cbs).onattribute)===null||s===void 0||s.call(a,this.attribname,this.attribvalue,e===au.Double?'"':e===au.Single?"'":e===au.NoValue?void 0:null),this.attribs&&!Object.prototype.hasOwnProperty.call(this.attribs,this.attribname)&&(this.attribs[this.attribname]=this.attribvalue),this.attribvalue=""}getInstructionName(e){let t=e.search(ki),a=t<0?e:e.substr(0,t);return this.lowerCaseTagNames&&(a=a.toLowerCase()),a}ondeclaration(e,t){this.endIndex=t;let a=this.getSlice(e,t);if(this.cbs.onprocessinginstruction){let s=this.getInstructionName(a);this.cbs.onprocessinginstruction(`!${s}`,`!${a}`)}this.startIndex=t+1}onprocessinginstruction(e,t){this.endIndex=t;let a=this.getSlice(e,t);if(this.cbs.onprocessinginstruction){let s=this.getInstructionName(a);this.cbs.onprocessinginstruction(`?${s}`,`?${a}`)}this.startIndex=t+1}oncomment(e,t,a){var s,i,n,d;this.endIndex=t,(i=(s=this.cbs).oncomment)===null||i===void 0||i.call(s,this.getSlice(e,t-a)),(d=(n=this.cbs).oncommentend)===null||d===void 0||d.call(n),this.startIndex=t+1}oncdata(e,t,a){var s,i,n,d,l,h,p,x,A,S;this.endIndex=t;let _=this.getSlice(e,t-a);!this.htmlMode||this.options.recognizeCDATA?((i=(s=this.cbs).oncdatastart)===null||i===void 0||i.call(s),(d=(n=this.cbs).ontext)===null||d===void 0||d.call(n,_),(h=(l=this.cbs).oncdataend)===null||h===void 0||h.call(l)):((x=(p=this.cbs).oncomment)===null||x===void 0||x.call(p,`[CDATA[${_}]]`),(S=(A=this.cbs).oncommentend)===null||S===void 0||S.call(A)),this.startIndex=t+1}onend(){var e,t;if(this.cbs.onclosetag){this.endIndex=this.startIndex;for(let a=0;a<this.stack.length;a++)this.cbs.onclosetag(this.stack[a],!0)}(t=(e=this.cbs).onend)===null||t===void 0||t.call(e)}reset(){var e,t,a,s;(t=(e=this.cbs).onreset)===null||t===void 0||t.call(e),this.tokenizer.reset(),this.tagname="",this.attribname="",this.attribs=null,this.stack.length=0,this.startIndex=0,this.endIndex=0,(s=(a=this.cbs).onparserinit)===null||s===void 0||s.call(a,this),this.buffers.length=0,this.foreignContext.length=0,this.foreignContext.unshift(!this.htmlMode),this.bufferOffset=0,this.writeIndex=0,this.ended=!1}parseComplete(e){this.reset(),this.end(e)}getSlice(e,t){for(;e-this.bufferOffset>=this.buffers[0].length;)this.shiftBuffer();let a=this.buffers[0].slice(e-this.bufferOffset,t-this.bufferOffset);for(;t-this.bufferOffset>this.buffers[0].length;)this.shiftBuffer(),a+=this.buffers[0].slice(0,t-this.bufferOffset);return a}shiftBuffer(){this.bufferOffset+=this.buffers[0].length,this.writeIndex--,this.buffers.shift()}write(e){var t,a;if(this.ended){(a=(t=this.cbs).onerror)===null||a===void 0||a.call(t,new Error(".write() after done!"));return}this.buffers.push(e),this.tokenizer.running&&(this.tokenizer.write(e),this.writeIndex++)}end(e){var t,a;if(this.ended){(a=(t=this.cbs).onerror)===null||a===void 0||a.call(t,new Error(".end() after done!"));return}e&&this.write(e),this.ended=!0,this.tokenizer.end()}pause(){this.tokenizer.pause()}resume(){for(this.tokenizer.resume();this.tokenizer.running&&this.writeIndex<this.buffers.length;)this.tokenizer.write(this.buffers[this.writeIndex++]);this.ended&&this.tokenizer.end()}parseChunk(e){this.write(e)}done(e){this.end(e)}};function Va(u,e){let t=new Xu(void 0,e);return new Ee(t,e).end(u),t.root}var b0,pe=(b0=Object.hasOwn)!==null&&b0!==void 0?b0:(u,e)=>Object.prototype.hasOwnProperty.call(u,e),Te=/\s+/,m0="data-",E0=/^(?:autofocus|autoplay|async|checked|controls|defer|disabled|hidden|loop|multiple|open|readonly|required|scoped|selected)$/i,wi=/^{[^]*}$|^\[[^]*]$/;function it(u,e,t){var a;if(!(!u||!N(u))){if((a=u.attribs)!==null&&a!==void 0||(u.attribs={}),!e)return u.attribs;if(pe(u.attribs,e))return!t&&E0.test(e)?e:u.attribs[e];if(u.name==="option"&&e==="value")return Fu(u.children);if(u.name==="input"&&(u.attribs.type==="radio"||u.attribs.type==="checkbox")&&e==="value")return"on"}}function Zu(u,e,t){t===null?Xa(u,e):u.attribs[e]=`${t}`}function Bi(u,e){if(typeof u=="object"||e!==void 0){if(typeof e=="function"){if(typeof u!="string")throw new Error("Bad combination of arguments.");return P(this,(t,a)=>{N(t)&&Zu(t,u,e.call(t,a,t.attribs[u]))})}return P(this,t=>{if(N(t))if(typeof u=="object")for(let a of Object.keys(u)){let s=u[a];Zu(t,a,s)}else Zu(t,u,e)})}return arguments.length>1?this:it(this[0],u,this.options.xmlMode)}function Ga(u,e,t){return e in u?u[e]:!t&&E0.test(e)?it(u,e,!1)!==void 0:it(u,e,t)}function h0(u,e,t,a){e in u?u[e]=t:Zu(u,e,!a&&E0.test(e)?t?"":null:`${t}`)}function vi(u,e){var t;if(typeof u=="string"&&e===void 0){let a=this[0];if(!a)return;switch(u){case"style":{let s=this.css(),i=Object.keys(s);for(let n=0;n<i.length;n++)s[n]=i[n];return s.length=i.length,s}case"tagName":case"nodeName":return N(a)?a.name.toUpperCase():void 0;case"href":case"src":{if(!N(a))return;let s=(t=a.attribs)===null||t===void 0?void 0:t[u];return typeof URL!="undefined"&&(u==="href"&&(a.tagName==="a"||a.tagName==="link")||u==="src"&&(a.tagName==="img"||a.tagName==="iframe"||a.tagName==="audio"||a.tagName==="video"||a.tagName==="source"))&&s!==void 0&&this.options.baseURI?new URL(s,this.options.baseURI).href:s}case"innerText":return le(a);case"textContent":return bu(a);case"outerHTML":return a.type===Z.Root?this.html():this.clone().wrap("<container />").parent().html();case"innerHTML":return this.html();default:return N(a)?Ga(a,u,this.options.xmlMode):void 0}}if(typeof u=="object"||e!==void 0){if(typeof e=="function"){if(typeof u=="object")throw new TypeError("Bad combination of arguments.");return P(this,(a,s)=>{N(a)&&h0(a,u,e.call(a,s,Ga(a,u,this.options.xmlMode)),this.options.xmlMode)})}return P(this,a=>{if(N(a))if(typeof u=="object")for(let s of Object.keys(u)){let i=u[s];h0(a,s,i,this.options.xmlMode)}else h0(a,u,e,this.options.xmlMode)})}}function Wa(u,e,t){var a;(a=u.data)!==null&&a!==void 0||(u.data={}),typeof e=="object"?Object.assign(u.data,e):typeof e=="string"&&t!==void 0&&(u.data[e]=t)}function Ui(u){for(let e of Object.keys(u.attribs)){if(!e.startsWith(m0))continue;let t=Ba(e.slice(m0.length));pe(u.data,t)||(u.data[t]=Qa(u.attribs[e]))}return u.data}function Hi(u,e){let t=m0+va(e),a=u.data;if(pe(a,e))return a[e];if(pe(u.attribs,t))return a[e]=Qa(u.attribs[t])}function Qa(u){if(u==="null")return null;if(u==="true")return!0;if(u==="false")return!1;let e=Number(u);if(u===String(e))return e;if(wi.test(u))try{return JSON.parse(u)}catch(t){}return u}function Fi(u,e){var t;let a=this[0];if(!a||!N(a))return;let s=a;return(t=s.data)!==null&&t!==void 0||(s.data={}),u==null?Ui(s):typeof u=="object"||e!==void 0?(P(this,i=>{N(i)&&(typeof u=="object"?Wa(i,u):Wa(i,u,e))}),this):Hi(s,u)}function qi(u){let e=arguments.length===0,t=this[0];if(!t||!N(t))return e?void 0:this;switch(t.name){case"textarea":return this.text(u);case"select":{let a=this.find("option:selected");if(!e){if(this.attr("multiple")==null&&typeof u=="object")return this;this.find("option").removeAttr("selected");let s=typeof u=="object"?u:[u];for(let i of s)this.find(`option[value="${i}"]`).attr("selected","");return this}return this.attr("multiple")?a.toArray().map(s=>Fu(s.children)):a.attr("value")}case"input":case"option":return e?this.attr("value"):this.attr("value",u)}}function Xa(u,e){!u.attribs||!pe(u.attribs,e)||delete u.attribs[e]}function nt(u){return u?u.trim().split(Te):[]}function Yi(u){let e=nt(u);for(let t of e)P(this,a=>{N(a)&&Xa(a,t)});return this}function Vi(u){return this.toArray().some(e=>{let t=N(e)&&e.attribs.class,a=-1;if(t&&u.length>0)for(;(a=t.indexOf(u,a+1))>-1;){let s=a+u.length;if((a===0||Te.test(t[a-1]))&&(s===t.length||Te.test(t[s])))return!0}return!1})}function Ka(u){if(typeof u=="function")return P(this,(a,s)=>{if(N(a)){let i=a.attribs.class||"";Ka.call([a],u.call(a,s,i))}});if(!u||typeof u!="string")return this;let e=u.split(Te),t=this.length;for(let a=0;a<t;a++){let s=this[a];if(!N(s))continue;let i=it(s,"class",!1);if(i){let n=` ${i} `;for(let d of e){let l=`${d} `;n.includes(` ${l}`)||(n+=l)}Zu(s,"class",n.trim())}else Zu(s,"class",e.join(" ").trim())}return this}function ja(u){if(typeof u=="function")return P(this,(s,i)=>{N(s)&&ja.call([s],u.call(s,i,s.attribs.class||""))});let e=nt(u),t=e.length,a=arguments.length===0;return P(this,s=>{if(N(s))if(a)s.attribs.class="";else{let i=nt(s.attribs.class),n=!1;for(let d=0;d<t;d++){let l=i.indexOf(e[d]);l!==-1&&(i.splice(l,1),n=!0,d--)}n&&(s.attribs.class=i.join(" "))}})}function za(u,e){if(typeof u=="function")return P(this,(n,d)=>{N(n)&&za.call([n],u.call(n,d,n.attribs.class||"",e),e)});if(!u||typeof u!="string")return this;let t=u.split(Te),a=t.length,s=typeof e=="boolean"?e?1:-1:0,i=this.length;for(let n=0;n<i;n++){let d=this[n];if(!N(d))continue;let l=nt(d.attribs.class);for(let h=0;h<a;h++){let p=l.indexOf(t[h]);s>=0&&p===-1?l.push(t[h]):s<=0&&p!==-1&&l.splice(p,1)}d.attribs.class=l.join(" ")}return this}var F0={};$(F0,{_findBySelector:()=>En,add:()=>Gn,addBack:()=>Wn,children:()=>Dn,closest:()=>xn,contents:()=>On,each:()=>Rn,end:()=>Vn,eq:()=>Un,filter:()=>Pn,filterArray:()=>H0,find:()=>mn,first:()=>Bn,get:()=>Hn,has:()=>wn,index:()=>qn,is:()=>Mn,last:()=>vn,map:()=>yn,next:()=>An,nextAll:()=>_n,nextUntil:()=>Nn,not:()=>kn,parent:()=>pn,parents:()=>Tn,parentsUntil:()=>gn,prev:()=>Sn,prevAll:()=>In,prevUntil:()=>Cn,siblings:()=>Ln,slice:()=>Yn,toArray:()=>Fn});var I;(function(u){u.Attribute="attribute",u.Pseudo="pseudo",u.PseudoElement="pseudo-element",u.Tag="tag",u.Universal="universal",u.Adjacent="adjacent",u.Child="child",u.Descendant="descendant",u.Parent="parent",u.Sibling="sibling",u.ColumnCombinator="column-combinator"})(I||(I={}));var w;(function(u){u.Any="any",u.Element="element",u.End="end",u.Equals="equals",u.Exists="exists",u.Hyphen="hyphen",u.Not="not",u.Start="start"})(w||(w={}));var $a=/^[^\\#]?(?:\\(?:[\da-f]{1,6}\s?|.)|[\w\-\u00b0-\uFFFF])+/,Gi=/\\([\da-f]{1,6}\s?|(\s)|.)/gi,Wi=new Map([[126,w.Element],[94,w.Start],[36,w.End],[42,w.Any],[33,w.Not],[124,w.Hyphen]]),Qi=new Set(["has","not","matches","is","where","host","host-context"]);function Yu(u){switch(u.type){case I.Adjacent:case I.Child:case I.Descendant:case I.Parent:case I.Sibling:case I.ColumnCombinator:return!0;default:return!1}}var Xi=new Set(["contains","icontains"]);function Ki(u,e,t){let a=parseInt(e,16)-65536;return a!==a||t?e:a<0?String.fromCharCode(a+65536):String.fromCharCode(a>>10|55296,a&1023|56320)}function ge(u){return u.replace(Gi,Ki)}function T0(u){return u===39||u===34}function Za(u){return u===32||u===9||u===10||u===12||u===13}function pu(u){let e=[],t=Ja(e,`${u}`,0);if(t<u.length)throw new Error(`Unmatched selector: ${u.slice(t)}`);return e}function Ja(u,e,t){let a=[];function s(A){let S=e.slice(t+A).match($a);if(!S)throw new Error(`Expected name, found ${e.slice(t)}`);let[_]=S;return t+=A+_.length,ge(_)}function i(A){for(t+=A;t<e.length&&Za(e.charCodeAt(t));)t++}function n(){t+=1;let A=t,S=1;for(;S>0&&t<e.length;t++)e.charCodeAt(t)===40&&!d(t)?S++:e.charCodeAt(t)===41&&!d(t)&&S--;if(S)throw new Error("Parenthesis not matched");return ge(e.slice(A,t-1))}function d(A){let S=0;for(;e.charCodeAt(--A)===92;)S++;return(S&1)===1}function l(){if(a.length>0&&Yu(a[a.length-1]))throw new Error("Did not expect successive traversals.")}function h(A){if(a.length>0&&a[a.length-1].type===I.Descendant){a[a.length-1].type=A;return}l(),a.push({type:A})}function p(A,S){a.push({type:I.Attribute,name:A,action:S,value:s(1),namespace:null,ignoreCase:"quirks"})}function x(){if(a.length&&a[a.length-1].type===I.Descendant&&a.pop(),a.length===0)throw new Error("Empty sub-selector");u.push(a)}if(i(0),e.length===t)return t;u:for(;t<e.length;){let A=e.charCodeAt(t);switch(A){case 32:case 9:case 10:case 12:case 13:{(a.length===0||a[0].type!==I.Descendant)&&(l(),a.push({type:I.Descendant})),i(1);break}case 62:{h(I.Child),i(1);break}case 60:{h(I.Parent),i(1);break}case 126:{h(I.Sibling),i(1);break}case 43:{h(I.Adjacent),i(1);break}case 46:{p("class",w.Element);break}case 35:{p("id",w.Equals);break}case 91:{i(1);let S,_=null;e.charCodeAt(t)===124?S=s(1):e.startsWith("*|",t)?(_="*",S=s(2)):(S=s(0),e.charCodeAt(t)===124&&e.charCodeAt(t+1)!==61&&(_=S,S=s(1))),i(0);let D=w.Exists,k=Wi.get(e.charCodeAt(t));if(k){if(D=k,e.charCodeAt(t+1)!==61)throw new Error("Expected `=`");i(2)}else e.charCodeAt(t)===61&&(D=w.Equals,i(1));let xu="",Au=null;if(D!=="exists"){if(T0(e.charCodeAt(t))){let He=e.charCodeAt(t),Mu=t+1;for(;Mu<e.length&&(e.charCodeAt(Mu)!==He||d(Mu));)Mu+=1;if(e.charCodeAt(Mu)!==He)throw new Error("Attribute value didn't end");xu=ge(e.slice(t+1,Mu)),t=Mu+1}else{let He=t;for(;t<e.length&&(!Za(e.charCodeAt(t))&&e.charCodeAt(t)!==93||d(t));)t+=1;xu=ge(e.slice(He,t))}i(0);let ne=e.charCodeAt(t)|32;ne===115?(Au=!1,i(1)):ne===105&&(Au=!0,i(1))}if(e.charCodeAt(t)!==93)throw new Error("Attribute selector didn't terminate");t+=1;let ie={type:I.Attribute,name:S,action:D,value:xu,namespace:_,ignoreCase:Au};a.push(ie);break}case 58:{if(e.charCodeAt(t+1)===58){a.push({type:I.PseudoElement,name:s(2).toLowerCase(),data:e.charCodeAt(t)===40?n():null});continue}let S=s(1).toLowerCase(),_=null;if(e.charCodeAt(t)===40)if(Qi.has(S)){if(T0(e.charCodeAt(t+1)))throw new Error(`Pseudo-selector ${S} cannot be quoted`);if(_=[],t=Ja(_,e,t+1),e.charCodeAt(t)!==41)throw new Error(`Missing closing parenthesis in :${S} (${e})`);t+=1}else{if(_=n(),Xi.has(S)){let D=_.charCodeAt(0);D===_.charCodeAt(_.length-1)&&T0(D)&&(_=_.slice(1,-1))}_=ge(_)}a.push({type:I.Pseudo,name:S,data:_});break}case 44:{x(),a=[],i(1);break}default:{if(e.startsWith("/*",t)){let D=e.indexOf("*/",t+2);if(D<0)throw new Error("Comment was not terminated");t=D+2,a.length===0&&i(0);break}let S=null,_;if(A===42)t+=1,_="*";else if(A===124){if(_="",e.charCodeAt(t+1)===124){h(I.ColumnCombinator),i(2);break}}else if($a.test(e.slice(t)))_=s(0);else break u;e.charCodeAt(t)===124&&e.charCodeAt(t+1)!==124&&(S=_,e.charCodeAt(t+1)===42?(_="*",t+=2):_=s(1)),a.push(_==="*"?{type:I.Universal,namespace:S}:{type:I.Tag,name:_,namespace:S})}}}return x(),t}var O0=ku(Cu(),1);var Tu=ku(Cu(),1);var er=new Map([[I.Universal,50],[I.Tag,30],[I.Attribute,1],[I.Pseudo,0]]);function xe(u){return!er.has(u.type)}var ji=new Map([[w.Exists,10],[w.Equals,8],[w.Not,7],[w.Start,6],[w.End,6],[w.Any,5]]);function g0(u){let e=u.map(tr);for(let t=1;t<u.length;t++){let a=e[t];if(!(a<0))for(let s=t-1;s>=0&&a<e[s];s--){let i=u[s+1];u[s+1]=u[s],u[s]=i,e[s+1]=e[s],e[s]=a}}}function tr(u){var e,t;let a=(e=er.get(u.type))!==null&&e!==void 0?e:-1;return u.type===I.Attribute?(a=(t=ji.get(u.action))!==null&&t!==void 0?t:4,u.action===w.Equals&&u.name==="id"&&(a=9),u.ignoreCase&&(a>>=1)):u.type===I.Pseudo&&(u.data?u.name==="has"||u.name==="contains"?a=0:Array.isArray(u.data)?(a=Math.min(...u.data.map(s=>Math.min(...s.map(tr)))),a<0&&(a=0)):a=2:a=3),a}var Ae=ku(Cu(),1),zi=/[-[\]{}()*+?.,\\^$|#\s]/g;function ar(u){return u.replace(zi,"\\$&")}var $i=new Set(["accept","accept-charset","align","alink","axis","bgcolor","charset","checked","clear","codetype","color","compact","declare","defer","dir","direction","disabled","enctype","face","frame","hreflang","http-equiv","lang","language","link","media","method","multiple","nohref","noresize","noshade","nowrap","readonly","rel","rev","rules","scope","scrolling","selected","shape","target","text","type","valign","valuetype","vlink"]);function Vu(u,e){return typeof u.ignoreCase=="boolean"?u.ignoreCase:u.ignoreCase==="quirks"?!!e.quirksMode:!e.xmlMode&&$i.has(u.name)}var rr={equals(u,e,t){let{adapter:a}=t,{name:s}=e,{value:i}=e;return Vu(e,t)?(i=i.toLowerCase(),n=>{let d=a.getAttributeValue(n,s);return d!=null&&d.length===i.length&&d.toLowerCase()===i&&u(n)}):n=>a.getAttributeValue(n,s)===i&&u(n)},hyphen(u,e,t){let{adapter:a}=t,{name:s}=e,{value:i}=e,n=i.length;return Vu(e,t)?(i=i.toLowerCase(),function(l){let h=a.getAttributeValue(l,s);return h!=null&&(h.length===n||h.charAt(n)==="-")&&h.substr(0,n).toLowerCase()===i&&u(l)}):function(l){let h=a.getAttributeValue(l,s);return h!=null&&(h.length===n||h.charAt(n)==="-")&&h.substr(0,n)===i&&u(l)}},element(u,e,t){let{adapter:a}=t,{name:s,value:i}=e;if(/\s/.test(i))return Ae.default.falseFunc;let n=new RegExp(`(?:^|\\s)${ar(i)}(?:$|\\s)`,Vu(e,t)?"i":"");return function(l){let h=a.getAttributeValue(l,s);return h!=null&&h.length>=i.length&&n.test(h)&&u(l)}},exists(u,{name:e},{adapter:t}){return a=>t.hasAttrib(a,e)&&u(a)},start(u,e,t){let{adapter:a}=t,{name:s}=e,{value:i}=e,n=i.length;return n===0?Ae.default.falseFunc:Vu(e,t)?(i=i.toLowerCase(),d=>{let l=a.getAttributeValue(d,s);return l!=null&&l.length>=n&&l.substr(0,n).toLowerCase()===i&&u(d)}):d=>{var l;return!!(!((l=a.getAttributeValue(d,s))===null||l===void 0)&&l.startsWith(i))&&u(d)}},end(u,e,t){let{adapter:a}=t,{name:s}=e,{value:i}=e,n=-i.length;return n===0?Ae.default.falseFunc:Vu(e,t)?(i=i.toLowerCase(),d=>{var l;return((l=a.getAttributeValue(d,s))===null||l===void 0?void 0:l.substr(n).toLowerCase())===i&&u(d)}):d=>{var l;return!!(!((l=a.getAttributeValue(d,s))===null||l===void 0)&&l.endsWith(i))&&u(d)}},any(u,e,t){let{adapter:a}=t,{name:s,value:i}=e;if(i==="")return Ae.default.falseFunc;if(Vu(e,t)){let n=new RegExp(ar(i),"i");return function(l){let h=a.getAttributeValue(l,s);return h!=null&&h.length>=i.length&&n.test(h)&&u(l)}}return n=>{var d;return!!(!((d=a.getAttributeValue(n,s))===null||d===void 0)&&d.includes(i))&&u(n)}},not(u,e,t){let{adapter:a}=t,{name:s}=e,{value:i}=e;return i===""?n=>!!a.getAttributeValue(n,s)&&u(n):Vu(e,t)?(i=i.toLowerCase(),n=>{let d=a.getAttributeValue(n,s);return(d==null||d.length!==i.length||d.toLowerCase()!==i)&&u(n)}):n=>a.getAttributeValue(n,s)!==i&&u(n)}};var Zi=new Set([9,10,12,13,32]),sr="0".charCodeAt(0),Ji="9".charCodeAt(0);function ir(u){if(u=u.trim().toLowerCase(),u==="even")return[2,0];if(u==="odd")return[2,1];let e=0,t=0,a=i(),s=n();if(e<u.length&&u.charAt(e)==="n"&&(e++,t=a*(s!=null?s:1),d(),e<u.length?(a=i(),d(),s=n()):a=s=0),s===null||e<u.length)throw new Error(`n-th rule couldn't be parsed ('${u}')`);return[t,a*s];function i(){return u.charAt(e)==="-"?(e++,-1):(u.charAt(e)==="+"&&e++,1)}function n(){let l=e,h=0;for(;e<u.length&&u.charCodeAt(e)>=sr&&u.charCodeAt(e)<=Ji;)h=h*10+(u.charCodeAt(e)-sr),e++;return e===l?null:h}function d(){for(;e<u.length&&Zi.has(u.charCodeAt(e));)e++}}var x0=ku(Cu(),1);function nr(u){let e=u[0],t=u[1]-1;if(t<0&&e<=0)return x0.default.falseFunc;if(e===-1)return i=>i<=t;if(e===0)return i=>i===t;if(e===1)return t<0?x0.default.trueFunc:i=>i>=t;let a=Math.abs(e),s=(t%a+a)%a;return e>1?i=>i>=t&&i%a===s:i=>i<=t&&i%a===s}function Ju(u){return nr(ir(u))}var z=ku(Cu(),1);function ct(u,e){return t=>{let a=e.getParent(t);return a!=null&&e.isTag(a)&&u(t)}}var ue={contains(u,e,{adapter:t}){return function(s){return u(s)&&t.getText(s).includes(e)}},icontains(u,e,{adapter:t}){let a=e.toLowerCase();return function(i){return u(i)&&t.getText(i).toLowerCase().includes(a)}},"nth-child"(u,e,{adapter:t,equals:a}){let s=Ju(e);return s===z.default.falseFunc?z.default.falseFunc:s===z.default.trueFunc?ct(u,t):function(n){let d=t.getSiblings(n),l=0;for(let h=0;h<d.length&&!a(n,d[h]);h++)t.isTag(d[h])&&l++;return s(l)&&u(n)}},"nth-last-child"(u,e,{adapter:t,equals:a}){let s=Ju(e);return s===z.default.falseFunc?z.default.falseFunc:s===z.default.trueFunc?ct(u,t):function(n){let d=t.getSiblings(n),l=0;for(let h=d.length-1;h>=0&&!a(n,d[h]);h--)t.isTag(d[h])&&l++;return s(l)&&u(n)}},"nth-of-type"(u,e,{adapter:t,equals:a}){let s=Ju(e);return s===z.default.falseFunc?z.default.falseFunc:s===z.default.trueFunc?ct(u,t):function(n){let d=t.getSiblings(n),l=0;for(let h=0;h<d.length;h++){let p=d[h];if(a(n,p))break;t.isTag(p)&&t.getName(p)===t.getName(n)&&l++}return s(l)&&u(n)}},"nth-last-of-type"(u,e,{adapter:t,equals:a}){let s=Ju(e);return s===z.default.falseFunc?z.default.falseFunc:s===z.default.trueFunc?ct(u,t):function(n){let d=t.getSiblings(n),l=0;for(let h=d.length-1;h>=0;h--){let p=d[h];if(a(n,p))break;t.isTag(p)&&t.getName(p)===t.getName(n)&&l++}return s(l)&&u(n)}},root(u,e,{adapter:t}){return a=>{let s=t.getParent(a);return(s==null||!t.isTag(s))&&u(a)}},scope(u,e,t,a){let{equals:s}=t;return!a||a.length===0?ue.root(u,e,t):a.length===1?i=>s(a[0],i)&&u(i):i=>a.includes(i)&&u(i)},hover:A0("isHovered"),visited:A0("isVisited"),active:A0("isActive")};function A0(u){return function(t,a,{adapter:s}){let i=s[u];return typeof i!="function"?z.default.falseFunc:function(d){return i(d)&&t(d)}}}var _e={empty(u,{adapter:e}){return!e.getChildren(u).some(t=>e.isTag(t)||e.getText(t)!=="")},"first-child"(u,{adapter:e,equals:t}){if(e.prevElementSibling)return e.prevElementSibling(u)==null;let a=e.getSiblings(u).find(s=>e.isTag(s));return a!=null&&t(u,a)},"last-child"(u,{adapter:e,equals:t}){let a=e.getSiblings(u);for(let s=a.length-1;s>=0;s--){if(t(u,a[s]))return!0;if(e.isTag(a[s]))break}return!1},"first-of-type"(u,{adapter:e,equals:t}){let a=e.getSiblings(u),s=e.getName(u);for(let i=0;i<a.length;i++){let n=a[i];if(t(u,n))return!0;if(e.isTag(n)&&e.getName(n)===s)break}return!1},"last-of-type"(u,{adapter:e,equals:t}){let a=e.getSiblings(u),s=e.getName(u);for(let i=a.length-1;i>=0;i--){let n=a[i];if(t(u,n))return!0;if(e.isTag(n)&&e.getName(n)===s)break}return!1},"only-of-type"(u,{adapter:e,equals:t}){let a=e.getName(u);return e.getSiblings(u).every(s=>t(u,s)||!e.isTag(s)||e.getName(s)!==a)},"only-child"(u,{adapter:e,equals:t}){return e.getSiblings(u).every(a=>t(u,a)||!e.isTag(a))}};function _0(u,e,t,a){if(t===null){if(u.length>a)throw new Error(`Pseudo-class :${e} requires an argument`)}else if(u.length===a)throw new Error(`Pseudo-class :${e} doesn't have any arguments`)}var ot={"any-link":":is(a, area, link)[href]",link:":any-link:not(:visited)",disabled:`:is(
        :is(button, input, select, textarea, optgroup, option)[disabled],
        optgroup[disabled] > option,
        fieldset[disabled]:not(fieldset[disabled] legend:first-of-type *)
    )`,enabled:":not(:disabled)",checked:":is(:is(input[type=radio], input[type=checkbox])[checked], option:selected)",required:":is(input, select, textarea)[required]",optional:":is(input, select, textarea):not([required])",selected:"option:is([selected], select:not([multiple]):not(:has(> option[selected])) > :first-of-type)",checkbox:"[type=checkbox]",file:"[type=file]",password:"[type=password]",radio:"[type=radio]",reset:"[type=reset]",image:"[type=image]",submit:"[type=submit]",parent:":not(:empty)",header:":is(h1, h2, h3, h4, h5, h6)",button:":is(button, input[type=button])",input:":is(input, textarea, select, button)",text:"input:is(:not([type!='']), [type=text])"};var ru=ku(Cu(),1);var I0={};function C0(u,e){return u===ru.default.falseFunc?ru.default.falseFunc:t=>e.isTag(t)&&u(t)}function L0(u,e){let t=e.getSiblings(u);if(t.length<=1)return[];let a=t.indexOf(u);return a<0||a===t.length-1?[]:t.slice(a+1).filter(e.isTag)}function S0(u){return{xmlMode:!!u.xmlMode,lowerCaseAttributeNames:!!u.lowerCaseAttributeNames,lowerCaseTags:!!u.lowerCaseTags,quirksMode:!!u.quirksMode,cacheResults:!!u.cacheResults,pseudos:u.pseudos,adapter:u.adapter,equals:u.equals}}var N0=(u,e,t,a,s)=>{let i=s(e,S0(t),a);return i===ru.default.trueFunc?u:i===ru.default.falseFunc?ru.default.falseFunc:n=>i(n)&&u(n)},dt={is:N0,matches:N0,where:N0,not(u,e,t,a,s){let i=s(e,S0(t),a);return i===ru.default.falseFunc?u:i===ru.default.trueFunc?ru.default.falseFunc:n=>!i(n)&&u(n)},has(u,e,t,a,s){let{adapter:i}=t,n=S0(t);n.relativeSelector=!0;let d=e.some(p=>p.some(xe))?[I0]:void 0,l=s(e,n,d);if(l===ru.default.falseFunc)return ru.default.falseFunc;let h=C0(l,i);if(d&&l!==ru.default.trueFunc){let{shouldTestNextSiblings:p=!1}=l;return x=>{if(!u(x))return!1;d[0]=x;let A=i.getChildren(x),S=p?[...A,...L0(x,i)]:A;return i.existsOne(h,S)}}return p=>u(p)&&i.existsOne(h,i.getChildren(p))}};function cr(u,e,t,a,s){var i;let{name:n,data:d}=e;if(Array.isArray(d)){if(!(n in dt))throw new Error(`Unknown pseudo-class :${n}(${d})`);return dt[n](u,d,t,a,s)}let l=(i=t.pseudos)===null||i===void 0?void 0:i[n],h=typeof l=="string"?l:ot[n];if(typeof h=="string"){if(d!=null)throw new Error(`Pseudo ${n} doesn't have any arguments`);let p=pu(h);return dt.is(u,p,t,a,s)}if(typeof l=="function")return _0(l,n,d,1),p=>l(p,d)&&u(p);if(n in ue)return ue[n](u,d,t,a);if(n in _e){let p=_e[n];return _0(p,n,d,2),x=>p(x,t,d)&&u(x)}throw new Error(`Unknown pseudo-class :${n}`)}function D0(u,e){let t=e.getParent(u);return t&&e.isTag(t)?t:null}function or(u,e,t,a,s){let{adapter:i,equals:n}=t;switch(e.type){case I.PseudoElement:throw new Error("Pseudo-elements are not supported by css-select");case I.ColumnCombinator:throw new Error("Column combinators are not yet supported by css-select");case I.Attribute:{if(e.namespace!=null)throw new Error("Namespaced attributes are not yet supported by css-select");return(!t.xmlMode||t.lowerCaseAttributeNames)&&(e.name=e.name.toLowerCase()),rr[e.action](u,e,t)}case I.Pseudo:return cr(u,e,t,a,s);case I.Tag:{if(e.namespace!=null)throw new Error("Namespaced tag names are not yet supported by css-select");let{name:d}=e;return(!t.xmlMode||t.lowerCaseTags)&&(d=d.toLowerCase()),function(h){return i.getName(h)===d&&u(h)}}case I.Descendant:{if(t.cacheResults===!1||typeof WeakSet=="undefined")return function(h){let p=h;for(;p=D0(p,i);)if(u(p))return!0;return!1};let d=new WeakSet;return function(h){let p=h;for(;p=D0(p,i);)if(!d.has(p)){if(i.isTag(p)&&u(p))return!0;d.add(p)}return!1}}case"_flexibleDescendant":return function(l){let h=l;do if(u(h))return!0;while(h=D0(h,i));return!1};case I.Parent:return function(l){return i.getChildren(l).some(h=>i.isTag(h)&&u(h))};case I.Child:return function(l){let h=i.getParent(l);return h!=null&&i.isTag(h)&&u(h)};case I.Sibling:return function(l){let h=i.getSiblings(l);for(let p=0;p<h.length;p++){let x=h[p];if(n(l,x))break;if(i.isTag(x)&&u(x))return!0}return!1};case I.Adjacent:return i.prevElementSibling?function(l){let h=i.prevElementSibling(l);return h!=null&&u(h)}:function(l){let h=i.getSiblings(l),p;for(let x=0;x<h.length;x++){let A=h[x];if(n(l,A))break;i.isTag(A)&&(p=A)}return!!p&&u(p)};case I.Universal:{if(e.namespace!=null&&e.namespace!=="*")throw new Error("Namespaced universal selectors are not yet supported by css-select");return u}}}function dr(u,e,t){let a=ft(u,e,t);return C0(a,e.adapter)}function ft(u,e,t){let a=typeof u=="string"?pu(u):u;return lt(a,e,t)}function fr(u){return u.type===I.Pseudo&&(u.name==="scope"||Array.isArray(u.data)&&u.data.some(e=>e.some(fr)))}var un={type:I.Descendant},en={type:"_flexibleDescendant"},tn={type:I.Pseudo,name:"scope",data:null};function an(u,{adapter:e},t){let a=!!(t!=null&&t.every(s=>{let i=e.isTag(s)&&e.getParent(s);return s===I0||i&&e.isTag(i)}));for(let s of u){if(!(s.length>0&&xe(s[0])&&s[0].type!==I.Descendant))if(a&&!s.some(fr))s.unshift(un);else continue;s.unshift(tn)}}function lt(u,e,t){var a;u.forEach(g0),t=(a=e.context)!==null&&a!==void 0?a:t;let s=Array.isArray(t),i=t&&(Array.isArray(t)?t:[t]);if(e.relativeSelector!==!1)an(u,e,i);else if(u.some(l=>l.length>0&&xe(l[0])))throw new Error("Relative selectors are not allowed when the `relativeSelector` option is disabled");let n=!1,d=u.map(l=>{if(l.length>=2){let[h,p]=l;h.type!==I.Pseudo||h.name!=="scope"||(s&&p.type===I.Descendant?l[1]=en:(p.type===I.Adjacent||p.type===I.Sibling)&&(n=!0))}return rn(l,e,i)}).reduce(sn,Tu.default.falseFunc);return d.shouldTestNextSiblings=n,d}function rn(u,e,t){var a;return u.reduce((s,i)=>s===Tu.default.falseFunc?Tu.default.falseFunc:or(s,i,e,t,lt),(a=e.rootFunc)!==null&&a!==void 0?a:Tu.default.trueFunc)}function sn(u,e){return e===Tu.default.falseFunc||u===Tu.default.trueFunc?u:u===Tu.default.falseFunc||e===Tu.default.trueFunc?e:function(a){return u(a)||e(a)}}var lr=(u,e)=>u===e,nn={adapter:mu,equals:lr};function br(u){var e,t,a,s;let i=u!=null?u:nn;return(e=i.adapter)!==null&&e!==void 0||(i.adapter=mu),(t=i.equals)!==null&&t!==void 0||(i.equals=(s=(a=i.adapter)===null||a===void 0?void 0:a.equals)!==null&&s!==void 0?s:lr),i}function R0(u){return function(t,a,s){let i=br(a);return u(t,i,s)}}var If=R0(dr),Cf=R0(ft),bt=R0(lt);function hr(u){return function(t,a,s){let i=br(s);typeof t!="function"&&(t=ft(t,i,a));let n=ht(a,i.adapter,t.shouldTestNextSiblings);return u(t,n,i)}}function ht(u,e,t=!1){return t&&(u=cn(u,e)),Array.isArray(u)?e.removeSubsets(u):e.getChildren(u)}function cn(u,e){let t=Array.isArray(u)?u.slice(0):[u],a=t.length;for(let s=0;s<a;s++){let i=L0(t[s],e);t.push(...i)}return t}var Lf=hr((u,e,t)=>u===O0.default.falseFunc||!e||e.length===0?[]:t.adapter.findAll(u,e)),Df=hr((u,e,t)=>u===O0.default.falseFunc||!e||e.length===0?null:t.adapter.findOne(u,e));var Et=ku(Cu(),1);var on=new Set(["first","last","eq","gt","nth","lt","even","odd"]);function ee(u){return u.type!=="pseudo"?!1:on.has(u.name)?!0:u.name==="not"&&Array.isArray(u.data)?u.data.some(e=>e.some(ee)):!1}function mr(u,e,t){let a=e!=null?parseInt(e,10):NaN;switch(u){case"first":return 1;case"nth":case"eq":return isFinite(a)?a>=0?a+1:1/0:0;case"lt":return isFinite(a)?a>=0?Math.min(a,t):1/0:0;case"gt":return isFinite(a)?1/0:0;case"odd":return 2*t;case"even":return 2*t-1;case"last":case"not":return 1/0}}function Er(u){for(;u.parent;)u=u.parent;return u}function mt(u){let e=[],t=[];for(let a of u)a.some(ee)?e.push(a):t.push(a);return[t,e]}var dn={type:I.Universal,namespace:null},fn={type:I.Pseudo,name:"scope",data:null};function M0(u,e,t={}){return k0([u],e,t)}function k0(u,e,t={}){if(typeof e=="function")return u.some(e);let[a,s]=mt(pu(e));return a.length>0&&u.some(bt(a,t))||s.some(i=>Tr(i,u,t).length>0)}function ln(u,e,t,a){let s=typeof t=="string"?parseInt(t,10):NaN;switch(u){case"first":case"lt":return e;case"last":return e.length>0?[e[e.length-1]]:e;case"nth":case"eq":return isFinite(s)&&Math.abs(s)<e.length?[s<0?e[e.length+s]:e[s]]:[];case"gt":return isFinite(s)?e.slice(s+1):[];case"even":return e.filter((i,n)=>n%2===0);case"odd":return e.filter((i,n)=>n%2===1);case"not":{let i=new Set(pr(t,e,a));return e.filter(n=>!i.has(n))}}}function w0(u,e,t={}){return pr(pu(u),e,t)}function pr(u,e,t){if(e.length===0)return[];let[a,s]=mt(u),i;if(a.length){let n=P0(e,a,t);if(s.length===0)return n;n.length&&(i=new Set(n))}for(let n=0;n<s.length&&(i==null?void 0:i.size)!==e.length;n++){let d=s[n];if((i?e.filter(p=>N(p)&&!i.has(p)):e).length===0)break;let h=Tr(d,e,t);if(h.length)if(i)h.forEach(p=>i.add(p));else{if(n===s.length-1)return h;i=new Set(h)}}return typeof i!="undefined"?i.size===e.length?e:e.filter(n=>i.has(n)):[]}function Tr(u,e,t){var a;if(u.some(Yu)){let s=(a=t.root)!==null&&a!==void 0?a:Er(e[0]),i={...t,context:e,relativeSelector:!1};return u.push(fn),pt(s,u,i,!0,e.length)}return pt(e,u,t,!1,e.length)}function gr(u,e,t={},a=1/0){if(typeof u=="function")return xr(e,u);let[s,i]=mt(pu(u)),n=i.map(d=>pt(e,d,t,!0,a));return s.length&&n.push(y0(e,s,t,a)),n.length===0?[]:n.length===1?n[0]:Nu(n.reduce((d,l)=>[...d,...l]))}function pt(u,e,t,a,s){let i=e.findIndex(ee),n=e.slice(0,i),d=e[i],l=e.length-1===i?s:1/0,h=mr(d.name,d.data,l);if(h===0)return[];let x=(n.length===0&&!Array.isArray(u)?Ku(u).filter(N):n.length===0?(Array.isArray(u)?u:[u]).filter(N):a||n.some(Yu)?y0(u,[n],t,h):P0(u,[n],t)).slice(0,h),A=ln(d.name,x,d.data,t);if(A.length===0||e.length===i+1)return A;let S=e.slice(i+1),_=S.some(Yu);if(_){if(Yu(S[0])){let{type:D}=S[0];(D===I.Sibling||D===I.Adjacent)&&(A=ht(A,mu,!0)),S.unshift(dn)}t={...t,relativeSelector:!1,rootFunc:D=>A.includes(D)}}else t.rootFunc&&t.rootFunc!==Et.trueFunc&&(t={...t,rootFunc:Et.trueFunc});return S.some(ee)?pt(A,S,t,!1,s):_?y0(A,[S],t,s):P0(A,[S],t)}function y0(u,e,t,a){let s=bt(e,t,u);return xr(u,s,a)}function xr(u,e,t=1/0){let a=ht(u,mu,e.shouldTestNextSiblings);return $e(s=>N(s)&&e(s),a,!0,t)}function P0(u,e,t){let a=(Array.isArray(u)?u:[u]).filter(N);if(a.length===0)return a;let s=bt(e,t);return s===Et.trueFunc?a:a.filter(s)}var hn=/^\s*[+~]/;function mn(u){if(!u)return this._make([]);if(typeof u!="string"){let e=tu(u)?u.toArray():[u],t=this.toArray();return this._make(e.filter(a=>t.some(s=>et(s,a))))}return this._findBySelector(u,Number.POSITIVE_INFINITY)}function En(u,e){var t;let a=this.toArray(),s=hn.test(u)?a:this.children().toArray(),i={context:a,root:(t=this._root)===null||t===void 0?void 0:t[0],xmlMode:this.options.xmlMode,lowerCaseTags:this.options.lowerCaseTags,lowerCaseAttributeNames:this.options.lowerCaseAttributeNames,pseudos:this.options.pseudos,quirksMode:this.options.quirksMode};return this._make(gr(u,s,i,e))}function B0(u){return function(e,...t){return function(a){var s;let i=u(e,this);return a&&(i=H0(i,a,this.options.xmlMode,(s=this._root)===null||s===void 0?void 0:s[0])),this._make(this.length>1&&i.length>1?t.reduce((n,d)=>d(n),i):i)}}}var Ne=B0((u,e)=>{let t=[];for(let a=0;a<e.length;a++){let s=u(e[a]);s.length>0&&(t=t.concat(s))}return t}),v0=B0((u,e)=>{let t=[];for(let a=0;a<e.length;a++){let s=u(e[a]);s!==null&&t.push(s)}return t});function U0(u,...e){let t=null,a=B0((s,i)=>{let n=[];return P(i,d=>{for(let l;(l=s(d))&&!(t!=null&&t(l,n.length));d=l)n.push(l)}),n})(u,...e);return function(s,i){t=typeof s=="string"?d=>M0(d,s,this.options):s?Se(s):null;let n=a.call(this,i);return t=null,n}}function te(u){return u.length>1?Array.from(new Set(u)):u}var pn=v0(({parent:u})=>u&&!uu(u)?u:null,te),Tn=Ne(u=>{let e=[];for(;u.parent&&!uu(u.parent);)e.push(u.parent),u=u.parent;return e},Nu,u=>u.reverse()),gn=U0(({parent:u})=>u&&!uu(u)?u:null,Nu,u=>u.reverse());function xn(u){var e;let t=[];if(!u)return this._make(t);let a={xmlMode:this.options.xmlMode,root:(e=this._root)===null||e===void 0?void 0:e[0]},s=typeof u=="string"?i=>M0(i,u,a):Se(u);return P(this,i=>{for(i&&!uu(i)&&!N(i)&&(i=i.parent);i&&N(i);){if(s(i,0)){t.includes(i)||t.push(i);break}i=i.parent}}),this._make(t)}var An=v0(u=>je(u)),_n=Ne(u=>{let e=[];for(;u.next;)u=u.next,N(u)&&e.push(u);return e},te),Nn=U0(u=>je(u),te),Sn=v0(u=>ze(u)),In=Ne(u=>{let e=[];for(;u.prev;)u=u.prev,N(u)&&e.push(u);return e},te),Cn=U0(u=>ze(u),te),Ln=Ne(u=>i0(u).filter(e=>N(e)&&e!==u),Nu),Dn=Ne(u=>Ku(u).filter(N),te);function On(){let u=this.toArray().reduce((e,t)=>R(t)?e.concat(t.children):e,[]);return this._make(u)}function Rn(u){let e=0,t=this.length;for(;e<t&&u.call(this[e],e,this[e])!==!1;)++e;return this}function yn(u){let e=[];for(let t=0;t<this.length;t++){let a=this[t],s=u.call(a,t,a);s!=null&&(e=e.concat(s))}return this._make(e)}function Se(u){return typeof u=="function"?(e,t)=>u.call(e,t,e):tu(u)?e=>Array.prototype.includes.call(u,e):function(e){return u===e}}function Pn(u){var e;return this._make(H0(this.toArray(),u,this.options.xmlMode,(e=this._root)===null||e===void 0?void 0:e[0]))}function H0(u,e,t,a){return typeof e=="string"?w0(e,u,{xmlMode:t,root:a}):u.filter(Se(e))}function Mn(u){let e=this.toArray();return typeof u=="string"?k0(e.filter(N),u,this.options):u?e.some(Se(u)):!1}function kn(u){let e=this.toArray();if(typeof u=="string"){let t=new Set(w0(u,e,this.options));e=e.filter(a=>!t.has(a))}else{let t=Se(u);e=e.filter((a,s)=>!t(a,s))}return this._make(e)}function wn(u){return this.filter(typeof u=="string"?`:has(${u})`:(e,t)=>this._make(t).find(u).length>0)}function Bn(){return this.length>1?this._make(this[0]):this}function vn(){return this.length>0?this._make(this[this.length-1]):this}function Un(u){var e;return u=+u,u===0&&this.length<=1?this:(u<0&&(u=this.length+u),this._make((e=this[u])!==null&&e!==void 0?e:[]))}function Hn(u){return u==null?this.toArray():this[u<0?this.length+u:u]}function Fn(){return Array.prototype.slice.call(this)}function qn(u){let e,t;return u==null?(e=this.parent().children(),t=this[0]):typeof u=="string"?(e=this._make(u),t=this[0]):(e=this,t=tu(u)?u[0]:u),Array.prototype.indexOf.call(e,t)}function Yn(u,e){return this._make(Array.prototype.slice.call(this,u,e))}function Vn(){var u;return(u=this.prevObject)!==null&&u!==void 0?u:this._make([])}function Gn(u,e){let t=this._make(u,e),a=Nu([...this.get(),...t.get()]);return this._make(a)}function Wn(u){return this.prevObject?this.add(u?this.prevObject.filter(u):this.prevObject):this}var q0={};$(q0,{_makeDomArray:()=>Qn,after:()=>ec,append:()=>jn,appendTo:()=>Xn,before:()=>ac,clone:()=>fc,empty:()=>nc,html:()=>cc,insertAfter:()=>tc,insertBefore:()=>rc,prepend:()=>zn,prependTo:()=>Kn,remove:()=>sc,replaceWith:()=>ic,text:()=>dc,toString:()=>oc,unwrap:()=>Jn,wrap:()=>$n,wrapAll:()=>uc,wrapInner:()=>Zn});function Ar(u){return function(t,a,s,i){if(typeof Buffer!="undefined"&&Buffer.isBuffer(t)&&(t=t.toString()),typeof t=="string")return u(t,a,s,i);let n=t;if(!Array.isArray(n)&&uu(n))return n;let d=new J([]);return Lu(n,d),d}}function Lu(u,e){let t=Array.isArray(u)?u:[u];e?e.children=t:e=null;for(let a=0;a<t.length;a++){let s=t[a];s.parent&&s.parent.children!==t&&hu(s),e?(s.prev=t[a-1]||null,s.next=t[a+1]||null):s.prev=s.next=null,s.parent=e}return e}function Qn(u,e){if(u==null)return[];if(typeof u=="string")return this._parse(u,this.options,!1,null).children.slice(0);if("length"in u){if(u.length===1)return this._makeDomArray(u[0],e);let t=[];for(let a=0;a<u.length;a++){let s=u[a];if(typeof s=="object"){if(s==null)continue;if(!("length"in s)){t.push(e?Qu(s,!0):s);continue}}t.push(...this._makeDomArray(s,e))}return t}return[e?Qu(u,!0):u]}function _r(u){return function(...e){let t=this.length-1;return P(this,(a,s)=>{if(!R(a))return;let i=typeof e[0]=="function"?e[0].call(a,s,this._render(a.children)):e,n=this._makeDomArray(i,s<t);u(n,a.children,a)})}}function Du(u,e,t,a,s){var i,n;let d=[e,t,...a],l=e===0?null:u[e-1],h=e+t>=u.length?null:u[e+t];for(let p=0;p<a.length;++p){let x=a[p],A=x.parent;if(A){let _=A.children.indexOf(x);_!==-1&&(A.children.splice(_,1),s===A&&e>_&&d[0]--)}x.parent=s,x.prev&&(x.prev.next=(i=x.next)!==null&&i!==void 0?i:null),x.next&&(x.next.prev=(n=x.prev)!==null&&n!==void 0?n:null),x.prev=p===0?l:a[p-1],x.next=p===a.length-1?h:a[p+1]}return l&&(l.next=a[0]),h&&(h.prev=a[a.length-1]),u.splice(...d)}function Xn(u){return(tu(u)?u:this._make(u)).append(this),this}function Kn(u){return(tu(u)?u:this._make(u)).prepend(this),this}var jn=_r((u,e,t)=>{Du(e,e.length,0,u,t)}),zn=_r((u,e,t)=>{Du(e,0,0,u,t)});function Nr(u){return function(e){let t=this.length-1,a=this.parents().last();for(let s=0;s<this.length;s++){let i=this[s],n=typeof e=="function"?e.call(i,s,i):typeof e=="string"&&!he(e)?a.find(e).clone():e,[d]=this._makeDomArray(n,s<t);if(!d||!R(d))continue;let l=d,h=0;for(;h<l.children.length;){let p=l.children[h];N(p)?(l=p,h=0):h++}u(i,l,[d])}return this}}var $n=Nr((u,e,t)=>{let{parent:a}=u;if(!a)return;let s=a.children,i=s.indexOf(u);Lu([u],e),Du(s,i,0,t,a)}),Zn=Nr((u,e,t)=>{R(u)&&(Lu(u.children,e),Lu(t,u))});function Jn(u){return this.parent(u).not("body").each((e,t)=>{this._make(t).replaceWith(t.children)}),this}function uc(u){let e=this[0];if(e){let t=this._make(typeof u=="function"?u.call(e,0,e):u).insertBefore(e),a;for(let i=0;i<t.length;i++)t[i].type===Z.Tag&&(a=t[i]);let s=0;for(;a&&s<a.children.length;){let i=a.children[s];i.type===Z.Tag?(a=i,s=0):s++}a&&this._make(a).append(this)}return this}function ec(...u){let e=this.length-1;return P(this,(t,a)=>{if(!R(t)||!t.parent)return;let s=t.parent.children,i=s.indexOf(t);if(i===-1)return;let n=typeof u[0]=="function"?u[0].call(t,a,this._render(t.children)):u,d=this._makeDomArray(n,a<e);Du(s,i+1,0,d,t.parent)})}function tc(u){typeof u=="string"&&(u=this._make(u)),this.remove();let e=[];for(let t of this._makeDomArray(u)){let a=this.clone().toArray(),{parent:s}=t;if(!s)continue;let i=s.children,n=i.indexOf(t);n!==-1&&(Du(i,n+1,0,a,s),e.push(...a))}return this._make(e)}function ac(...u){let e=this.length-1;return P(this,(t,a)=>{if(!R(t)||!t.parent)return;let s=t.parent.children,i=s.indexOf(t);if(i===-1)return;let n=typeof u[0]=="function"?u[0].call(t,a,this._render(t.children)):u,d=this._makeDomArray(n,a<e);Du(s,i,0,d,t.parent)})}function rc(u){let e=this._make(u);this.remove();let t=[];return P(e,a=>{let s=this.clone().toArray(),{parent:i}=a;if(!i)return;let n=i.children,d=n.indexOf(a);d!==-1&&(Du(n,d,0,s,i),t.push(...s))}),this._make(t)}function sc(u){let e=u?this.filter(u):this;return P(e,t=>{hu(t),t.prev=t.next=t.parent=null}),this}function ic(u){return P(this,(e,t)=>{let{parent:a}=e;if(!a)return;let s=a.children,i=typeof u=="function"?u.call(e,t,e):u,n=this._makeDomArray(i);Lu(n,null);let d=s.indexOf(e);Du(s,d,1,n,a),n.includes(e)||(e.parent=e.prev=e.next=null)})}function nc(){return P(this,u=>{if(R(u)){for(let e of u.children)e.next=e.prev=e.parent=null;u.children.length=0}})}function cc(u){if(u===void 0){let e=this[0];return!e||!R(e)?null:this._render(e.children)}return P(this,e=>{if(!R(e))return;for(let a of e.children)a.next=a.prev=a.parent=null;let t=tu(u)?u.toArray():this._parse(`${u}`,this.options,!1,e).children;Lu(t,e)})}function oc(){return this._render(this)}function dc(u){return u===void 0?Fu(this):typeof u=="function"?P(this,(e,t)=>this._make(e).text(u.call(e,t,Fu([e])))):P(this,e=>{if(!R(e))return;for(let a of e.children)a.next=a.prev=a.parent=null;let t=new ou(`${u}`);Lu(t,e)})}function fc(){let u=Array.prototype.map.call(this.get(),t=>Qu(t,!0)),e=new J(u);for(let t of u)t.parent=e;return this._make(u)}var Y0={};$(Y0,{css:()=>lc});function lc(u,e){if(u!=null&&e!=null||typeof u=="object"&&!Array.isArray(u))return P(this,(t,a)=>{N(t)&&Sr(t,u,e,a)});if(this.length!==0)return Ir(this[0],u)}function Sr(u,e,t,a){if(typeof e=="string"){let s=Ir(u),i=typeof t=="function"?t.call(u,a,s[e]):t;i===""?delete s[e]:i!=null&&(s[e]=i),u.attribs.style=bc(s)}else if(typeof e=="object"){let s=Object.keys(e);for(let i=0;i<s.length;i++){let n=s[i];Sr(u,n,e[n],i)}}}function Ir(u,e){if(!u||!N(u))return;let t=hc(u.attribs.style);if(typeof e=="string")return t[e];if(Array.isArray(e)){let a={};for(let s of e)t[s]!=null&&(a[s]=t[s]);return a}return t}function bc(u){return Object.keys(u).reduce((e,t)=>`${e}${e?" ":""}${t}: ${u[t]};`,"")}function hc(u){if(u=(u||"").trim(),!u)return{};let e={},t;for(let a of u.split(";")){let s=a.indexOf(":");if(s<1||s===a.length-1){let i=a.trimEnd();i.length>0&&t!==void 0&&(e[t]+=`;${i}`)}else t=a.slice(0,s).trim(),e[t]=a.slice(s+1).trim()}return e}var V0={};$(V0,{serialize:()=>Ec,serializeArray:()=>pc});var Cr="input,select,textarea,keygen",mc=/%20/g,Lr=/\r?\n/g;function Ec(){return this.serializeArray().map(t=>`${encodeURIComponent(t.name)}=${encodeURIComponent(t.value)}`).join("&").replace(mc,"+")}function pc(){return this.map((u,e)=>{let t=this._make(e);return N(e)&&e.name==="form"?t.find(Cr).toArray():t.filter(Cr).toArray()}).filter('[name!=""]:enabled:not(:submit, :button, :image, :reset, :file):matches([checked], :not(:checkbox, :radio))').map((u,e)=>{var t;let a=this._make(e),s=a.attr("name"),i=(t=a.val())!==null&&t!==void 0?t:"";return Array.isArray(i)?i.map(n=>({name:s,value:n.replace(Lr,`\r
`)})):{name:s,value:i.replace(Lr,`\r
`)}}).toArray()}var G0={};$(G0,{extract:()=>gc});function Tc(u){var e;return typeof u=="string"?{selector:u,value:"textContent"}:{selector:u.selector,value:(e=u.value)!==null&&e!==void 0?e:"textContent"}}function gc(u){let e={};for(let t in u){let a=u[t],s=Array.isArray(a),{selector:i,value:n}=Tc(s?a[0]:a),d=typeof n=="function"?n:typeof n=="string"?l=>this._make(l).prop(n):l=>this._make(l).extract(n);if(s)e[t]=this._findBySelector(i,Number.POSITIVE_INFINITY).map((l,h)=>d(h,t,e)).get();else{let l=this._findBySelector(i,1);e[t]=l.length>0?d(l[0],t,e):void 0}}return e}var Ou=class{constructor(e,t,a){if(this.length=0,this.options=a,this._root=t,e){for(let s=0;s<e.length;s++)this[s]=e[s];this.length=e.length}}};Ou.prototype.cheerio="[cheerio object]";Ou.prototype.splice=Array.prototype.splice;Ou.prototype[Symbol.iterator]=Array.prototype[Symbol.iterator];Object.assign(Ou.prototype,p0,F0,q0,Y0,V0,G0);function Dr(u,e){return function t(a,s,i=!0){if(a==null)throw new Error("cheerio.load() expects a string");let n=be(s),d=u(a,n,i,null);class l extends Ou{_make(x,A){let S=h(x,A);return S.prevObject=this,S}_parse(x,A,S,_){return u(x,A,S,_)}_render(x){return e(x,this.options)}}function h(p,x,A=d,S){if(p&&tu(p))return p;let _=be(S,n),D=typeof A=="string"?[u(A,_,!1,null)]:"length"in A?A:[A],k=tu(D)?D:new l(D,null,_);if(k._root=k,!p)return new l(void 0,k,_);let xu=typeof p=="string"&&he(p)?u(p,_,!1,null).children:xc(p)?[p]:Array.isArray(p)?p:void 0,Au=new l(xu,k,_);if(xu)return Au;if(typeof p!="string")throw new TypeError("Unexpected type of selector");let ie=p,ne=x?typeof x=="string"?he(x)?new l([u(x,_,!1,null)],k,_):(ie=`${x} ${ie}`,k):tu(x)?x:new l(Array.isArray(x)?x:[x],k,_):k;return ne?ne.find(ie):Au}return Object.assign(h,o0,{load:t,_root:d,_options:n,fn:l.prototype,prototype:l.prototype}),h}}function xc(u){return!!u.name||u.type===Z.Root||u.type===Z.Text||u.type===Z.Comment}var Ac=new Set([65534,65535,131070,131071,196606,196607,262142,262143,327678,327679,393214,393215,458750,458751,524286,524287,589822,589823,655358,655359,720894,720895,786430,786431,851966,851967,917502,917503,983038,983039,1048574,1048575,1114110,1114111]),M="\uFFFD",c;(function(u){u[u.EOF=-1]="EOF",u[u.NULL=0]="NULL",u[u.TABULATION=9]="TABULATION",u[u.CARRIAGE_RETURN=13]="CARRIAGE_RETURN",u[u.LINE_FEED=10]="LINE_FEED",u[u.FORM_FEED=12]="FORM_FEED",u[u.SPACE=32]="SPACE",u[u.EXCLAMATION_MARK=33]="EXCLAMATION_MARK",u[u.QUOTATION_MARK=34]="QUOTATION_MARK",u[u.AMPERSAND=38]="AMPERSAND",u[u.APOSTROPHE=39]="APOSTROPHE",u[u.HYPHEN_MINUS=45]="HYPHEN_MINUS",u[u.SOLIDUS=47]="SOLIDUS",u[u.DIGIT_0=48]="DIGIT_0",u[u.DIGIT_9=57]="DIGIT_9",u[u.SEMICOLON=59]="SEMICOLON",u[u.LESS_THAN_SIGN=60]="LESS_THAN_SIGN",u[u.EQUALS_SIGN=61]="EQUALS_SIGN",u[u.GREATER_THAN_SIGN=62]="GREATER_THAN_SIGN",u[u.QUESTION_MARK=63]="QUESTION_MARK",u[u.LATIN_CAPITAL_A=65]="LATIN_CAPITAL_A",u[u.LATIN_CAPITAL_Z=90]="LATIN_CAPITAL_Z",u[u.RIGHT_SQUARE_BRACKET=93]="RIGHT_SQUARE_BRACKET",u[u.GRAVE_ACCENT=96]="GRAVE_ACCENT",u[u.LATIN_SMALL_A=97]="LATIN_SMALL_A",u[u.LATIN_SMALL_Z=122]="LATIN_SMALL_Z"})(c||(c={}));var X={DASH_DASH:"--",CDATA_START:"[CDATA[",DOCTYPE:"doctype",SCRIPT:"script",PUBLIC:"public",SYSTEM:"system"};function Tt(u){return u>=55296&&u<=57343}function Or(u){return u>=56320&&u<=57343}function Rr(u,e){return(u-55296)*1024+9216+e}function gt(u){return u!==32&&u!==10&&u!==13&&u!==9&&u!==12&&u>=1&&u<=31||u>=127&&u<=159}function xt(u){return u>=64976&&u<=65007||Ac.has(u)}var m;(function(u){u.controlCharacterInInputStream="control-character-in-input-stream",u.noncharacterInInputStream="noncharacter-in-input-stream",u.surrogateInInputStream="surrogate-in-input-stream",u.nonVoidHtmlElementStartTagWithTrailingSolidus="non-void-html-element-start-tag-with-trailing-solidus",u.endTagWithAttributes="end-tag-with-attributes",u.endTagWithTrailingSolidus="end-tag-with-trailing-solidus",u.unexpectedSolidusInTag="unexpected-solidus-in-tag",u.unexpectedNullCharacter="unexpected-null-character",u.unexpectedQuestionMarkInsteadOfTagName="unexpected-question-mark-instead-of-tag-name",u.invalidFirstCharacterOfTagName="invalid-first-character-of-tag-name",u.unexpectedEqualsSignBeforeAttributeName="unexpected-equals-sign-before-attribute-name",u.missingEndTagName="missing-end-tag-name",u.unexpectedCharacterInAttributeName="unexpected-character-in-attribute-name",u.unknownNamedCharacterReference="unknown-named-character-reference",u.missingSemicolonAfterCharacterReference="missing-semicolon-after-character-reference",u.unexpectedCharacterAfterDoctypeSystemIdentifier="unexpected-character-after-doctype-system-identifier",u.unexpectedCharacterInUnquotedAttributeValue="unexpected-character-in-unquoted-attribute-value",u.eofBeforeTagName="eof-before-tag-name",u.eofInTag="eof-in-tag",u.missingAttributeValue="missing-attribute-value",u.missingWhitespaceBetweenAttributes="missing-whitespace-between-attributes",u.missingWhitespaceAfterDoctypePublicKeyword="missing-whitespace-after-doctype-public-keyword",u.missingWhitespaceBetweenDoctypePublicAndSystemIdentifiers="missing-whitespace-between-doctype-public-and-system-identifiers",u.missingWhitespaceAfterDoctypeSystemKeyword="missing-whitespace-after-doctype-system-keyword",u.missingQuoteBeforeDoctypePublicIdentifier="missing-quote-before-doctype-public-identifier",u.missingQuoteBeforeDoctypeSystemIdentifier="missing-quote-before-doctype-system-identifier",u.missingDoctypePublicIdentifier="missing-doctype-public-identifier",u.missingDoctypeSystemIdentifier="missing-doctype-system-identifier",u.abruptDoctypePublicIdentifier="abrupt-doctype-public-identifier",u.abruptDoctypeSystemIdentifier="abrupt-doctype-system-identifier",u.cdataInHtmlContent="cdata-in-html-content",u.incorrectlyOpenedComment="incorrectly-opened-comment",u.eofInScriptHtmlCommentLikeText="eof-in-script-html-comment-like-text",u.eofInDoctype="eof-in-doctype",u.nestedComment="nested-comment",u.abruptClosingOfEmptyComment="abrupt-closing-of-empty-comment",u.eofInComment="eof-in-comment",u.incorrectlyClosedComment="incorrectly-closed-comment",u.eofInCdata="eof-in-cdata",u.absenceOfDigitsInNumericCharacterReference="absence-of-digits-in-numeric-character-reference",u.nullCharacterReference="null-character-reference",u.surrogateCharacterReference="surrogate-character-reference",u.characterReferenceOutsideUnicodeRange="character-reference-outside-unicode-range",u.controlCharacterReference="control-character-reference",u.noncharacterCharacterReference="noncharacter-character-reference",u.missingWhitespaceBeforeDoctypeName="missing-whitespace-before-doctype-name",u.missingDoctypeName="missing-doctype-name",u.invalidCharacterSequenceAfterDoctypeName="invalid-character-sequence-after-doctype-name",u.duplicateAttribute="duplicate-attribute",u.nonConformingDoctype="non-conforming-doctype",u.missingDoctype="missing-doctype",u.misplacedDoctype="misplaced-doctype",u.endTagWithoutMatchingOpenElement="end-tag-without-matching-open-element",u.closingOfElementWithOpenChildElements="closing-of-element-with-open-child-elements",u.disallowedContentInNoscriptInHead="disallowed-content-in-noscript-in-head",u.openElementsLeftAfterEof="open-elements-left-after-eof",u.abandonedHeadElementChild="abandoned-head-element-child",u.misplacedStartTagForHeadElement="misplaced-start-tag-for-head-element",u.nestedNoscriptInHead="nested-noscript-in-head",u.eofInElementThatCanContainOnlyText="eof-in-element-that-can-contain-only-text"})(m||(m={}));var Nc=1<<16,At=class{constructor(e){this.handler=e,this.html="",this.pos=-1,this.lastGapPos=-2,this.gapStack=[],this.skipNextNewLine=!1,this.lastChunkWritten=!1,this.endOfChunkHit=!1,this.bufferWaterline=Nc,this.isEol=!1,this.lineStartPos=0,this.droppedBufferSize=0,this.line=1,this.lastErrOffset=-1}get col(){return this.pos-this.lineStartPos+Number(this.lastGapPos!==this.pos)}get offset(){return this.droppedBufferSize+this.pos}getError(e,t){let{line:a,col:s,offset:i}=this,n=s+t,d=i+t;return{code:e,startLine:a,endLine:a,startCol:n,endCol:n,startOffset:d,endOffset:d}}_err(e){this.handler.onParseError&&this.lastErrOffset!==this.offset&&(this.lastErrOffset=this.offset,this.handler.onParseError(this.getError(e,0)))}_addGap(){this.gapStack.push(this.lastGapPos),this.lastGapPos=this.pos}_processSurrogate(e){if(this.pos!==this.html.length-1){let t=this.html.charCodeAt(this.pos+1);if(Or(t))return this.pos++,this._addGap(),Rr(e,t)}else if(!this.lastChunkWritten)return this.endOfChunkHit=!0,c.EOF;return this._err(m.surrogateInInputStream),e}willDropParsedChunk(){return this.pos>this.bufferWaterline}dropParsedChunk(){this.willDropParsedChunk()&&(this.html=this.html.substring(this.pos),this.lineStartPos-=this.pos,this.droppedBufferSize+=this.pos,this.pos=0,this.lastGapPos=-2,this.gapStack.length=0)}write(e,t){this.html.length>0?this.html+=e:this.html=e,this.endOfChunkHit=!1,this.lastChunkWritten=t}insertHtmlAtCurrentPos(e){this.html=this.html.substring(0,this.pos+1)+e+this.html.substring(this.pos+1),this.endOfChunkHit=!1}startsWith(e,t){if(this.pos+e.length>this.html.length)return this.endOfChunkHit=!this.lastChunkWritten,!1;if(t)return this.html.startsWith(e,this.pos);for(let a=0;a<e.length;a++)if((this.html.charCodeAt(this.pos+a)|32)!==e.charCodeAt(a))return!1;return!0}peek(e){let t=this.pos+e;if(t>=this.html.length)return this.endOfChunkHit=!this.lastChunkWritten,c.EOF;let a=this.html.charCodeAt(t);return a===c.CARRIAGE_RETURN?c.LINE_FEED:a}advance(){if(this.pos++,this.isEol&&(this.isEol=!1,this.line++,this.lineStartPos=this.pos),this.pos>=this.html.length)return this.endOfChunkHit=!this.lastChunkWritten,c.EOF;let e=this.html.charCodeAt(this.pos);return e===c.CARRIAGE_RETURN?(this.isEol=!0,this.skipNextNewLine=!0,c.LINE_FEED):e===c.LINE_FEED&&(this.isEol=!0,this.skipNextNewLine)?(this.line--,this.skipNextNewLine=!1,this._addGap(),this.advance()):(this.skipNextNewLine=!1,Tt(e)&&(e=this._processSurrogate(e)),this.handler.onParseError===null||e>31&&e<127||e===c.LINE_FEED||e===c.CARRIAGE_RETURN||e>159&&e<64976||this._checkForProblematicCharacters(e),e)}_checkForProblematicCharacters(e){gt(e)?this._err(m.controlCharacterInInputStream):xt(e)&&this._err(m.noncharacterInInputStream)}retreat(e){for(this.pos-=e;this.pos<this.lastGapPos;)this.lastGapPos=this.gapStack.pop(),this.pos--;this.isEol=!1}};var W0={};$(W0,{TokenType:()=>L,getTokenAttr:()=>Ie});var L;(function(u){u[u.CHARACTER=0]="CHARACTER",u[u.NULL_CHARACTER=1]="NULL_CHARACTER",u[u.WHITESPACE_CHARACTER=2]="WHITESPACE_CHARACTER",u[u.START_TAG=3]="START_TAG",u[u.END_TAG=4]="END_TAG",u[u.COMMENT=5]="COMMENT",u[u.DOCTYPE=6]="DOCTYPE",u[u.EOF=7]="EOF",u[u.HIBERNATION=8]="HIBERNATION"})(L||(L={}));function Ie(u,e){for(let t=u.attrs.length-1;t>=0;t--)if(u.attrs[t].name===e)return u.attrs[t].value;return null}var _t=new Uint16Array('\u1D41<\xD5\u0131\u028A\u049D\u057B\u05D0\u0675\u06DE\u07A2\u07D6\u080F\u0A4A\u0A91\u0DA1\u0E6D\u0F09\u0F26\u10CA\u1228\u12E1\u1415\u149D\u14C3\u14DF\u1525\0\0\0\0\0\0\u156B\u16CD\u198D\u1C12\u1DDD\u1F7E\u2060\u21B0\u228D\u23C0\u23FB\u2442\u2824\u2912\u2D08\u2E48\u2FCE\u3016\u32BA\u3639\u37AC\u38FE\u3A28\u3A71\u3AE0\u3B2E\u0800EMabcfglmnoprstu\\bfms\x7F\x84\x8B\x90\x95\x98\xA6\xB3\xB9\xC8\xCFlig\u803B\xC6\u40C6P\u803B&\u4026cute\u803B\xC1\u40C1reve;\u4102\u0100iyx}rc\u803B\xC2\u40C2;\u4410r;\uC000\u{1D504}rave\u803B\xC0\u40C0pha;\u4391acr;\u4100d;\u6A53\u0100gp\x9D\xA1on;\u4104f;\uC000\u{1D538}plyFunction;\u6061ing\u803B\xC5\u40C5\u0100cs\xBE\xC3r;\uC000\u{1D49C}ign;\u6254ilde\u803B\xC3\u40C3ml\u803B\xC4\u40C4\u0400aceforsu\xE5\xFB\xFE\u0117\u011C\u0122\u0127\u012A\u0100cr\xEA\xF2kslash;\u6216\u0176\xF6\xF8;\u6AE7ed;\u6306y;\u4411\u0180crt\u0105\u010B\u0114ause;\u6235noullis;\u612Ca;\u4392r;\uC000\u{1D505}pf;\uC000\u{1D539}eve;\u42D8c\xF2\u0113mpeq;\u624E\u0700HOacdefhilorsu\u014D\u0151\u0156\u0180\u019E\u01A2\u01B5\u01B7\u01BA\u01DC\u0215\u0273\u0278\u027Ecy;\u4427PY\u803B\xA9\u40A9\u0180cpy\u015D\u0162\u017Aute;\u4106\u0100;i\u0167\u0168\u62D2talDifferentialD;\u6145leys;\u612D\u0200aeio\u0189\u018E\u0194\u0198ron;\u410Cdil\u803B\xC7\u40C7rc;\u4108nint;\u6230ot;\u410A\u0100dn\u01A7\u01ADilla;\u40B8terDot;\u40B7\xF2\u017Fi;\u43A7rcle\u0200DMPT\u01C7\u01CB\u01D1\u01D6ot;\u6299inus;\u6296lus;\u6295imes;\u6297o\u0100cs\u01E2\u01F8kwiseContourIntegral;\u6232eCurly\u0100DQ\u0203\u020FoubleQuote;\u601Duote;\u6019\u0200lnpu\u021E\u0228\u0247\u0255on\u0100;e\u0225\u0226\u6237;\u6A74\u0180git\u022F\u0236\u023Aruent;\u6261nt;\u622FourIntegral;\u622E\u0100fr\u024C\u024E;\u6102oduct;\u6210nterClockwiseContourIntegral;\u6233oss;\u6A2Fcr;\uC000\u{1D49E}p\u0100;C\u0284\u0285\u62D3ap;\u624D\u0580DJSZacefios\u02A0\u02AC\u02B0\u02B4\u02B8\u02CB\u02D7\u02E1\u02E6\u0333\u048D\u0100;o\u0179\u02A5trahd;\u6911cy;\u4402cy;\u4405cy;\u440F\u0180grs\u02BF\u02C4\u02C7ger;\u6021r;\u61A1hv;\u6AE4\u0100ay\u02D0\u02D5ron;\u410E;\u4414l\u0100;t\u02DD\u02DE\u6207a;\u4394r;\uC000\u{1D507}\u0100af\u02EB\u0327\u0100cm\u02F0\u0322ritical\u0200ADGT\u0300\u0306\u0316\u031Ccute;\u40B4o\u0174\u030B\u030D;\u42D9bleAcute;\u42DDrave;\u4060ilde;\u42DCond;\u62C4ferentialD;\u6146\u0470\u033D\0\0\0\u0342\u0354\0\u0405f;\uC000\u{1D53B}\u0180;DE\u0348\u0349\u034D\u40A8ot;\u60DCqual;\u6250ble\u0300CDLRUV\u0363\u0372\u0382\u03CF\u03E2\u03F8ontourIntegra\xEC\u0239o\u0274\u0379\0\0\u037B\xBB\u0349nArrow;\u61D3\u0100eo\u0387\u03A4ft\u0180ART\u0390\u0396\u03A1rrow;\u61D0ightArrow;\u61D4e\xE5\u02CAng\u0100LR\u03AB\u03C4eft\u0100AR\u03B3\u03B9rrow;\u67F8ightArrow;\u67FAightArrow;\u67F9ight\u0100AT\u03D8\u03DErrow;\u61D2ee;\u62A8p\u0241\u03E9\0\0\u03EFrrow;\u61D1ownArrow;\u61D5erticalBar;\u6225n\u0300ABLRTa\u0412\u042A\u0430\u045E\u047F\u037Crrow\u0180;BU\u041D\u041E\u0422\u6193ar;\u6913pArrow;\u61F5reve;\u4311eft\u02D2\u043A\0\u0446\0\u0450ightVector;\u6950eeVector;\u695Eector\u0100;B\u0459\u045A\u61BDar;\u6956ight\u01D4\u0467\0\u0471eeVector;\u695Fector\u0100;B\u047A\u047B\u61C1ar;\u6957ee\u0100;A\u0486\u0487\u62A4rrow;\u61A7\u0100ct\u0492\u0497r;\uC000\u{1D49F}rok;\u4110\u0800NTacdfglmopqstux\u04BD\u04C0\u04C4\u04CB\u04DE\u04E2\u04E7\u04EE\u04F5\u0521\u052F\u0536\u0552\u055D\u0560\u0565G;\u414AH\u803B\xD0\u40D0cute\u803B\xC9\u40C9\u0180aiy\u04D2\u04D7\u04DCron;\u411Arc\u803B\xCA\u40CA;\u442Dot;\u4116r;\uC000\u{1D508}rave\u803B\xC8\u40C8ement;\u6208\u0100ap\u04FA\u04FEcr;\u4112ty\u0253\u0506\0\0\u0512mallSquare;\u65FBerySmallSquare;\u65AB\u0100gp\u0526\u052Aon;\u4118f;\uC000\u{1D53C}silon;\u4395u\u0100ai\u053C\u0549l\u0100;T\u0542\u0543\u6A75ilde;\u6242librium;\u61CC\u0100ci\u0557\u055Ar;\u6130m;\u6A73a;\u4397ml\u803B\xCB\u40CB\u0100ip\u056A\u056Fsts;\u6203onentialE;\u6147\u0280cfios\u0585\u0588\u058D\u05B2\u05CCy;\u4424r;\uC000\u{1D509}lled\u0253\u0597\0\0\u05A3mallSquare;\u65FCerySmallSquare;\u65AA\u0370\u05BA\0\u05BF\0\0\u05C4f;\uC000\u{1D53D}All;\u6200riertrf;\u6131c\xF2\u05CB\u0600JTabcdfgorst\u05E8\u05EC\u05EF\u05FA\u0600\u0612\u0616\u061B\u061D\u0623\u066C\u0672cy;\u4403\u803B>\u403Emma\u0100;d\u05F7\u05F8\u4393;\u43DCreve;\u411E\u0180eiy\u0607\u060C\u0610dil;\u4122rc;\u411C;\u4413ot;\u4120r;\uC000\u{1D50A};\u62D9pf;\uC000\u{1D53E}eater\u0300EFGLST\u0635\u0644\u064E\u0656\u065B\u0666qual\u0100;L\u063E\u063F\u6265ess;\u62DBullEqual;\u6267reater;\u6AA2ess;\u6277lantEqual;\u6A7Eilde;\u6273cr;\uC000\u{1D4A2};\u626B\u0400Aacfiosu\u0685\u068B\u0696\u069B\u069E\u06AA\u06BE\u06CARDcy;\u442A\u0100ct\u0690\u0694ek;\u42C7;\u405Eirc;\u4124r;\u610ClbertSpace;\u610B\u01F0\u06AF\0\u06B2f;\u610DizontalLine;\u6500\u0100ct\u06C3\u06C5\xF2\u06A9rok;\u4126mp\u0144\u06D0\u06D8ownHum\xF0\u012Fqual;\u624F\u0700EJOacdfgmnostu\u06FA\u06FE\u0703\u0707\u070E\u071A\u071E\u0721\u0728\u0744\u0778\u078B\u078F\u0795cy;\u4415lig;\u4132cy;\u4401cute\u803B\xCD\u40CD\u0100iy\u0713\u0718rc\u803B\xCE\u40CE;\u4418ot;\u4130r;\u6111rave\u803B\xCC\u40CC\u0180;ap\u0720\u072F\u073F\u0100cg\u0734\u0737r;\u412AinaryI;\u6148lie\xF3\u03DD\u01F4\u0749\0\u0762\u0100;e\u074D\u074E\u622C\u0100gr\u0753\u0758ral;\u622Bsection;\u62C2isible\u0100CT\u076C\u0772omma;\u6063imes;\u6062\u0180gpt\u077F\u0783\u0788on;\u412Ef;\uC000\u{1D540}a;\u4399cr;\u6110ilde;\u4128\u01EB\u079A\0\u079Ecy;\u4406l\u803B\xCF\u40CF\u0280cfosu\u07AC\u07B7\u07BC\u07C2\u07D0\u0100iy\u07B1\u07B5rc;\u4134;\u4419r;\uC000\u{1D50D}pf;\uC000\u{1D541}\u01E3\u07C7\0\u07CCr;\uC000\u{1D4A5}rcy;\u4408kcy;\u4404\u0380HJacfos\u07E4\u07E8\u07EC\u07F1\u07FD\u0802\u0808cy;\u4425cy;\u440Cppa;\u439A\u0100ey\u07F6\u07FBdil;\u4136;\u441Ar;\uC000\u{1D50E}pf;\uC000\u{1D542}cr;\uC000\u{1D4A6}\u0580JTaceflmost\u0825\u0829\u082C\u0850\u0863\u09B3\u09B8\u09C7\u09CD\u0A37\u0A47cy;\u4409\u803B<\u403C\u0280cmnpr\u0837\u083C\u0841\u0844\u084Dute;\u4139bda;\u439Bg;\u67EAlacetrf;\u6112r;\u619E\u0180aey\u0857\u085C\u0861ron;\u413Ddil;\u413B;\u441B\u0100fs\u0868\u0970t\u0500ACDFRTUVar\u087E\u08A9\u08B1\u08E0\u08E6\u08FC\u092F\u095B\u0390\u096A\u0100nr\u0883\u088FgleBracket;\u67E8row\u0180;BR\u0899\u089A\u089E\u6190ar;\u61E4ightArrow;\u61C6eiling;\u6308o\u01F5\u08B7\0\u08C3bleBracket;\u67E6n\u01D4\u08C8\0\u08D2eeVector;\u6961ector\u0100;B\u08DB\u08DC\u61C3ar;\u6959loor;\u630Aight\u0100AV\u08EF\u08F5rrow;\u6194ector;\u694E\u0100er\u0901\u0917e\u0180;AV\u0909\u090A\u0910\u62A3rrow;\u61A4ector;\u695Aiangle\u0180;BE\u0924\u0925\u0929\u62B2ar;\u69CFqual;\u62B4p\u0180DTV\u0937\u0942\u094CownVector;\u6951eeVector;\u6960ector\u0100;B\u0956\u0957\u61BFar;\u6958ector\u0100;B\u0965\u0966\u61BCar;\u6952ight\xE1\u039Cs\u0300EFGLST\u097E\u098B\u0995\u099D\u09A2\u09ADqualGreater;\u62DAullEqual;\u6266reater;\u6276ess;\u6AA1lantEqual;\u6A7Dilde;\u6272r;\uC000\u{1D50F}\u0100;e\u09BD\u09BE\u62D8ftarrow;\u61DAidot;\u413F\u0180npw\u09D4\u0A16\u0A1Bg\u0200LRlr\u09DE\u09F7\u0A02\u0A10eft\u0100AR\u09E6\u09ECrrow;\u67F5ightArrow;\u67F7ightArrow;\u67F6eft\u0100ar\u03B3\u0A0Aight\xE1\u03BFight\xE1\u03CAf;\uC000\u{1D543}er\u0100LR\u0A22\u0A2CeftArrow;\u6199ightArrow;\u6198\u0180cht\u0A3E\u0A40\u0A42\xF2\u084C;\u61B0rok;\u4141;\u626A\u0400acefiosu\u0A5A\u0A5D\u0A60\u0A77\u0A7C\u0A85\u0A8B\u0A8Ep;\u6905y;\u441C\u0100dl\u0A65\u0A6FiumSpace;\u605Flintrf;\u6133r;\uC000\u{1D510}nusPlus;\u6213pf;\uC000\u{1D544}c\xF2\u0A76;\u439C\u0480Jacefostu\u0AA3\u0AA7\u0AAD\u0AC0\u0B14\u0B19\u0D91\u0D97\u0D9Ecy;\u440Acute;\u4143\u0180aey\u0AB4\u0AB9\u0ABEron;\u4147dil;\u4145;\u441D\u0180gsw\u0AC7\u0AF0\u0B0Eative\u0180MTV\u0AD3\u0ADF\u0AE8ediumSpace;\u600Bhi\u0100cn\u0AE6\u0AD8\xEB\u0AD9eryThi\xEE\u0AD9ted\u0100GL\u0AF8\u0B06reaterGreate\xF2\u0673essLes\xF3\u0A48Line;\u400Ar;\uC000\u{1D511}\u0200Bnpt\u0B22\u0B28\u0B37\u0B3Areak;\u6060BreakingSpace;\u40A0f;\u6115\u0680;CDEGHLNPRSTV\u0B55\u0B56\u0B6A\u0B7C\u0BA1\u0BEB\u0C04\u0C5E\u0C84\u0CA6\u0CD8\u0D61\u0D85\u6AEC\u0100ou\u0B5B\u0B64ngruent;\u6262pCap;\u626DoubleVerticalBar;\u6226\u0180lqx\u0B83\u0B8A\u0B9Bement;\u6209ual\u0100;T\u0B92\u0B93\u6260ilde;\uC000\u2242\u0338ists;\u6204reater\u0380;EFGLST\u0BB6\u0BB7\u0BBD\u0BC9\u0BD3\u0BD8\u0BE5\u626Fqual;\u6271ullEqual;\uC000\u2267\u0338reater;\uC000\u226B\u0338ess;\u6279lantEqual;\uC000\u2A7E\u0338ilde;\u6275ump\u0144\u0BF2\u0BFDownHump;\uC000\u224E\u0338qual;\uC000\u224F\u0338e\u0100fs\u0C0A\u0C27tTriangle\u0180;BE\u0C1A\u0C1B\u0C21\u62EAar;\uC000\u29CF\u0338qual;\u62ECs\u0300;EGLST\u0C35\u0C36\u0C3C\u0C44\u0C4B\u0C58\u626Equal;\u6270reater;\u6278ess;\uC000\u226A\u0338lantEqual;\uC000\u2A7D\u0338ilde;\u6274ested\u0100GL\u0C68\u0C79reaterGreater;\uC000\u2AA2\u0338essLess;\uC000\u2AA1\u0338recedes\u0180;ES\u0C92\u0C93\u0C9B\u6280qual;\uC000\u2AAF\u0338lantEqual;\u62E0\u0100ei\u0CAB\u0CB9verseElement;\u620CghtTriangle\u0180;BE\u0CCB\u0CCC\u0CD2\u62EBar;\uC000\u29D0\u0338qual;\u62ED\u0100qu\u0CDD\u0D0CuareSu\u0100bp\u0CE8\u0CF9set\u0100;E\u0CF0\u0CF3\uC000\u228F\u0338qual;\u62E2erset\u0100;E\u0D03\u0D06\uC000\u2290\u0338qual;\u62E3\u0180bcp\u0D13\u0D24\u0D4Eset\u0100;E\u0D1B\u0D1E\uC000\u2282\u20D2qual;\u6288ceeds\u0200;EST\u0D32\u0D33\u0D3B\u0D46\u6281qual;\uC000\u2AB0\u0338lantEqual;\u62E1ilde;\uC000\u227F\u0338erset\u0100;E\u0D58\u0D5B\uC000\u2283\u20D2qual;\u6289ilde\u0200;EFT\u0D6E\u0D6F\u0D75\u0D7F\u6241qual;\u6244ullEqual;\u6247ilde;\u6249erticalBar;\u6224cr;\uC000\u{1D4A9}ilde\u803B\xD1\u40D1;\u439D\u0700Eacdfgmoprstuv\u0DBD\u0DC2\u0DC9\u0DD5\u0DDB\u0DE0\u0DE7\u0DFC\u0E02\u0E20\u0E22\u0E32\u0E3F\u0E44lig;\u4152cute\u803B\xD3\u40D3\u0100iy\u0DCE\u0DD3rc\u803B\xD4\u40D4;\u441Eblac;\u4150r;\uC000\u{1D512}rave\u803B\xD2\u40D2\u0180aei\u0DEE\u0DF2\u0DF6cr;\u414Cga;\u43A9cron;\u439Fpf;\uC000\u{1D546}enCurly\u0100DQ\u0E0E\u0E1AoubleQuote;\u601Cuote;\u6018;\u6A54\u0100cl\u0E27\u0E2Cr;\uC000\u{1D4AA}ash\u803B\xD8\u40D8i\u016C\u0E37\u0E3Cde\u803B\xD5\u40D5es;\u6A37ml\u803B\xD6\u40D6er\u0100BP\u0E4B\u0E60\u0100ar\u0E50\u0E53r;\u603Eac\u0100ek\u0E5A\u0E5C;\u63DEet;\u63B4arenthesis;\u63DC\u0480acfhilors\u0E7F\u0E87\u0E8A\u0E8F\u0E92\u0E94\u0E9D\u0EB0\u0EFCrtialD;\u6202y;\u441Fr;\uC000\u{1D513}i;\u43A6;\u43A0usMinus;\u40B1\u0100ip\u0EA2\u0EADncareplan\xE5\u069Df;\u6119\u0200;eio\u0EB9\u0EBA\u0EE0\u0EE4\u6ABBcedes\u0200;EST\u0EC8\u0EC9\u0ECF\u0EDA\u627Aqual;\u6AAFlantEqual;\u627Cilde;\u627Eme;\u6033\u0100dp\u0EE9\u0EEEuct;\u620Fortion\u0100;a\u0225\u0EF9l;\u621D\u0100ci\u0F01\u0F06r;\uC000\u{1D4AB};\u43A8\u0200Ufos\u0F11\u0F16\u0F1B\u0F1FOT\u803B"\u4022r;\uC000\u{1D514}pf;\u611Acr;\uC000\u{1D4AC}\u0600BEacefhiorsu\u0F3E\u0F43\u0F47\u0F60\u0F73\u0FA7\u0FAA\u0FAD\u1096\u10A9\u10B4\u10BEarr;\u6910G\u803B\xAE\u40AE\u0180cnr\u0F4E\u0F53\u0F56ute;\u4154g;\u67EBr\u0100;t\u0F5C\u0F5D\u61A0l;\u6916\u0180aey\u0F67\u0F6C\u0F71ron;\u4158dil;\u4156;\u4420\u0100;v\u0F78\u0F79\u611Cerse\u0100EU\u0F82\u0F99\u0100lq\u0F87\u0F8Eement;\u620Builibrium;\u61CBpEquilibrium;\u696Fr\xBB\u0F79o;\u43A1ght\u0400ACDFTUVa\u0FC1\u0FEB\u0FF3\u1022\u1028\u105B\u1087\u03D8\u0100nr\u0FC6\u0FD2gleBracket;\u67E9row\u0180;BL\u0FDC\u0FDD\u0FE1\u6192ar;\u61E5eftArrow;\u61C4eiling;\u6309o\u01F5\u0FF9\0\u1005bleBracket;\u67E7n\u01D4\u100A\0\u1014eeVector;\u695Dector\u0100;B\u101D\u101E\u61C2ar;\u6955loor;\u630B\u0100er\u102D\u1043e\u0180;AV\u1035\u1036\u103C\u62A2rrow;\u61A6ector;\u695Biangle\u0180;BE\u1050\u1051\u1055\u62B3ar;\u69D0qual;\u62B5p\u0180DTV\u1063\u106E\u1078ownVector;\u694FeeVector;\u695Cector\u0100;B\u1082\u1083\u61BEar;\u6954ector\u0100;B\u1091\u1092\u61C0ar;\u6953\u0100pu\u109B\u109Ef;\u611DndImplies;\u6970ightarrow;\u61DB\u0100ch\u10B9\u10BCr;\u611B;\u61B1leDelayed;\u69F4\u0680HOacfhimoqstu\u10E4\u10F1\u10F7\u10FD\u1119\u111E\u1151\u1156\u1161\u1167\u11B5\u11BB\u11BF\u0100Cc\u10E9\u10EEHcy;\u4429y;\u4428FTcy;\u442Ccute;\u415A\u0280;aeiy\u1108\u1109\u110E\u1113\u1117\u6ABCron;\u4160dil;\u415Erc;\u415C;\u4421r;\uC000\u{1D516}ort\u0200DLRU\u112A\u1134\u113E\u1149ownArrow\xBB\u041EeftArrow\xBB\u089AightArrow\xBB\u0FDDpArrow;\u6191gma;\u43A3allCircle;\u6218pf;\uC000\u{1D54A}\u0272\u116D\0\0\u1170t;\u621Aare\u0200;ISU\u117B\u117C\u1189\u11AF\u65A1ntersection;\u6293u\u0100bp\u118F\u119Eset\u0100;E\u1197\u1198\u628Fqual;\u6291erset\u0100;E\u11A8\u11A9\u6290qual;\u6292nion;\u6294cr;\uC000\u{1D4AE}ar;\u62C6\u0200bcmp\u11C8\u11DB\u1209\u120B\u0100;s\u11CD\u11CE\u62D0et\u0100;E\u11CD\u11D5qual;\u6286\u0100ch\u11E0\u1205eeds\u0200;EST\u11ED\u11EE\u11F4\u11FF\u627Bqual;\u6AB0lantEqual;\u627Dilde;\u627FTh\xE1\u0F8C;\u6211\u0180;es\u1212\u1213\u1223\u62D1rset\u0100;E\u121C\u121D\u6283qual;\u6287et\xBB\u1213\u0580HRSacfhiors\u123E\u1244\u1249\u1255\u125E\u1271\u1276\u129F\u12C2\u12C8\u12D1ORN\u803B\xDE\u40DEADE;\u6122\u0100Hc\u124E\u1252cy;\u440By;\u4426\u0100bu\u125A\u125C;\u4009;\u43A4\u0180aey\u1265\u126A\u126Fron;\u4164dil;\u4162;\u4422r;\uC000\u{1D517}\u0100ei\u127B\u1289\u01F2\u1280\0\u1287efore;\u6234a;\u4398\u0100cn\u128E\u1298kSpace;\uC000\u205F\u200ASpace;\u6009lde\u0200;EFT\u12AB\u12AC\u12B2\u12BC\u623Cqual;\u6243ullEqual;\u6245ilde;\u6248pf;\uC000\u{1D54B}ipleDot;\u60DB\u0100ct\u12D6\u12DBr;\uC000\u{1D4AF}rok;\u4166\u0AE1\u12F7\u130E\u131A\u1326\0\u132C\u1331\0\0\0\0\0\u1338\u133D\u1377\u1385\0\u13FF\u1404\u140A\u1410\u0100cr\u12FB\u1301ute\u803B\xDA\u40DAr\u0100;o\u1307\u1308\u619Fcir;\u6949r\u01E3\u1313\0\u1316y;\u440Eve;\u416C\u0100iy\u131E\u1323rc\u803B\xDB\u40DB;\u4423blac;\u4170r;\uC000\u{1D518}rave\u803B\xD9\u40D9acr;\u416A\u0100di\u1341\u1369er\u0100BP\u1348\u135D\u0100ar\u134D\u1350r;\u405Fac\u0100ek\u1357\u1359;\u63DFet;\u63B5arenthesis;\u63DDon\u0100;P\u1370\u1371\u62C3lus;\u628E\u0100gp\u137B\u137Fon;\u4172f;\uC000\u{1D54C}\u0400ADETadps\u1395\u13AE\u13B8\u13C4\u03E8\u13D2\u13D7\u13F3rrow\u0180;BD\u1150\u13A0\u13A4ar;\u6912ownArrow;\u61C5ownArrow;\u6195quilibrium;\u696Eee\u0100;A\u13CB\u13CC\u62A5rrow;\u61A5own\xE1\u03F3er\u0100LR\u13DE\u13E8eftArrow;\u6196ightArrow;\u6197i\u0100;l\u13F9\u13FA\u43D2on;\u43A5ing;\u416Ecr;\uC000\u{1D4B0}ilde;\u4168ml\u803B\xDC\u40DC\u0480Dbcdefosv\u1427\u142C\u1430\u1433\u143E\u1485\u148A\u1490\u1496ash;\u62ABar;\u6AEBy;\u4412ash\u0100;l\u143B\u143C\u62A9;\u6AE6\u0100er\u1443\u1445;\u62C1\u0180bty\u144C\u1450\u147Aar;\u6016\u0100;i\u144F\u1455cal\u0200BLST\u1461\u1465\u146A\u1474ar;\u6223ine;\u407Ceparator;\u6758ilde;\u6240ThinSpace;\u600Ar;\uC000\u{1D519}pf;\uC000\u{1D54D}cr;\uC000\u{1D4B1}dash;\u62AA\u0280cefos\u14A7\u14AC\u14B1\u14B6\u14BCirc;\u4174dge;\u62C0r;\uC000\u{1D51A}pf;\uC000\u{1D54E}cr;\uC000\u{1D4B2}\u0200fios\u14CB\u14D0\u14D2\u14D8r;\uC000\u{1D51B};\u439Epf;\uC000\u{1D54F}cr;\uC000\u{1D4B3}\u0480AIUacfosu\u14F1\u14F5\u14F9\u14FD\u1504\u150F\u1514\u151A\u1520cy;\u442Fcy;\u4407cy;\u442Ecute\u803B\xDD\u40DD\u0100iy\u1509\u150Drc;\u4176;\u442Br;\uC000\u{1D51C}pf;\uC000\u{1D550}cr;\uC000\u{1D4B4}ml;\u4178\u0400Hacdefos\u1535\u1539\u153F\u154B\u154F\u155D\u1560\u1564cy;\u4416cute;\u4179\u0100ay\u1544\u1549ron;\u417D;\u4417ot;\u417B\u01F2\u1554\0\u155BoWidt\xE8\u0AD9a;\u4396r;\u6128pf;\u6124cr;\uC000\u{1D4B5}\u0BE1\u1583\u158A\u1590\0\u15B0\u15B6\u15BF\0\0\0\0\u15C6\u15DB\u15EB\u165F\u166D\0\u1695\u169B\u16B2\u16B9\0\u16BEcute\u803B\xE1\u40E1reve;\u4103\u0300;Ediuy\u159C\u159D\u15A1\u15A3\u15A8\u15AD\u623E;\uC000\u223E\u0333;\u623Frc\u803B\xE2\u40E2te\u80BB\xB4\u0306;\u4430lig\u803B\xE6\u40E6\u0100;r\xB2\u15BA;\uC000\u{1D51E}rave\u803B\xE0\u40E0\u0100ep\u15CA\u15D6\u0100fp\u15CF\u15D4sym;\u6135\xE8\u15D3ha;\u43B1\u0100ap\u15DFc\u0100cl\u15E4\u15E7r;\u4101g;\u6A3F\u0264\u15F0\0\0\u160A\u0280;adsv\u15FA\u15FB\u15FF\u1601\u1607\u6227nd;\u6A55;\u6A5Clope;\u6A58;\u6A5A\u0380;elmrsz\u1618\u1619\u161B\u161E\u163F\u164F\u1659\u6220;\u69A4e\xBB\u1619sd\u0100;a\u1625\u1626\u6221\u0461\u1630\u1632\u1634\u1636\u1638\u163A\u163C\u163E;\u69A8;\u69A9;\u69AA;\u69AB;\u69AC;\u69AD;\u69AE;\u69AFt\u0100;v\u1645\u1646\u621Fb\u0100;d\u164C\u164D\u62BE;\u699D\u0100pt\u1654\u1657h;\u6222\xBB\xB9arr;\u637C\u0100gp\u1663\u1667on;\u4105f;\uC000\u{1D552}\u0380;Eaeiop\u12C1\u167B\u167D\u1682\u1684\u1687\u168A;\u6A70cir;\u6A6F;\u624Ad;\u624Bs;\u4027rox\u0100;e\u12C1\u1692\xF1\u1683ing\u803B\xE5\u40E5\u0180cty\u16A1\u16A6\u16A8r;\uC000\u{1D4B6};\u402Amp\u0100;e\u12C1\u16AF\xF1\u0288ilde\u803B\xE3\u40E3ml\u803B\xE4\u40E4\u0100ci\u16C2\u16C8onin\xF4\u0272nt;\u6A11\u0800Nabcdefiklnoprsu\u16ED\u16F1\u1730\u173C\u1743\u1748\u1778\u177D\u17E0\u17E6\u1839\u1850\u170D\u193D\u1948\u1970ot;\u6AED\u0100cr\u16F6\u171Ek\u0200ceps\u1700\u1705\u170D\u1713ong;\u624Cpsilon;\u43F6rime;\u6035im\u0100;e\u171A\u171B\u623Dq;\u62CD\u0176\u1722\u1726ee;\u62BDed\u0100;g\u172C\u172D\u6305e\xBB\u172Drk\u0100;t\u135C\u1737brk;\u63B6\u0100oy\u1701\u1741;\u4431quo;\u601E\u0280cmprt\u1753\u175B\u1761\u1764\u1768aus\u0100;e\u010A\u0109ptyv;\u69B0s\xE9\u170Cno\xF5\u0113\u0180ahw\u176F\u1771\u1773;\u43B2;\u6136een;\u626Cr;\uC000\u{1D51F}g\u0380costuvw\u178D\u179D\u17B3\u17C1\u17D5\u17DB\u17DE\u0180aiu\u1794\u1796\u179A\xF0\u0760rc;\u65EFp\xBB\u1371\u0180dpt\u17A4\u17A8\u17ADot;\u6A00lus;\u6A01imes;\u6A02\u0271\u17B9\0\0\u17BEcup;\u6A06ar;\u6605riangle\u0100du\u17CD\u17D2own;\u65BDp;\u65B3plus;\u6A04e\xE5\u1444\xE5\u14ADarow;\u690D\u0180ako\u17ED\u1826\u1835\u0100cn\u17F2\u1823k\u0180lst\u17FA\u05AB\u1802ozenge;\u69EBriangle\u0200;dlr\u1812\u1813\u1818\u181D\u65B4own;\u65BEeft;\u65C2ight;\u65B8k;\u6423\u01B1\u182B\0\u1833\u01B2\u182F\0\u1831;\u6592;\u65914;\u6593ck;\u6588\u0100eo\u183E\u184D\u0100;q\u1843\u1846\uC000=\u20E5uiv;\uC000\u2261\u20E5t;\u6310\u0200ptwx\u1859\u185E\u1867\u186Cf;\uC000\u{1D553}\u0100;t\u13CB\u1863om\xBB\u13CCtie;\u62C8\u0600DHUVbdhmptuv\u1885\u1896\u18AA\u18BB\u18D7\u18DB\u18EC\u18FF\u1905\u190A\u1910\u1921\u0200LRlr\u188E\u1890\u1892\u1894;\u6557;\u6554;\u6556;\u6553\u0280;DUdu\u18A1\u18A2\u18A4\u18A6\u18A8\u6550;\u6566;\u6569;\u6564;\u6567\u0200LRlr\u18B3\u18B5\u18B7\u18B9;\u655D;\u655A;\u655C;\u6559\u0380;HLRhlr\u18CA\u18CB\u18CD\u18CF\u18D1\u18D3\u18D5\u6551;\u656C;\u6563;\u6560;\u656B;\u6562;\u655Fox;\u69C9\u0200LRlr\u18E4\u18E6\u18E8\u18EA;\u6555;\u6552;\u6510;\u650C\u0280;DUdu\u06BD\u18F7\u18F9\u18FB\u18FD;\u6565;\u6568;\u652C;\u6534inus;\u629Flus;\u629Eimes;\u62A0\u0200LRlr\u1919\u191B\u191D\u191F;\u655B;\u6558;\u6518;\u6514\u0380;HLRhlr\u1930\u1931\u1933\u1935\u1937\u1939\u193B\u6502;\u656A;\u6561;\u655E;\u653C;\u6524;\u651C\u0100ev\u0123\u1942bar\u803B\xA6\u40A6\u0200ceio\u1951\u1956\u195A\u1960r;\uC000\u{1D4B7}mi;\u604Fm\u0100;e\u171A\u171Cl\u0180;bh\u1968\u1969\u196B\u405C;\u69C5sub;\u67C8\u016C\u1974\u197El\u0100;e\u1979\u197A\u6022t\xBB\u197Ap\u0180;Ee\u012F\u1985\u1987;\u6AAE\u0100;q\u06DC\u06DB\u0CE1\u19A7\0\u19E8\u1A11\u1A15\u1A32\0\u1A37\u1A50\0\0\u1AB4\0\0\u1AC1\0\0\u1B21\u1B2E\u1B4D\u1B52\0\u1BFD\0\u1C0C\u0180cpr\u19AD\u19B2\u19DDute;\u4107\u0300;abcds\u19BF\u19C0\u19C4\u19CA\u19D5\u19D9\u6229nd;\u6A44rcup;\u6A49\u0100au\u19CF\u19D2p;\u6A4Bp;\u6A47ot;\u6A40;\uC000\u2229\uFE00\u0100eo\u19E2\u19E5t;\u6041\xEE\u0693\u0200aeiu\u19F0\u19FB\u1A01\u1A05\u01F0\u19F5\0\u19F8s;\u6A4Don;\u410Ddil\u803B\xE7\u40E7rc;\u4109ps\u0100;s\u1A0C\u1A0D\u6A4Cm;\u6A50ot;\u410B\u0180dmn\u1A1B\u1A20\u1A26il\u80BB\xB8\u01ADptyv;\u69B2t\u8100\xA2;e\u1A2D\u1A2E\u40A2r\xE4\u01B2r;\uC000\u{1D520}\u0180cei\u1A3D\u1A40\u1A4Dy;\u4447ck\u0100;m\u1A47\u1A48\u6713ark\xBB\u1A48;\u43C7r\u0380;Ecefms\u1A5F\u1A60\u1A62\u1A6B\u1AA4\u1AAA\u1AAE\u65CB;\u69C3\u0180;el\u1A69\u1A6A\u1A6D\u42C6q;\u6257e\u0261\u1A74\0\0\u1A88rrow\u0100lr\u1A7C\u1A81eft;\u61BAight;\u61BB\u0280RSacd\u1A92\u1A94\u1A96\u1A9A\u1A9F\xBB\u0F47;\u64C8st;\u629Birc;\u629Aash;\u629Dnint;\u6A10id;\u6AEFcir;\u69C2ubs\u0100;u\u1ABB\u1ABC\u6663it\xBB\u1ABC\u02EC\u1AC7\u1AD4\u1AFA\0\u1B0Aon\u0100;e\u1ACD\u1ACE\u403A\u0100;q\xC7\xC6\u026D\u1AD9\0\0\u1AE2a\u0100;t\u1ADE\u1ADF\u402C;\u4040\u0180;fl\u1AE8\u1AE9\u1AEB\u6201\xEE\u1160e\u0100mx\u1AF1\u1AF6ent\xBB\u1AE9e\xF3\u024D\u01E7\u1AFE\0\u1B07\u0100;d\u12BB\u1B02ot;\u6A6Dn\xF4\u0246\u0180fry\u1B10\u1B14\u1B17;\uC000\u{1D554}o\xE4\u0254\u8100\xA9;s\u0155\u1B1Dr;\u6117\u0100ao\u1B25\u1B29rr;\u61B5ss;\u6717\u0100cu\u1B32\u1B37r;\uC000\u{1D4B8}\u0100bp\u1B3C\u1B44\u0100;e\u1B41\u1B42\u6ACF;\u6AD1\u0100;e\u1B49\u1B4A\u6AD0;\u6AD2dot;\u62EF\u0380delprvw\u1B60\u1B6C\u1B77\u1B82\u1BAC\u1BD4\u1BF9arr\u0100lr\u1B68\u1B6A;\u6938;\u6935\u0270\u1B72\0\0\u1B75r;\u62DEc;\u62DFarr\u0100;p\u1B7F\u1B80\u61B6;\u693D\u0300;bcdos\u1B8F\u1B90\u1B96\u1BA1\u1BA5\u1BA8\u622Arcap;\u6A48\u0100au\u1B9B\u1B9Ep;\u6A46p;\u6A4Aot;\u628Dr;\u6A45;\uC000\u222A\uFE00\u0200alrv\u1BB5\u1BBF\u1BDE\u1BE3rr\u0100;m\u1BBC\u1BBD\u61B7;\u693Cy\u0180evw\u1BC7\u1BD4\u1BD8q\u0270\u1BCE\0\0\u1BD2re\xE3\u1B73u\xE3\u1B75ee;\u62CEedge;\u62CFen\u803B\xA4\u40A4earrow\u0100lr\u1BEE\u1BF3eft\xBB\u1B80ight\xBB\u1BBDe\xE4\u1BDD\u0100ci\u1C01\u1C07onin\xF4\u01F7nt;\u6231lcty;\u632D\u0980AHabcdefhijlorstuwz\u1C38\u1C3B\u1C3F\u1C5D\u1C69\u1C75\u1C8A\u1C9E\u1CAC\u1CB7\u1CFB\u1CFF\u1D0D\u1D7B\u1D91\u1DAB\u1DBB\u1DC6\u1DCDr\xF2\u0381ar;\u6965\u0200glrs\u1C48\u1C4D\u1C52\u1C54ger;\u6020eth;\u6138\xF2\u1133h\u0100;v\u1C5A\u1C5B\u6010\xBB\u090A\u016B\u1C61\u1C67arow;\u690Fa\xE3\u0315\u0100ay\u1C6E\u1C73ron;\u410F;\u4434\u0180;ao\u0332\u1C7C\u1C84\u0100gr\u02BF\u1C81r;\u61CAtseq;\u6A77\u0180glm\u1C91\u1C94\u1C98\u803B\xB0\u40B0ta;\u43B4ptyv;\u69B1\u0100ir\u1CA3\u1CA8sht;\u697F;\uC000\u{1D521}ar\u0100lr\u1CB3\u1CB5\xBB\u08DC\xBB\u101E\u0280aegsv\u1CC2\u0378\u1CD6\u1CDC\u1CE0m\u0180;os\u0326\u1CCA\u1CD4nd\u0100;s\u0326\u1CD1uit;\u6666amma;\u43DDin;\u62F2\u0180;io\u1CE7\u1CE8\u1CF8\u40F7de\u8100\xF7;o\u1CE7\u1CF0ntimes;\u62C7n\xF8\u1CF7cy;\u4452c\u026F\u1D06\0\0\u1D0Arn;\u631Eop;\u630D\u0280lptuw\u1D18\u1D1D\u1D22\u1D49\u1D55lar;\u4024f;\uC000\u{1D555}\u0280;emps\u030B\u1D2D\u1D37\u1D3D\u1D42q\u0100;d\u0352\u1D33ot;\u6251inus;\u6238lus;\u6214quare;\u62A1blebarwedg\xE5\xFAn\u0180adh\u112E\u1D5D\u1D67ownarrow\xF3\u1C83arpoon\u0100lr\u1D72\u1D76ef\xF4\u1CB4igh\xF4\u1CB6\u0162\u1D7F\u1D85karo\xF7\u0F42\u026F\u1D8A\0\0\u1D8Ern;\u631Fop;\u630C\u0180cot\u1D98\u1DA3\u1DA6\u0100ry\u1D9D\u1DA1;\uC000\u{1D4B9};\u4455l;\u69F6rok;\u4111\u0100dr\u1DB0\u1DB4ot;\u62F1i\u0100;f\u1DBA\u1816\u65BF\u0100ah\u1DC0\u1DC3r\xF2\u0429a\xF2\u0FA6angle;\u69A6\u0100ci\u1DD2\u1DD5y;\u445Fgrarr;\u67FF\u0900Dacdefglmnopqrstux\u1E01\u1E09\u1E19\u1E38\u0578\u1E3C\u1E49\u1E61\u1E7E\u1EA5\u1EAF\u1EBD\u1EE1\u1F2A\u1F37\u1F44\u1F4E\u1F5A\u0100Do\u1E06\u1D34o\xF4\u1C89\u0100cs\u1E0E\u1E14ute\u803B\xE9\u40E9ter;\u6A6E\u0200aioy\u1E22\u1E27\u1E31\u1E36ron;\u411Br\u0100;c\u1E2D\u1E2E\u6256\u803B\xEA\u40EAlon;\u6255;\u444Dot;\u4117\u0100Dr\u1E41\u1E45ot;\u6252;\uC000\u{1D522}\u0180;rs\u1E50\u1E51\u1E57\u6A9Aave\u803B\xE8\u40E8\u0100;d\u1E5C\u1E5D\u6A96ot;\u6A98\u0200;ils\u1E6A\u1E6B\u1E72\u1E74\u6A99nters;\u63E7;\u6113\u0100;d\u1E79\u1E7A\u6A95ot;\u6A97\u0180aps\u1E85\u1E89\u1E97cr;\u4113ty\u0180;sv\u1E92\u1E93\u1E95\u6205et\xBB\u1E93p\u01001;\u1E9D\u1EA4\u0133\u1EA1\u1EA3;\u6004;\u6005\u6003\u0100gs\u1EAA\u1EAC;\u414Bp;\u6002\u0100gp\u1EB4\u1EB8on;\u4119f;\uC000\u{1D556}\u0180als\u1EC4\u1ECE\u1ED2r\u0100;s\u1ECA\u1ECB\u62D5l;\u69E3us;\u6A71i\u0180;lv\u1EDA\u1EDB\u1EDF\u43B5on\xBB\u1EDB;\u43F5\u0200csuv\u1EEA\u1EF3\u1F0B\u1F23\u0100io\u1EEF\u1E31rc\xBB\u1E2E\u0269\u1EF9\0\0\u1EFB\xED\u0548ant\u0100gl\u1F02\u1F06tr\xBB\u1E5Dess\xBB\u1E7A\u0180aei\u1F12\u1F16\u1F1Als;\u403Dst;\u625Fv\u0100;D\u0235\u1F20D;\u6A78parsl;\u69E5\u0100Da\u1F2F\u1F33ot;\u6253rr;\u6971\u0180cdi\u1F3E\u1F41\u1EF8r;\u612Fo\xF4\u0352\u0100ah\u1F49\u1F4B;\u43B7\u803B\xF0\u40F0\u0100mr\u1F53\u1F57l\u803B\xEB\u40EBo;\u60AC\u0180cip\u1F61\u1F64\u1F67l;\u4021s\xF4\u056E\u0100eo\u1F6C\u1F74ctatio\xEE\u0559nential\xE5\u0579\u09E1\u1F92\0\u1F9E\0\u1FA1\u1FA7\0\0\u1FC6\u1FCC\0\u1FD3\0\u1FE6\u1FEA\u2000\0\u2008\u205Allingdotse\xF1\u1E44y;\u4444male;\u6640\u0180ilr\u1FAD\u1FB3\u1FC1lig;\u8000\uFB03\u0269\u1FB9\0\0\u1FBDg;\u8000\uFB00ig;\u8000\uFB04;\uC000\u{1D523}lig;\u8000\uFB01lig;\uC000fj\u0180alt\u1FD9\u1FDC\u1FE1t;\u666Dig;\u8000\uFB02ns;\u65B1of;\u4192\u01F0\u1FEE\0\u1FF3f;\uC000\u{1D557}\u0100ak\u05BF\u1FF7\u0100;v\u1FFC\u1FFD\u62D4;\u6AD9artint;\u6A0D\u0100ao\u200C\u2055\u0100cs\u2011\u2052\u03B1\u201A\u2030\u2038\u2045\u2048\0\u2050\u03B2\u2022\u2025\u2027\u202A\u202C\0\u202E\u803B\xBD\u40BD;\u6153\u803B\xBC\u40BC;\u6155;\u6159;\u615B\u01B3\u2034\0\u2036;\u6154;\u6156\u02B4\u203E\u2041\0\0\u2043\u803B\xBE\u40BE;\u6157;\u615C5;\u6158\u01B6\u204C\0\u204E;\u615A;\u615D8;\u615El;\u6044wn;\u6322cr;\uC000\u{1D4BB}\u0880Eabcdefgijlnorstv\u2082\u2089\u209F\u20A5\u20B0\u20B4\u20F0\u20F5\u20FA\u20FF\u2103\u2112\u2138\u0317\u213E\u2152\u219E\u0100;l\u064D\u2087;\u6A8C\u0180cmp\u2090\u2095\u209Dute;\u41F5ma\u0100;d\u209C\u1CDA\u43B3;\u6A86reve;\u411F\u0100iy\u20AA\u20AErc;\u411D;\u4433ot;\u4121\u0200;lqs\u063E\u0642\u20BD\u20C9\u0180;qs\u063E\u064C\u20C4lan\xF4\u0665\u0200;cdl\u0665\u20D2\u20D5\u20E5c;\u6AA9ot\u0100;o\u20DC\u20DD\u6A80\u0100;l\u20E2\u20E3\u6A82;\u6A84\u0100;e\u20EA\u20ED\uC000\u22DB\uFE00s;\u6A94r;\uC000\u{1D524}\u0100;g\u0673\u061Bmel;\u6137cy;\u4453\u0200;Eaj\u065A\u210C\u210E\u2110;\u6A92;\u6AA5;\u6AA4\u0200Eaes\u211B\u211D\u2129\u2134;\u6269p\u0100;p\u2123\u2124\u6A8Arox\xBB\u2124\u0100;q\u212E\u212F\u6A88\u0100;q\u212E\u211Bim;\u62E7pf;\uC000\u{1D558}\u0100ci\u2143\u2146r;\u610Am\u0180;el\u066B\u214E\u2150;\u6A8E;\u6A90\u8300>;cdlqr\u05EE\u2160\u216A\u216E\u2173\u2179\u0100ci\u2165\u2167;\u6AA7r;\u6A7Aot;\u62D7Par;\u6995uest;\u6A7C\u0280adels\u2184\u216A\u2190\u0656\u219B\u01F0\u2189\0\u218Epro\xF8\u209Er;\u6978q\u0100lq\u063F\u2196les\xF3\u2088i\xED\u066B\u0100en\u21A3\u21ADrtneqq;\uC000\u2269\uFE00\xC5\u21AA\u0500Aabcefkosy\u21C4\u21C7\u21F1\u21F5\u21FA\u2218\u221D\u222F\u2268\u227Dr\xF2\u03A0\u0200ilmr\u21D0\u21D4\u21D7\u21DBrs\xF0\u1484f\xBB\u2024il\xF4\u06A9\u0100dr\u21E0\u21E4cy;\u444A\u0180;cw\u08F4\u21EB\u21EFir;\u6948;\u61ADar;\u610Firc;\u4125\u0180alr\u2201\u220E\u2213rts\u0100;u\u2209\u220A\u6665it\xBB\u220Alip;\u6026con;\u62B9r;\uC000\u{1D525}s\u0100ew\u2223\u2229arow;\u6925arow;\u6926\u0280amopr\u223A\u223E\u2243\u225E\u2263rr;\u61FFtht;\u623Bk\u0100lr\u2249\u2253eftarrow;\u61A9ightarrow;\u61AAf;\uC000\u{1D559}bar;\u6015\u0180clt\u226F\u2274\u2278r;\uC000\u{1D4BD}as\xE8\u21F4rok;\u4127\u0100bp\u2282\u2287ull;\u6043hen\xBB\u1C5B\u0AE1\u22A3\0\u22AA\0\u22B8\u22C5\u22CE\0\u22D5\u22F3\0\0\u22F8\u2322\u2367\u2362\u237F\0\u2386\u23AA\u23B4cute\u803B\xED\u40ED\u0180;iy\u0771\u22B0\u22B5rc\u803B\xEE\u40EE;\u4438\u0100cx\u22BC\u22BFy;\u4435cl\u803B\xA1\u40A1\u0100fr\u039F\u22C9;\uC000\u{1D526}rave\u803B\xEC\u40EC\u0200;ino\u073E\u22DD\u22E9\u22EE\u0100in\u22E2\u22E6nt;\u6A0Ct;\u622Dfin;\u69DCta;\u6129lig;\u4133\u0180aop\u22FE\u231A\u231D\u0180cgt\u2305\u2308\u2317r;\u412B\u0180elp\u071F\u230F\u2313in\xE5\u078Ear\xF4\u0720h;\u4131f;\u62B7ed;\u41B5\u0280;cfot\u04F4\u232C\u2331\u233D\u2341are;\u6105in\u0100;t\u2338\u2339\u621Eie;\u69DDdo\xF4\u2319\u0280;celp\u0757\u234C\u2350\u235B\u2361al;\u62BA\u0100gr\u2355\u2359er\xF3\u1563\xE3\u234Darhk;\u6A17rod;\u6A3C\u0200cgpt\u236F\u2372\u2376\u237By;\u4451on;\u412Ff;\uC000\u{1D55A}a;\u43B9uest\u803B\xBF\u40BF\u0100ci\u238A\u238Fr;\uC000\u{1D4BE}n\u0280;Edsv\u04F4\u239B\u239D\u23A1\u04F3;\u62F9ot;\u62F5\u0100;v\u23A6\u23A7\u62F4;\u62F3\u0100;i\u0777\u23AElde;\u4129\u01EB\u23B8\0\u23BCcy;\u4456l\u803B\xEF\u40EF\u0300cfmosu\u23CC\u23D7\u23DC\u23E1\u23E7\u23F5\u0100iy\u23D1\u23D5rc;\u4135;\u4439r;\uC000\u{1D527}ath;\u4237pf;\uC000\u{1D55B}\u01E3\u23EC\0\u23F1r;\uC000\u{1D4BF}rcy;\u4458kcy;\u4454\u0400acfghjos\u240B\u2416\u2422\u2427\u242D\u2431\u2435\u243Bppa\u0100;v\u2413\u2414\u43BA;\u43F0\u0100ey\u241B\u2420dil;\u4137;\u443Ar;\uC000\u{1D528}reen;\u4138cy;\u4445cy;\u445Cpf;\uC000\u{1D55C}cr;\uC000\u{1D4C0}\u0B80ABEHabcdefghjlmnoprstuv\u2470\u2481\u2486\u248D\u2491\u250E\u253D\u255A\u2580\u264E\u265E\u2665\u2679\u267D\u269A\u26B2\u26D8\u275D\u2768\u278B\u27C0\u2801\u2812\u0180art\u2477\u247A\u247Cr\xF2\u09C6\xF2\u0395ail;\u691Barr;\u690E\u0100;g\u0994\u248B;\u6A8Bar;\u6962\u0963\u24A5\0\u24AA\0\u24B1\0\0\0\0\0\u24B5\u24BA\0\u24C6\u24C8\u24CD\0\u24F9ute;\u413Amptyv;\u69B4ra\xEE\u084Cbda;\u43BBg\u0180;dl\u088E\u24C1\u24C3;\u6991\xE5\u088E;\u6A85uo\u803B\xAB\u40ABr\u0400;bfhlpst\u0899\u24DE\u24E6\u24E9\u24EB\u24EE\u24F1\u24F5\u0100;f\u089D\u24E3s;\u691Fs;\u691D\xEB\u2252p;\u61ABl;\u6939im;\u6973l;\u61A2\u0180;ae\u24FF\u2500\u2504\u6AABil;\u6919\u0100;s\u2509\u250A\u6AAD;\uC000\u2AAD\uFE00\u0180abr\u2515\u2519\u251Drr;\u690Crk;\u6772\u0100ak\u2522\u252Cc\u0100ek\u2528\u252A;\u407B;\u405B\u0100es\u2531\u2533;\u698Bl\u0100du\u2539\u253B;\u698F;\u698D\u0200aeuy\u2546\u254B\u2556\u2558ron;\u413E\u0100di\u2550\u2554il;\u413C\xEC\u08B0\xE2\u2529;\u443B\u0200cqrs\u2563\u2566\u256D\u257Da;\u6936uo\u0100;r\u0E19\u1746\u0100du\u2572\u2577har;\u6967shar;\u694Bh;\u61B2\u0280;fgqs\u258B\u258C\u0989\u25F3\u25FF\u6264t\u0280ahlrt\u2598\u25A4\u25B7\u25C2\u25E8rrow\u0100;t\u0899\u25A1a\xE9\u24F6arpoon\u0100du\u25AF\u25B4own\xBB\u045Ap\xBB\u0966eftarrows;\u61C7ight\u0180ahs\u25CD\u25D6\u25DErrow\u0100;s\u08F4\u08A7arpoon\xF3\u0F98quigarro\xF7\u21F0hreetimes;\u62CB\u0180;qs\u258B\u0993\u25FAlan\xF4\u09AC\u0280;cdgs\u09AC\u260A\u260D\u261D\u2628c;\u6AA8ot\u0100;o\u2614\u2615\u6A7F\u0100;r\u261A\u261B\u6A81;\u6A83\u0100;e\u2622\u2625\uC000\u22DA\uFE00s;\u6A93\u0280adegs\u2633\u2639\u263D\u2649\u264Bppro\xF8\u24C6ot;\u62D6q\u0100gq\u2643\u2645\xF4\u0989gt\xF2\u248C\xF4\u099Bi\xED\u09B2\u0180ilr\u2655\u08E1\u265Asht;\u697C;\uC000\u{1D529}\u0100;E\u099C\u2663;\u6A91\u0161\u2669\u2676r\u0100du\u25B2\u266E\u0100;l\u0965\u2673;\u696Alk;\u6584cy;\u4459\u0280;acht\u0A48\u2688\u268B\u2691\u2696r\xF2\u25C1orne\xF2\u1D08ard;\u696Bri;\u65FA\u0100io\u269F\u26A4dot;\u4140ust\u0100;a\u26AC\u26AD\u63B0che\xBB\u26AD\u0200Eaes\u26BB\u26BD\u26C9\u26D4;\u6268p\u0100;p\u26C3\u26C4\u6A89rox\xBB\u26C4\u0100;q\u26CE\u26CF\u6A87\u0100;q\u26CE\u26BBim;\u62E6\u0400abnoptwz\u26E9\u26F4\u26F7\u271A\u272F\u2741\u2747\u2750\u0100nr\u26EE\u26F1g;\u67ECr;\u61FDr\xEB\u08C1g\u0180lmr\u26FF\u270D\u2714eft\u0100ar\u09E6\u2707ight\xE1\u09F2apsto;\u67FCight\xE1\u09FDparrow\u0100lr\u2725\u2729ef\xF4\u24EDight;\u61AC\u0180afl\u2736\u2739\u273Dr;\u6985;\uC000\u{1D55D}us;\u6A2Dimes;\u6A34\u0161\u274B\u274Fst;\u6217\xE1\u134E\u0180;ef\u2757\u2758\u1800\u65CAnge\xBB\u2758ar\u0100;l\u2764\u2765\u4028t;\u6993\u0280achmt\u2773\u2776\u277C\u2785\u2787r\xF2\u08A8orne\xF2\u1D8Car\u0100;d\u0F98\u2783;\u696D;\u600Eri;\u62BF\u0300achiqt\u2798\u279D\u0A40\u27A2\u27AE\u27BBquo;\u6039r;\uC000\u{1D4C1}m\u0180;eg\u09B2\u27AA\u27AC;\u6A8D;\u6A8F\u0100bu\u252A\u27B3o\u0100;r\u0E1F\u27B9;\u601Arok;\u4142\u8400<;cdhilqr\u082B\u27D2\u2639\u27DC\u27E0\u27E5\u27EA\u27F0\u0100ci\u27D7\u27D9;\u6AA6r;\u6A79re\xE5\u25F2mes;\u62C9arr;\u6976uest;\u6A7B\u0100Pi\u27F5\u27F9ar;\u6996\u0180;ef\u2800\u092D\u181B\u65C3r\u0100du\u2807\u280Dshar;\u694Ahar;\u6966\u0100en\u2817\u2821rtneqq;\uC000\u2268\uFE00\xC5\u281E\u0700Dacdefhilnopsu\u2840\u2845\u2882\u288E\u2893\u28A0\u28A5\u28A8\u28DA\u28E2\u28E4\u0A83\u28F3\u2902Dot;\u623A\u0200clpr\u284E\u2852\u2863\u287Dr\u803B\xAF\u40AF\u0100et\u2857\u2859;\u6642\u0100;e\u285E\u285F\u6720se\xBB\u285F\u0100;s\u103B\u2868to\u0200;dlu\u103B\u2873\u2877\u287Bow\xEE\u048Cef\xF4\u090F\xF0\u13D1ker;\u65AE\u0100oy\u2887\u288Cmma;\u6A29;\u443Cash;\u6014asuredangle\xBB\u1626r;\uC000\u{1D52A}o;\u6127\u0180cdn\u28AF\u28B4\u28C9ro\u803B\xB5\u40B5\u0200;acd\u1464\u28BD\u28C0\u28C4s\xF4\u16A7ir;\u6AF0ot\u80BB\xB7\u01B5us\u0180;bd\u28D2\u1903\u28D3\u6212\u0100;u\u1D3C\u28D8;\u6A2A\u0163\u28DE\u28E1p;\u6ADB\xF2\u2212\xF0\u0A81\u0100dp\u28E9\u28EEels;\u62A7f;\uC000\u{1D55E}\u0100ct\u28F8\u28FDr;\uC000\u{1D4C2}pos\xBB\u159D\u0180;lm\u2909\u290A\u290D\u43BCtimap;\u62B8\u0C00GLRVabcdefghijlmoprstuvw\u2942\u2953\u297E\u2989\u2998\u29DA\u29E9\u2A15\u2A1A\u2A58\u2A5D\u2A83\u2A95\u2AA4\u2AA8\u2B04\u2B07\u2B44\u2B7F\u2BAE\u2C34\u2C67\u2C7C\u2CE9\u0100gt\u2947\u294B;\uC000\u22D9\u0338\u0100;v\u2950\u0BCF\uC000\u226B\u20D2\u0180elt\u295A\u2972\u2976ft\u0100ar\u2961\u2967rrow;\u61CDightarrow;\u61CE;\uC000\u22D8\u0338\u0100;v\u297B\u0C47\uC000\u226A\u20D2ightarrow;\u61CF\u0100Dd\u298E\u2993ash;\u62AFash;\u62AE\u0280bcnpt\u29A3\u29A7\u29AC\u29B1\u29CCla\xBB\u02DEute;\u4144g;\uC000\u2220\u20D2\u0280;Eiop\u0D84\u29BC\u29C0\u29C5\u29C8;\uC000\u2A70\u0338d;\uC000\u224B\u0338s;\u4149ro\xF8\u0D84ur\u0100;a\u29D3\u29D4\u666El\u0100;s\u29D3\u0B38\u01F3\u29DF\0\u29E3p\u80BB\xA0\u0B37mp\u0100;e\u0BF9\u0C00\u0280aeouy\u29F4\u29FE\u2A03\u2A10\u2A13\u01F0\u29F9\0\u29FB;\u6A43on;\u4148dil;\u4146ng\u0100;d\u0D7E\u2A0Aot;\uC000\u2A6D\u0338p;\u6A42;\u443Dash;\u6013\u0380;Aadqsx\u0B92\u2A29\u2A2D\u2A3B\u2A41\u2A45\u2A50rr;\u61D7r\u0100hr\u2A33\u2A36k;\u6924\u0100;o\u13F2\u13F0ot;\uC000\u2250\u0338ui\xF6\u0B63\u0100ei\u2A4A\u2A4Ear;\u6928\xED\u0B98ist\u0100;s\u0BA0\u0B9Fr;\uC000\u{1D52B}\u0200Eest\u0BC5\u2A66\u2A79\u2A7C\u0180;qs\u0BBC\u2A6D\u0BE1\u0180;qs\u0BBC\u0BC5\u2A74lan\xF4\u0BE2i\xED\u0BEA\u0100;r\u0BB6\u2A81\xBB\u0BB7\u0180Aap\u2A8A\u2A8D\u2A91r\xF2\u2971rr;\u61AEar;\u6AF2\u0180;sv\u0F8D\u2A9C\u0F8C\u0100;d\u2AA1\u2AA2\u62FC;\u62FAcy;\u445A\u0380AEadest\u2AB7\u2ABA\u2ABE\u2AC2\u2AC5\u2AF6\u2AF9r\xF2\u2966;\uC000\u2266\u0338rr;\u619Ar;\u6025\u0200;fqs\u0C3B\u2ACE\u2AE3\u2AEFt\u0100ar\u2AD4\u2AD9rro\xF7\u2AC1ightarro\xF7\u2A90\u0180;qs\u0C3B\u2ABA\u2AEAlan\xF4\u0C55\u0100;s\u0C55\u2AF4\xBB\u0C36i\xED\u0C5D\u0100;r\u0C35\u2AFEi\u0100;e\u0C1A\u0C25i\xE4\u0D90\u0100pt\u2B0C\u2B11f;\uC000\u{1D55F}\u8180\xAC;in\u2B19\u2B1A\u2B36\u40ACn\u0200;Edv\u0B89\u2B24\u2B28\u2B2E;\uC000\u22F9\u0338ot;\uC000\u22F5\u0338\u01E1\u0B89\u2B33\u2B35;\u62F7;\u62F6i\u0100;v\u0CB8\u2B3C\u01E1\u0CB8\u2B41\u2B43;\u62FE;\u62FD\u0180aor\u2B4B\u2B63\u2B69r\u0200;ast\u0B7B\u2B55\u2B5A\u2B5Flle\xEC\u0B7Bl;\uC000\u2AFD\u20E5;\uC000\u2202\u0338lint;\u6A14\u0180;ce\u0C92\u2B70\u2B73u\xE5\u0CA5\u0100;c\u0C98\u2B78\u0100;e\u0C92\u2B7D\xF1\u0C98\u0200Aait\u2B88\u2B8B\u2B9D\u2BA7r\xF2\u2988rr\u0180;cw\u2B94\u2B95\u2B99\u619B;\uC000\u2933\u0338;\uC000\u219D\u0338ghtarrow\xBB\u2B95ri\u0100;e\u0CCB\u0CD6\u0380chimpqu\u2BBD\u2BCD\u2BD9\u2B04\u0B78\u2BE4\u2BEF\u0200;cer\u0D32\u2BC6\u0D37\u2BC9u\xE5\u0D45;\uC000\u{1D4C3}ort\u026D\u2B05\0\0\u2BD6ar\xE1\u2B56m\u0100;e\u0D6E\u2BDF\u0100;q\u0D74\u0D73su\u0100bp\u2BEB\u2BED\xE5\u0CF8\xE5\u0D0B\u0180bcp\u2BF6\u2C11\u2C19\u0200;Ees\u2BFF\u2C00\u0D22\u2C04\u6284;\uC000\u2AC5\u0338et\u0100;e\u0D1B\u2C0Bq\u0100;q\u0D23\u2C00c\u0100;e\u0D32\u2C17\xF1\u0D38\u0200;Ees\u2C22\u2C23\u0D5F\u2C27\u6285;\uC000\u2AC6\u0338et\u0100;e\u0D58\u2C2Eq\u0100;q\u0D60\u2C23\u0200gilr\u2C3D\u2C3F\u2C45\u2C47\xEC\u0BD7lde\u803B\xF1\u40F1\xE7\u0C43iangle\u0100lr\u2C52\u2C5Ceft\u0100;e\u0C1A\u2C5A\xF1\u0C26ight\u0100;e\u0CCB\u2C65\xF1\u0CD7\u0100;m\u2C6C\u2C6D\u43BD\u0180;es\u2C74\u2C75\u2C79\u4023ro;\u6116p;\u6007\u0480DHadgilrs\u2C8F\u2C94\u2C99\u2C9E\u2CA3\u2CB0\u2CB6\u2CD3\u2CE3ash;\u62ADarr;\u6904p;\uC000\u224D\u20D2ash;\u62AC\u0100et\u2CA8\u2CAC;\uC000\u2265\u20D2;\uC000>\u20D2nfin;\u69DE\u0180Aet\u2CBD\u2CC1\u2CC5rr;\u6902;\uC000\u2264\u20D2\u0100;r\u2CCA\u2CCD\uC000<\u20D2ie;\uC000\u22B4\u20D2\u0100At\u2CD8\u2CDCrr;\u6903rie;\uC000\u22B5\u20D2im;\uC000\u223C\u20D2\u0180Aan\u2CF0\u2CF4\u2D02rr;\u61D6r\u0100hr\u2CFA\u2CFDk;\u6923\u0100;o\u13E7\u13E5ear;\u6927\u1253\u1A95\0\0\0\0\0\0\0\0\0\0\0\0\0\u2D2D\0\u2D38\u2D48\u2D60\u2D65\u2D72\u2D84\u1B07\0\0\u2D8D\u2DAB\0\u2DC8\u2DCE\0\u2DDC\u2E19\u2E2B\u2E3E\u2E43\u0100cs\u2D31\u1A97ute\u803B\xF3\u40F3\u0100iy\u2D3C\u2D45r\u0100;c\u1A9E\u2D42\u803B\xF4\u40F4;\u443E\u0280abios\u1AA0\u2D52\u2D57\u01C8\u2D5Alac;\u4151v;\u6A38old;\u69BClig;\u4153\u0100cr\u2D69\u2D6Dir;\u69BF;\uC000\u{1D52C}\u036F\u2D79\0\0\u2D7C\0\u2D82n;\u42DBave\u803B\xF2\u40F2;\u69C1\u0100bm\u2D88\u0DF4ar;\u69B5\u0200acit\u2D95\u2D98\u2DA5\u2DA8r\xF2\u1A80\u0100ir\u2D9D\u2DA0r;\u69BEoss;\u69BBn\xE5\u0E52;\u69C0\u0180aei\u2DB1\u2DB5\u2DB9cr;\u414Dga;\u43C9\u0180cdn\u2DC0\u2DC5\u01CDron;\u43BF;\u69B6pf;\uC000\u{1D560}\u0180ael\u2DD4\u2DD7\u01D2r;\u69B7rp;\u69B9\u0380;adiosv\u2DEA\u2DEB\u2DEE\u2E08\u2E0D\u2E10\u2E16\u6228r\xF2\u1A86\u0200;efm\u2DF7\u2DF8\u2E02\u2E05\u6A5Dr\u0100;o\u2DFE\u2DFF\u6134f\xBB\u2DFF\u803B\xAA\u40AA\u803B\xBA\u40BAgof;\u62B6r;\u6A56lope;\u6A57;\u6A5B\u0180clo\u2E1F\u2E21\u2E27\xF2\u2E01ash\u803B\xF8\u40F8l;\u6298i\u016C\u2E2F\u2E34de\u803B\xF5\u40F5es\u0100;a\u01DB\u2E3As;\u6A36ml\u803B\xF6\u40F6bar;\u633D\u0AE1\u2E5E\0\u2E7D\0\u2E80\u2E9D\0\u2EA2\u2EB9\0\0\u2ECB\u0E9C\0\u2F13\0\0\u2F2B\u2FBC\0\u2FC8r\u0200;ast\u0403\u2E67\u2E72\u0E85\u8100\xB6;l\u2E6D\u2E6E\u40B6le\xEC\u0403\u0269\u2E78\0\0\u2E7Bm;\u6AF3;\u6AFDy;\u443Fr\u0280cimpt\u2E8B\u2E8F\u2E93\u1865\u2E97nt;\u4025od;\u402Eil;\u6030enk;\u6031r;\uC000\u{1D52D}\u0180imo\u2EA8\u2EB0\u2EB4\u0100;v\u2EAD\u2EAE\u43C6;\u43D5ma\xF4\u0A76ne;\u660E\u0180;tv\u2EBF\u2EC0\u2EC8\u43C0chfork\xBB\u1FFD;\u43D6\u0100au\u2ECF\u2EDFn\u0100ck\u2ED5\u2EDDk\u0100;h\u21F4\u2EDB;\u610E\xF6\u21F4s\u0480;abcdemst\u2EF3\u2EF4\u1908\u2EF9\u2EFD\u2F04\u2F06\u2F0A\u2F0E\u402Bcir;\u6A23ir;\u6A22\u0100ou\u1D40\u2F02;\u6A25;\u6A72n\u80BB\xB1\u0E9Dim;\u6A26wo;\u6A27\u0180ipu\u2F19\u2F20\u2F25ntint;\u6A15f;\uC000\u{1D561}nd\u803B\xA3\u40A3\u0500;Eaceinosu\u0EC8\u2F3F\u2F41\u2F44\u2F47\u2F81\u2F89\u2F92\u2F7E\u2FB6;\u6AB3p;\u6AB7u\xE5\u0ED9\u0100;c\u0ECE\u2F4C\u0300;acens\u0EC8\u2F59\u2F5F\u2F66\u2F68\u2F7Eppro\xF8\u2F43urlye\xF1\u0ED9\xF1\u0ECE\u0180aes\u2F6F\u2F76\u2F7Approx;\u6AB9qq;\u6AB5im;\u62E8i\xED\u0EDFme\u0100;s\u2F88\u0EAE\u6032\u0180Eas\u2F78\u2F90\u2F7A\xF0\u2F75\u0180dfp\u0EEC\u2F99\u2FAF\u0180als\u2FA0\u2FA5\u2FAAlar;\u632Eine;\u6312urf;\u6313\u0100;t\u0EFB\u2FB4\xEF\u0EFBrel;\u62B0\u0100ci\u2FC0\u2FC5r;\uC000\u{1D4C5};\u43C8ncsp;\u6008\u0300fiopsu\u2FDA\u22E2\u2FDF\u2FE5\u2FEB\u2FF1r;\uC000\u{1D52E}pf;\uC000\u{1D562}rime;\u6057cr;\uC000\u{1D4C6}\u0180aeo\u2FF8\u3009\u3013t\u0100ei\u2FFE\u3005rnion\xF3\u06B0nt;\u6A16st\u0100;e\u3010\u3011\u403F\xF1\u1F19\xF4\u0F14\u0A80ABHabcdefhilmnoprstux\u3040\u3051\u3055\u3059\u30E0\u310E\u312B\u3147\u3162\u3172\u318E\u3206\u3215\u3224\u3229\u3258\u326E\u3272\u3290\u32B0\u32B7\u0180art\u3047\u304A\u304Cr\xF2\u10B3\xF2\u03DDail;\u691Car\xF2\u1C65ar;\u6964\u0380cdenqrt\u3068\u3075\u3078\u307F\u308F\u3094\u30CC\u0100eu\u306D\u3071;\uC000\u223D\u0331te;\u4155i\xE3\u116Emptyv;\u69B3g\u0200;del\u0FD1\u3089\u308B\u308D;\u6992;\u69A5\xE5\u0FD1uo\u803B\xBB\u40BBr\u0580;abcfhlpstw\u0FDC\u30AC\u30AF\u30B7\u30B9\u30BC\u30BE\u30C0\u30C3\u30C7\u30CAp;\u6975\u0100;f\u0FE0\u30B4s;\u6920;\u6933s;\u691E\xEB\u225D\xF0\u272El;\u6945im;\u6974l;\u61A3;\u619D\u0100ai\u30D1\u30D5il;\u691Ao\u0100;n\u30DB\u30DC\u6236al\xF3\u0F1E\u0180abr\u30E7\u30EA\u30EEr\xF2\u17E5rk;\u6773\u0100ak\u30F3\u30FDc\u0100ek\u30F9\u30FB;\u407D;\u405D\u0100es\u3102\u3104;\u698Cl\u0100du\u310A\u310C;\u698E;\u6990\u0200aeuy\u3117\u311C\u3127\u3129ron;\u4159\u0100di\u3121\u3125il;\u4157\xEC\u0FF2\xE2\u30FA;\u4440\u0200clqs\u3134\u3137\u313D\u3144a;\u6937dhar;\u6969uo\u0100;r\u020E\u020Dh;\u61B3\u0180acg\u314E\u315F\u0F44l\u0200;ips\u0F78\u3158\u315B\u109Cn\xE5\u10BBar\xF4\u0FA9t;\u65AD\u0180ilr\u3169\u1023\u316Esht;\u697D;\uC000\u{1D52F}\u0100ao\u3177\u3186r\u0100du\u317D\u317F\xBB\u047B\u0100;l\u1091\u3184;\u696C\u0100;v\u318B\u318C\u43C1;\u43F1\u0180gns\u3195\u31F9\u31FCht\u0300ahlrst\u31A4\u31B0\u31C2\u31D8\u31E4\u31EErrow\u0100;t\u0FDC\u31ADa\xE9\u30C8arpoon\u0100du\u31BB\u31BFow\xEE\u317Ep\xBB\u1092eft\u0100ah\u31CA\u31D0rrow\xF3\u0FEAarpoon\xF3\u0551ightarrows;\u61C9quigarro\xF7\u30CBhreetimes;\u62CCg;\u42DAingdotse\xF1\u1F32\u0180ahm\u320D\u3210\u3213r\xF2\u0FEAa\xF2\u0551;\u600Foust\u0100;a\u321E\u321F\u63B1che\xBB\u321Fmid;\u6AEE\u0200abpt\u3232\u323D\u3240\u3252\u0100nr\u3237\u323Ag;\u67EDr;\u61FEr\xEB\u1003\u0180afl\u3247\u324A\u324Er;\u6986;\uC000\u{1D563}us;\u6A2Eimes;\u6A35\u0100ap\u325D\u3267r\u0100;g\u3263\u3264\u4029t;\u6994olint;\u6A12ar\xF2\u31E3\u0200achq\u327B\u3280\u10BC\u3285quo;\u603Ar;\uC000\u{1D4C7}\u0100bu\u30FB\u328Ao\u0100;r\u0214\u0213\u0180hir\u3297\u329B\u32A0re\xE5\u31F8mes;\u62CAi\u0200;efl\u32AA\u1059\u1821\u32AB\u65B9tri;\u69CEluhar;\u6968;\u611E\u0D61\u32D5\u32DB\u32DF\u332C\u3338\u3371\0\u337A\u33A4\0\0\u33EC\u33F0\0\u3428\u3448\u345A\u34AD\u34B1\u34CA\u34F1\0\u3616\0\0\u3633cute;\u415Bqu\xEF\u27BA\u0500;Eaceinpsy\u11ED\u32F3\u32F5\u32FF\u3302\u330B\u330F\u331F\u3326\u3329;\u6AB4\u01F0\u32FA\0\u32FC;\u6AB8on;\u4161u\xE5\u11FE\u0100;d\u11F3\u3307il;\u415Frc;\u415D\u0180Eas\u3316\u3318\u331B;\u6AB6p;\u6ABAim;\u62E9olint;\u6A13i\xED\u1204;\u4441ot\u0180;be\u3334\u1D47\u3335\u62C5;\u6A66\u0380Aacmstx\u3346\u334A\u3357\u335B\u335E\u3363\u336Drr;\u61D8r\u0100hr\u3350\u3352\xEB\u2228\u0100;o\u0A36\u0A34t\u803B\xA7\u40A7i;\u403Bwar;\u6929m\u0100in\u3369\xF0nu\xF3\xF1t;\u6736r\u0100;o\u3376\u2055\uC000\u{1D530}\u0200acoy\u3382\u3386\u3391\u33A0rp;\u666F\u0100hy\u338B\u338Fcy;\u4449;\u4448rt\u026D\u3399\0\0\u339Ci\xE4\u1464ara\xEC\u2E6F\u803B\xAD\u40AD\u0100gm\u33A8\u33B4ma\u0180;fv\u33B1\u33B2\u33B2\u43C3;\u43C2\u0400;deglnpr\u12AB\u33C5\u33C9\u33CE\u33D6\u33DE\u33E1\u33E6ot;\u6A6A\u0100;q\u12B1\u12B0\u0100;E\u33D3\u33D4\u6A9E;\u6AA0\u0100;E\u33DB\u33DC\u6A9D;\u6A9Fe;\u6246lus;\u6A24arr;\u6972ar\xF2\u113D\u0200aeit\u33F8\u3408\u340F\u3417\u0100ls\u33FD\u3404lsetm\xE9\u336Ahp;\u6A33parsl;\u69E4\u0100dl\u1463\u3414e;\u6323\u0100;e\u341C\u341D\u6AAA\u0100;s\u3422\u3423\u6AAC;\uC000\u2AAC\uFE00\u0180flp\u342E\u3433\u3442tcy;\u444C\u0100;b\u3438\u3439\u402F\u0100;a\u343E\u343F\u69C4r;\u633Ff;\uC000\u{1D564}a\u0100dr\u344D\u0402es\u0100;u\u3454\u3455\u6660it\xBB\u3455\u0180csu\u3460\u3479\u349F\u0100au\u3465\u346Fp\u0100;s\u1188\u346B;\uC000\u2293\uFE00p\u0100;s\u11B4\u3475;\uC000\u2294\uFE00u\u0100bp\u347F\u348F\u0180;es\u1197\u119C\u3486et\u0100;e\u1197\u348D\xF1\u119D\u0180;es\u11A8\u11AD\u3496et\u0100;e\u11A8\u349D\xF1\u11AE\u0180;af\u117B\u34A6\u05B0r\u0165\u34AB\u05B1\xBB\u117Car\xF2\u1148\u0200cemt\u34B9\u34BE\u34C2\u34C5r;\uC000\u{1D4C8}tm\xEE\xF1i\xEC\u3415ar\xE6\u11BE\u0100ar\u34CE\u34D5r\u0100;f\u34D4\u17BF\u6606\u0100an\u34DA\u34EDight\u0100ep\u34E3\u34EApsilo\xEE\u1EE0h\xE9\u2EAFs\xBB\u2852\u0280bcmnp\u34FB\u355E\u1209\u358B\u358E\u0480;Edemnprs\u350E\u350F\u3511\u3515\u351E\u3523\u352C\u3531\u3536\u6282;\u6AC5ot;\u6ABD\u0100;d\u11DA\u351Aot;\u6AC3ult;\u6AC1\u0100Ee\u3528\u352A;\u6ACB;\u628Alus;\u6ABFarr;\u6979\u0180eiu\u353D\u3552\u3555t\u0180;en\u350E\u3545\u354Bq\u0100;q\u11DA\u350Feq\u0100;q\u352B\u3528m;\u6AC7\u0100bp\u355A\u355C;\u6AD5;\u6AD3c\u0300;acens\u11ED\u356C\u3572\u3579\u357B\u3326ppro\xF8\u32FAurlye\xF1\u11FE\xF1\u11F3\u0180aes\u3582\u3588\u331Bppro\xF8\u331Aq\xF1\u3317g;\u666A\u0680123;Edehlmnps\u35A9\u35AC\u35AF\u121C\u35B2\u35B4\u35C0\u35C9\u35D5\u35DA\u35DF\u35E8\u35ED\u803B\xB9\u40B9\u803B\xB2\u40B2\u803B\xB3\u40B3;\u6AC6\u0100os\u35B9\u35BCt;\u6ABEub;\u6AD8\u0100;d\u1222\u35C5ot;\u6AC4s\u0100ou\u35CF\u35D2l;\u67C9b;\u6AD7arr;\u697Bult;\u6AC2\u0100Ee\u35E4\u35E6;\u6ACC;\u628Blus;\u6AC0\u0180eiu\u35F4\u3609\u360Ct\u0180;en\u121C\u35FC\u3602q\u0100;q\u1222\u35B2eq\u0100;q\u35E7\u35E4m;\u6AC8\u0100bp\u3611\u3613;\u6AD4;\u6AD6\u0180Aan\u361C\u3620\u362Drr;\u61D9r\u0100hr\u3626\u3628\xEB\u222E\u0100;o\u0A2B\u0A29war;\u692Alig\u803B\xDF\u40DF\u0BE1\u3651\u365D\u3660\u12CE\u3673\u3679\0\u367E\u36C2\0\0\0\0\0\u36DB\u3703\0\u3709\u376C\0\0\0\u3787\u0272\u3656\0\0\u365Bget;\u6316;\u43C4r\xEB\u0E5F\u0180aey\u3666\u366B\u3670ron;\u4165dil;\u4163;\u4442lrec;\u6315r;\uC000\u{1D531}\u0200eiko\u3686\u369D\u36B5\u36BC\u01F2\u368B\0\u3691e\u01004f\u1284\u1281a\u0180;sv\u3698\u3699\u369B\u43B8ym;\u43D1\u0100cn\u36A2\u36B2k\u0100as\u36A8\u36AEppro\xF8\u12C1im\xBB\u12ACs\xF0\u129E\u0100as\u36BA\u36AE\xF0\u12C1rn\u803B\xFE\u40FE\u01EC\u031F\u36C6\u22E7es\u8180\xD7;bd\u36CF\u36D0\u36D8\u40D7\u0100;a\u190F\u36D5r;\u6A31;\u6A30\u0180eps\u36E1\u36E3\u3700\xE1\u2A4D\u0200;bcf\u0486\u36EC\u36F0\u36F4ot;\u6336ir;\u6AF1\u0100;o\u36F9\u36FC\uC000\u{1D565}rk;\u6ADA\xE1\u3362rime;\u6034\u0180aip\u370F\u3712\u3764d\xE5\u1248\u0380adempst\u3721\u374D\u3740\u3751\u3757\u375C\u375Fngle\u0280;dlqr\u3730\u3731\u3736\u3740\u3742\u65B5own\xBB\u1DBBeft\u0100;e\u2800\u373E\xF1\u092E;\u625Cight\u0100;e\u32AA\u374B\xF1\u105Aot;\u65ECinus;\u6A3Alus;\u6A39b;\u69CDime;\u6A3Bezium;\u63E2\u0180cht\u3772\u377D\u3781\u0100ry\u3777\u377B;\uC000\u{1D4C9};\u4446cy;\u445Brok;\u4167\u0100io\u378B\u378Ex\xF4\u1777head\u0100lr\u3797\u37A0eftarro\xF7\u084Fightarrow\xBB\u0F5D\u0900AHabcdfghlmoprstuw\u37D0\u37D3\u37D7\u37E4\u37F0\u37FC\u380E\u381C\u3823\u3834\u3851\u385D\u386B\u38A9\u38CC\u38D2\u38EA\u38F6r\xF2\u03EDar;\u6963\u0100cr\u37DC\u37E2ute\u803B\xFA\u40FA\xF2\u1150r\u01E3\u37EA\0\u37EDy;\u445Eve;\u416D\u0100iy\u37F5\u37FArc\u803B\xFB\u40FB;\u4443\u0180abh\u3803\u3806\u380Br\xF2\u13ADlac;\u4171a\xF2\u13C3\u0100ir\u3813\u3818sht;\u697E;\uC000\u{1D532}rave\u803B\xF9\u40F9\u0161\u3827\u3831r\u0100lr\u382C\u382E\xBB\u0957\xBB\u1083lk;\u6580\u0100ct\u3839\u384D\u026F\u383F\0\0\u384Arn\u0100;e\u3845\u3846\u631Cr\xBB\u3846op;\u630Fri;\u65F8\u0100al\u3856\u385Acr;\u416B\u80BB\xA8\u0349\u0100gp\u3862\u3866on;\u4173f;\uC000\u{1D566}\u0300adhlsu\u114B\u3878\u387D\u1372\u3891\u38A0own\xE1\u13B3arpoon\u0100lr\u3888\u388Cef\xF4\u382Digh\xF4\u382Fi\u0180;hl\u3899\u389A\u389C\u43C5\xBB\u13FAon\xBB\u389Aparrows;\u61C8\u0180cit\u38B0\u38C4\u38C8\u026F\u38B6\0\0\u38C1rn\u0100;e\u38BC\u38BD\u631Dr\xBB\u38BDop;\u630Eng;\u416Fri;\u65F9cr;\uC000\u{1D4CA}\u0180dir\u38D9\u38DD\u38E2ot;\u62F0lde;\u4169i\u0100;f\u3730\u38E8\xBB\u1813\u0100am\u38EF\u38F2r\xF2\u38A8l\u803B\xFC\u40FCangle;\u69A7\u0780ABDacdeflnoprsz\u391C\u391F\u3929\u392D\u39B5\u39B8\u39BD\u39DF\u39E4\u39E8\u39F3\u39F9\u39FD\u3A01\u3A20r\xF2\u03F7ar\u0100;v\u3926\u3927\u6AE8;\u6AE9as\xE8\u03E1\u0100nr\u3932\u3937grt;\u699C\u0380eknprst\u34E3\u3946\u394B\u3952\u395D\u3964\u3996app\xE1\u2415othin\xE7\u1E96\u0180hir\u34EB\u2EC8\u3959op\xF4\u2FB5\u0100;h\u13B7\u3962\xEF\u318D\u0100iu\u3969\u396Dgm\xE1\u33B3\u0100bp\u3972\u3984setneq\u0100;q\u397D\u3980\uC000\u228A\uFE00;\uC000\u2ACB\uFE00setneq\u0100;q\u398F\u3992\uC000\u228B\uFE00;\uC000\u2ACC\uFE00\u0100hr\u399B\u399Fet\xE1\u369Ciangle\u0100lr\u39AA\u39AFeft\xBB\u0925ight\xBB\u1051y;\u4432ash\xBB\u1036\u0180elr\u39C4\u39D2\u39D7\u0180;be\u2DEA\u39CB\u39CFar;\u62BBq;\u625Alip;\u62EE\u0100bt\u39DC\u1468a\xF2\u1469r;\uC000\u{1D533}tr\xE9\u39AEsu\u0100bp\u39EF\u39F1\xBB\u0D1C\xBB\u0D59pf;\uC000\u{1D567}ro\xF0\u0EFBtr\xE9\u39B4\u0100cu\u3A06\u3A0Br;\uC000\u{1D4CB}\u0100bp\u3A10\u3A18n\u0100Ee\u3980\u3A16\xBB\u397En\u0100Ee\u3992\u3A1E\xBB\u3990igzag;\u699A\u0380cefoprs\u3A36\u3A3B\u3A56\u3A5B\u3A54\u3A61\u3A6Airc;\u4175\u0100di\u3A40\u3A51\u0100bg\u3A45\u3A49ar;\u6A5Fe\u0100;q\u15FA\u3A4F;\u6259erp;\u6118r;\uC000\u{1D534}pf;\uC000\u{1D568}\u0100;e\u1479\u3A66at\xE8\u1479cr;\uC000\u{1D4CC}\u0AE3\u178E\u3A87\0\u3A8B\0\u3A90\u3A9B\0\0\u3A9D\u3AA8\u3AAB\u3AAF\0\0\u3AC3\u3ACE\0\u3AD8\u17DC\u17DFtr\xE9\u17D1r;\uC000\u{1D535}\u0100Aa\u3A94\u3A97r\xF2\u03C3r\xF2\u09F6;\u43BE\u0100Aa\u3AA1\u3AA4r\xF2\u03B8r\xF2\u09EBa\xF0\u2713is;\u62FB\u0180dpt\u17A4\u3AB5\u3ABE\u0100fl\u3ABA\u17A9;\uC000\u{1D569}im\xE5\u17B2\u0100Aa\u3AC7\u3ACAr\xF2\u03CEr\xF2\u0A01\u0100cq\u3AD2\u17B8r;\uC000\u{1D4CD}\u0100pt\u17D6\u3ADCr\xE9\u17D4\u0400acefiosu\u3AF0\u3AFD\u3B08\u3B0C\u3B11\u3B15\u3B1B\u3B21c\u0100uy\u3AF6\u3AFBte\u803B\xFD\u40FD;\u444F\u0100iy\u3B02\u3B06rc;\u4177;\u444Bn\u803B\xA5\u40A5r;\uC000\u{1D536}cy;\u4457pf;\uC000\u{1D56A}cr;\uC000\u{1D4CE}\u0100cm\u3B26\u3B29y;\u444El\u803B\xFF\u40FF\u0500acdefhiosw\u3B42\u3B48\u3B54\u3B58\u3B64\u3B69\u3B6D\u3B74\u3B7A\u3B80cute;\u417A\u0100ay\u3B4D\u3B52ron;\u417E;\u4437ot;\u417C\u0100et\u3B5D\u3B61tr\xE6\u155Fa;\u43B6r;\uC000\u{1D537}cy;\u4436grarr;\u61DDpf;\uC000\u{1D56B}cr;\uC000\u{1D4CF}\u0100jn\u3B85\u3B87;\u600Dj;\u600C'.split("").map(u=>u.charCodeAt(0)));var Q0,Sc=new Map([[0,65533],[128,8364],[130,8218],[131,402],[132,8222],[133,8230],[134,8224],[135,8225],[136,710],[137,8240],[138,352],[139,8249],[140,338],[142,381],[145,8216],[146,8217],[147,8220],[148,8221],[149,8226],[150,8211],[151,8212],[152,732],[153,8482],[154,353],[155,8250],[156,339],[158,382],[159,376]]),yr=(Q0=String.fromCodePoint)!==null&&Q0!==void 0?Q0:function(u){let e="";return u>65535&&(u-=65536,e+=String.fromCharCode(u>>>10&1023|55296),u=56320|u&1023),e+=String.fromCharCode(u),e};function X0(u){var e;return u>=55296&&u<=57343||u>1114111?65533:(e=Sc.get(u))!==null&&e!==void 0?e:u}var V;(function(u){u[u.NUM=35]="NUM",u[u.SEMI=59]="SEMI",u[u.EQUALS=61]="EQUALS",u[u.ZERO=48]="ZERO",u[u.NINE=57]="NINE",u[u.LOWER_A=97]="LOWER_A",u[u.LOWER_F=102]="LOWER_F",u[u.LOWER_X=120]="LOWER_X",u[u.LOWER_Z=122]="LOWER_Z",u[u.UPPER_A=65]="UPPER_A",u[u.UPPER_F=70]="UPPER_F",u[u.UPPER_Z=90]="UPPER_Z"})(V||(V={}));var Cc=32,Ru;(function(u){u[u.VALUE_LENGTH=49152]="VALUE_LENGTH",u[u.BRANCH_LENGTH=16256]="BRANCH_LENGTH",u[u.JUMP_TABLE=127]="JUMP_TABLE"})(Ru||(Ru={}));function K0(u){return u>=V.ZERO&&u<=V.NINE}function Lc(u){return u>=V.UPPER_A&&u<=V.UPPER_F||u>=V.LOWER_A&&u<=V.LOWER_F}function Dc(u){return u>=V.UPPER_A&&u<=V.UPPER_Z||u>=V.LOWER_A&&u<=V.LOWER_Z||K0(u)}function Oc(u){return u===V.EQUALS||Dc(u)}var Y;(function(u){u[u.EntityStart=0]="EntityStart",u[u.NumericStart=1]="NumericStart",u[u.NumericDecimal=2]="NumericDecimal",u[u.NumericHex=3]="NumericHex",u[u.NamedEntity=4]="NamedEntity"})(Y||(Y={}));var du;(function(u){u[u.Legacy=0]="Legacy",u[u.Strict=1]="Strict",u[u.Attribute=2]="Attribute"})(du||(du={}));var Nt=class{constructor(e,t,a){this.decodeTree=e,this.emitCodePoint=t,this.errors=a,this.state=Y.EntityStart,this.consumed=1,this.result=0,this.treeIndex=0,this.excess=1,this.decodeMode=du.Strict}startEntity(e){this.decodeMode=e,this.state=Y.EntityStart,this.result=0,this.treeIndex=0,this.excess=1,this.consumed=1}write(e,t){switch(this.state){case Y.EntityStart:return e.charCodeAt(t)===V.NUM?(this.state=Y.NumericStart,this.consumed+=1,this.stateNumericStart(e,t+1)):(this.state=Y.NamedEntity,this.stateNamedEntity(e,t));case Y.NumericStart:return this.stateNumericStart(e,t);case Y.NumericDecimal:return this.stateNumericDecimal(e,t);case Y.NumericHex:return this.stateNumericHex(e,t);case Y.NamedEntity:return this.stateNamedEntity(e,t)}}stateNumericStart(e,t){return t>=e.length?-1:(e.charCodeAt(t)|Cc)===V.LOWER_X?(this.state=Y.NumericHex,this.consumed+=1,this.stateNumericHex(e,t+1)):(this.state=Y.NumericDecimal,this.stateNumericDecimal(e,t))}addToNumericResult(e,t,a,s){if(t!==a){let i=a-t;this.result=this.result*Math.pow(s,i)+Number.parseInt(e.substr(t,i),s),this.consumed+=i}}stateNumericHex(e,t){let a=t;for(;t<e.length;){let s=e.charCodeAt(t);if(K0(s)||Lc(s))t+=1;else return this.addToNumericResult(e,a,t,16),this.emitNumericEntity(s,3)}return this.addToNumericResult(e,a,t,16),-1}stateNumericDecimal(e,t){let a=t;for(;t<e.length;){let s=e.charCodeAt(t);if(K0(s))t+=1;else return this.addToNumericResult(e,a,t,10),this.emitNumericEntity(s,2)}return this.addToNumericResult(e,a,t,10),-1}emitNumericEntity(e,t){var a;if(this.consumed<=t)return(a=this.errors)===null||a===void 0||a.absenceOfDigitsInNumericCharacterReference(this.consumed),0;if(e===V.SEMI)this.consumed+=1;else if(this.decodeMode===du.Strict)return 0;return this.emitCodePoint(X0(this.result),this.consumed),this.errors&&(e!==V.SEMI&&this.errors.missingSemicolonAfterCharacterReference(),this.errors.validateNumericCharacterReference(this.result)),this.consumed}stateNamedEntity(e,t){let{decodeTree:a}=this,s=a[this.treeIndex],i=(s&Ru.VALUE_LENGTH)>>14;for(;t<e.length;t++,this.excess++){let n=e.charCodeAt(t);if(this.treeIndex=Rc(a,s,this.treeIndex+Math.max(1,i),n),this.treeIndex<0)return this.result===0||this.decodeMode===du.Attribute&&(i===0||Oc(n))?0:this.emitNotTerminatedNamedEntity();if(s=a[this.treeIndex],i=(s&Ru.VALUE_LENGTH)>>14,i!==0){if(n===V.SEMI)return this.emitNamedEntityData(this.treeIndex,i,this.consumed+this.excess);this.decodeMode!==du.Strict&&(this.result=this.treeIndex,this.consumed+=this.excess,this.excess=0)}}return-1}emitNotTerminatedNamedEntity(){var e;let{result:t,decodeTree:a}=this,s=(a[t]&Ru.VALUE_LENGTH)>>14;return this.emitNamedEntityData(t,s,this.consumed),(e=this.errors)===null||e===void 0||e.missingSemicolonAfterCharacterReference(),this.consumed}emitNamedEntityData(e,t,a){let{decodeTree:s}=this;return this.emitCodePoint(t===1?s[e]&~Ru.VALUE_LENGTH:s[e+1],a),t===3&&this.emitCodePoint(s[e+2],a),a}end(){var e;switch(this.state){case Y.NamedEntity:return this.result!==0&&(this.decodeMode!==du.Attribute||this.result===this.treeIndex)?this.emitNotTerminatedNamedEntity():0;case Y.NumericDecimal:return this.emitNumericEntity(0,2);case Y.NumericHex:return this.emitNumericEntity(0,3);case Y.NumericStart:return(e=this.errors)===null||e===void 0||e.absenceOfDigitsInNumericCharacterReference(this.consumed),0;case Y.EntityStart:return 0}}};function Rc(u,e,t,a){let s=(e&Ru.BRANCH_LENGTH)>>7,i=e&Ru.JUMP_TABLE;if(s===0)return i!==0&&a===i?t:-1;if(i){let l=a-i;return l<0||l>=s?-1:u[t+l]-1}let n=t,d=n+s-1;for(;n<=d;){let l=n+d>>>1,h=u[l];if(h<a)n=l+1;else if(h>a)d=l-1;else return u[l+s]}return-1}var Ce={};$(Ce,{ATTRS:()=>fu,DOCUMENT_MODE:()=>G,NS:()=>E,NUMBERED_HEADERS:()=>ae,SPECIAL_ELEMENTS:()=>j0,TAG_ID:()=>r,TAG_NAMES:()=>b,getTagID:()=>yu,hasUnescapedText:()=>z0});var E;(function(u){u.HTML="http://www.w3.org/1999/xhtml",u.MATHML="http://www.w3.org/1998/Math/MathML",u.SVG="http://www.w3.org/2000/svg",u.XLINK="http://www.w3.org/1999/xlink",u.XML="http://www.w3.org/XML/1998/namespace",u.XMLNS="http://www.w3.org/2000/xmlns/"})(E||(E={}));var fu;(function(u){u.TYPE="type",u.ACTION="action",u.ENCODING="encoding",u.PROMPT="prompt",u.NAME="name",u.COLOR="color",u.FACE="face",u.SIZE="size"})(fu||(fu={}));var G;(function(u){u.NO_QUIRKS="no-quirks",u.QUIRKS="quirks",u.LIMITED_QUIRKS="limited-quirks"})(G||(G={}));var b;(function(u){u.A="a",u.ADDRESS="address",u.ANNOTATION_XML="annotation-xml",u.APPLET="applet",u.AREA="area",u.ARTICLE="article",u.ASIDE="aside",u.B="b",u.BASE="base",u.BASEFONT="basefont",u.BGSOUND="bgsound",u.BIG="big",u.BLOCKQUOTE="blockquote",u.BODY="body",u.BR="br",u.BUTTON="button",u.CAPTION="caption",u.CENTER="center",u.CODE="code",u.COL="col",u.COLGROUP="colgroup",u.DD="dd",u.DESC="desc",u.DETAILS="details",u.DIALOG="dialog",u.DIR="dir",u.DIV="div",u.DL="dl",u.DT="dt",u.EM="em",u.EMBED="embed",u.FIELDSET="fieldset",u.FIGCAPTION="figcaption",u.FIGURE="figure",u.FONT="font",u.FOOTER="footer",u.FOREIGN_OBJECT="foreignObject",u.FORM="form",u.FRAME="frame",u.FRAMESET="frameset",u.H1="h1",u.H2="h2",u.H3="h3",u.H4="h4",u.H5="h5",u.H6="h6",u.HEAD="head",u.HEADER="header",u.HGROUP="hgroup",u.HR="hr",u.HTML="html",u.I="i",u.IMG="img",u.IMAGE="image",u.INPUT="input",u.IFRAME="iframe",u.KEYGEN="keygen",u.LABEL="label",u.LI="li",u.LINK="link",u.LISTING="listing",u.MAIN="main",u.MALIGNMARK="malignmark",u.MARQUEE="marquee",u.MATH="math",u.MENU="menu",u.META="meta",u.MGLYPH="mglyph",u.MI="mi",u.MO="mo",u.MN="mn",u.MS="ms",u.MTEXT="mtext",u.NAV="nav",u.NOBR="nobr",u.NOFRAMES="noframes",u.NOEMBED="noembed",u.NOSCRIPT="noscript",u.OBJECT="object",u.OL="ol",u.OPTGROUP="optgroup",u.OPTION="option",u.P="p",u.PARAM="param",u.PLAINTEXT="plaintext",u.PRE="pre",u.RB="rb",u.RP="rp",u.RT="rt",u.RTC="rtc",u.RUBY="ruby",u.S="s",u.SCRIPT="script",u.SEARCH="search",u.SECTION="section",u.SELECT="select",u.SOURCE="source",u.SMALL="small",u.SPAN="span",u.STRIKE="strike",u.STRONG="strong",u.STYLE="style",u.SUB="sub",u.SUMMARY="summary",u.SUP="sup",u.TABLE="table",u.TBODY="tbody",u.TEMPLATE="template",u.TEXTAREA="textarea",u.TFOOT="tfoot",u.TD="td",u.TH="th",u.THEAD="thead",u.TITLE="title",u.TR="tr",u.TRACK="track",u.TT="tt",u.U="u",u.UL="ul",u.SVG="svg",u.VAR="var",u.WBR="wbr",u.XMP="xmp"})(b||(b={}));var r;(function(u){u[u.UNKNOWN=0]="UNKNOWN",u[u.A=1]="A",u[u.ADDRESS=2]="ADDRESS",u[u.ANNOTATION_XML=3]="ANNOTATION_XML",u[u.APPLET=4]="APPLET",u[u.AREA=5]="AREA",u[u.ARTICLE=6]="ARTICLE",u[u.ASIDE=7]="ASIDE",u[u.B=8]="B",u[u.BASE=9]="BASE",u[u.BASEFONT=10]="BASEFONT",u[u.BGSOUND=11]="BGSOUND",u[u.BIG=12]="BIG",u[u.BLOCKQUOTE=13]="BLOCKQUOTE",u[u.BODY=14]="BODY",u[u.BR=15]="BR",u[u.BUTTON=16]="BUTTON",u[u.CAPTION=17]="CAPTION",u[u.CENTER=18]="CENTER",u[u.CODE=19]="CODE",u[u.COL=20]="COL",u[u.COLGROUP=21]="COLGROUP",u[u.DD=22]="DD",u[u.DESC=23]="DESC",u[u.DETAILS=24]="DETAILS",u[u.DIALOG=25]="DIALOG",u[u.DIR=26]="DIR",u[u.DIV=27]="DIV",u[u.DL=28]="DL",u[u.DT=29]="DT",u[u.EM=30]="EM",u[u.EMBED=31]="EMBED",u[u.FIELDSET=32]="FIELDSET",u[u.FIGCAPTION=33]="FIGCAPTION",u[u.FIGURE=34]="FIGURE",u[u.FONT=35]="FONT",u[u.FOOTER=36]="FOOTER",u[u.FOREIGN_OBJECT=37]="FOREIGN_OBJECT",u[u.FORM=38]="FORM",u[u.FRAME=39]="FRAME",u[u.FRAMESET=40]="FRAMESET",u[u.H1=41]="H1",u[u.H2=42]="H2",u[u.H3=43]="H3",u[u.H4=44]="H4",u[u.H5=45]="H5",u[u.H6=46]="H6",u[u.HEAD=47]="HEAD",u[u.HEADER=48]="HEADER",u[u.HGROUP=49]="HGROUP",u[u.HR=50]="HR",u[u.HTML=51]="HTML",u[u.I=52]="I",u[u.IMG=53]="IMG",u[u.IMAGE=54]="IMAGE",u[u.INPUT=55]="INPUT",u[u.IFRAME=56]="IFRAME",u[u.KEYGEN=57]="KEYGEN",u[u.LABEL=58]="LABEL",u[u.LI=59]="LI",u[u.LINK=60]="LINK",u[u.LISTING=61]="LISTING",u[u.MAIN=62]="MAIN",u[u.MALIGNMARK=63]="MALIGNMARK",u[u.MARQUEE=64]="MARQUEE",u[u.MATH=65]="MATH",u[u.MENU=66]="MENU",u[u.META=67]="META",u[u.MGLYPH=68]="MGLYPH",u[u.MI=69]="MI",u[u.MO=70]="MO",u[u.MN=71]="MN",u[u.MS=72]="MS",u[u.MTEXT=73]="MTEXT",u[u.NAV=74]="NAV",u[u.NOBR=75]="NOBR",u[u.NOFRAMES=76]="NOFRAMES",u[u.NOEMBED=77]="NOEMBED",u[u.NOSCRIPT=78]="NOSCRIPT",u[u.OBJECT=79]="OBJECT",u[u.OL=80]="OL",u[u.OPTGROUP=81]="OPTGROUP",u[u.OPTION=82]="OPTION",u[u.P=83]="P",u[u.PARAM=84]="PARAM",u[u.PLAINTEXT=85]="PLAINTEXT",u[u.PRE=86]="PRE",u[u.RB=87]="RB",u[u.RP=88]="RP",u[u.RT=89]="RT",u[u.RTC=90]="RTC",u[u.RUBY=91]="RUBY",u[u.S=92]="S",u[u.SCRIPT=93]="SCRIPT",u[u.SEARCH=94]="SEARCH",u[u.SECTION=95]="SECTION",u[u.SELECT=96]="SELECT",u[u.SOURCE=97]="SOURCE",u[u.SMALL=98]="SMALL",u[u.SPAN=99]="SPAN",u[u.STRIKE=100]="STRIKE",u[u.STRONG=101]="STRONG",u[u.STYLE=102]="STYLE",u[u.SUB=103]="SUB",u[u.SUMMARY=104]="SUMMARY",u[u.SUP=105]="SUP",u[u.TABLE=106]="TABLE",u[u.TBODY=107]="TBODY",u[u.TEMPLATE=108]="TEMPLATE",u[u.TEXTAREA=109]="TEXTAREA",u[u.TFOOT=110]="TFOOT",u[u.TD=111]="TD",u[u.TH=112]="TH",u[u.THEAD=113]="THEAD",u[u.TITLE=114]="TITLE",u[u.TR=115]="TR",u[u.TRACK=116]="TRACK",u[u.TT=117]="TT",u[u.U=118]="U",u[u.UL=119]="UL",u[u.SVG=120]="SVG",u[u.VAR=121]="VAR",u[u.WBR=122]="WBR",u[u.XMP=123]="XMP"})(r||(r={}));var yc=new Map([[b.A,r.A],[b.ADDRESS,r.ADDRESS],[b.ANNOTATION_XML,r.ANNOTATION_XML],[b.APPLET,r.APPLET],[b.AREA,r.AREA],[b.ARTICLE,r.ARTICLE],[b.ASIDE,r.ASIDE],[b.B,r.B],[b.BASE,r.BASE],[b.BASEFONT,r.BASEFONT],[b.BGSOUND,r.BGSOUND],[b.BIG,r.BIG],[b.BLOCKQUOTE,r.BLOCKQUOTE],[b.BODY,r.BODY],[b.BR,r.BR],[b.BUTTON,r.BUTTON],[b.CAPTION,r.CAPTION],[b.CENTER,r.CENTER],[b.CODE,r.CODE],[b.COL,r.COL],[b.COLGROUP,r.COLGROUP],[b.DD,r.DD],[b.DESC,r.DESC],[b.DETAILS,r.DETAILS],[b.DIALOG,r.DIALOG],[b.DIR,r.DIR],[b.DIV,r.DIV],[b.DL,r.DL],[b.DT,r.DT],[b.EM,r.EM],[b.EMBED,r.EMBED],[b.FIELDSET,r.FIELDSET],[b.FIGCAPTION,r.FIGCAPTION],[b.FIGURE,r.FIGURE],[b.FONT,r.FONT],[b.FOOTER,r.FOOTER],[b.FOREIGN_OBJECT,r.FOREIGN_OBJECT],[b.FORM,r.FORM],[b.FRAME,r.FRAME],[b.FRAMESET,r.FRAMESET],[b.H1,r.H1],[b.H2,r.H2],[b.H3,r.H3],[b.H4,r.H4],[b.H5,r.H5],[b.H6,r.H6],[b.HEAD,r.HEAD],[b.HEADER,r.HEADER],[b.HGROUP,r.HGROUP],[b.HR,r.HR],[b.HTML,r.HTML],[b.I,r.I],[b.IMG,r.IMG],[b.IMAGE,r.IMAGE],[b.INPUT,r.INPUT],[b.IFRAME,r.IFRAME],[b.KEYGEN,r.KEYGEN],[b.LABEL,r.LABEL],[b.LI,r.LI],[b.LINK,r.LINK],[b.LISTING,r.LISTING],[b.MAIN,r.MAIN],[b.MALIGNMARK,r.MALIGNMARK],[b.MARQUEE,r.MARQUEE],[b.MATH,r.MATH],[b.MENU,r.MENU],[b.META,r.META],[b.MGLYPH,r.MGLYPH],[b.MI,r.MI],[b.MO,r.MO],[b.MN,r.MN],[b.MS,r.MS],[b.MTEXT,r.MTEXT],[b.NAV,r.NAV],[b.NOBR,r.NOBR],[b.NOFRAMES,r.NOFRAMES],[b.NOEMBED,r.NOEMBED],[b.NOSCRIPT,r.NOSCRIPT],[b.OBJECT,r.OBJECT],[b.OL,r.OL],[b.OPTGROUP,r.OPTGROUP],[b.OPTION,r.OPTION],[b.P,r.P],[b.PARAM,r.PARAM],[b.PLAINTEXT,r.PLAINTEXT],[b.PRE,r.PRE],[b.RB,r.RB],[b.RP,r.RP],[b.RT,r.RT],[b.RTC,r.RTC],[b.RUBY,r.RUBY],[b.S,r.S],[b.SCRIPT,r.SCRIPT],[b.SEARCH,r.SEARCH],[b.SECTION,r.SECTION],[b.SELECT,r.SELECT],[b.SOURCE,r.SOURCE],[b.SMALL,r.SMALL],[b.SPAN,r.SPAN],[b.STRIKE,r.STRIKE],[b.STRONG,r.STRONG],[b.STYLE,r.STYLE],[b.SUB,r.SUB],[b.SUMMARY,r.SUMMARY],[b.SUP,r.SUP],[b.TABLE,r.TABLE],[b.TBODY,r.TBODY],[b.TEMPLATE,r.TEMPLATE],[b.TEXTAREA,r.TEXTAREA],[b.TFOOT,r.TFOOT],[b.TD,r.TD],[b.TH,r.TH],[b.THEAD,r.THEAD],[b.TITLE,r.TITLE],[b.TR,r.TR],[b.TRACK,r.TRACK],[b.TT,r.TT],[b.U,r.U],[b.UL,r.UL],[b.SVG,r.SVG],[b.VAR,r.VAR],[b.WBR,r.WBR],[b.XMP,r.XMP]]);function yu(u){var e;return(e=yc.get(u))!==null&&e!==void 0?e:r.UNKNOWN}var g=r,j0={[E.HTML]:new Set([g.ADDRESS,g.APPLET,g.AREA,g.ARTICLE,g.ASIDE,g.BASE,g.BASEFONT,g.BGSOUND,g.BLOCKQUOTE,g.BODY,g.BR,g.BUTTON,g.CAPTION,g.CENTER,g.COL,g.COLGROUP,g.DD,g.DETAILS,g.DIR,g.DIV,g.DL,g.DT,g.EMBED,g.FIELDSET,g.FIGCAPTION,g.FIGURE,g.FOOTER,g.FORM,g.FRAME,g.FRAMESET,g.H1,g.H2,g.H3,g.H4,g.H5,g.H6,g.HEAD,g.HEADER,g.HGROUP,g.HR,g.HTML,g.IFRAME,g.IMG,g.INPUT,g.LI,g.LINK,g.LISTING,g.MAIN,g.MARQUEE,g.MENU,g.META,g.NAV,g.NOEMBED,g.NOFRAMES,g.NOSCRIPT,g.OBJECT,g.OL,g.P,g.PARAM,g.PLAINTEXT,g.PRE,g.SCRIPT,g.SECTION,g.SELECT,g.SOURCE,g.STYLE,g.SUMMARY,g.TABLE,g.TBODY,g.TD,g.TEMPLATE,g.TEXTAREA,g.TFOOT,g.TH,g.THEAD,g.TITLE,g.TR,g.TRACK,g.UL,g.WBR,g.XMP]),[E.MATHML]:new Set([g.MI,g.MO,g.MN,g.MS,g.MTEXT,g.ANNOTATION_XML]),[E.SVG]:new Set([g.TITLE,g.FOREIGN_OBJECT,g.DESC]),[E.XLINK]:new Set,[E.XML]:new Set,[E.XMLNS]:new Set},ae=new Set([g.H1,g.H2,g.H3,g.H4,g.H5,g.H6]),Pc=new Set([b.STYLE,b.SCRIPT,b.XMP,b.IFRAME,b.NOEMBED,b.NOFRAMES,b.PLAINTEXT]);function z0(u,e){return Pc.has(u)||e&&u===b.NOSCRIPT}var o;(function(u){u[u.DATA=0]="DATA",u[u.RCDATA=1]="RCDATA",u[u.RAWTEXT=2]="RAWTEXT",u[u.SCRIPT_DATA=3]="SCRIPT_DATA",u[u.PLAINTEXT=4]="PLAINTEXT",u[u.TAG_OPEN=5]="TAG_OPEN",u[u.END_TAG_OPEN=6]="END_TAG_OPEN",u[u.TAG_NAME=7]="TAG_NAME",u[u.RCDATA_LESS_THAN_SIGN=8]="RCDATA_LESS_THAN_SIGN",u[u.RCDATA_END_TAG_OPEN=9]="RCDATA_END_TAG_OPEN",u[u.RCDATA_END_TAG_NAME=10]="RCDATA_END_TAG_NAME",u[u.RAWTEXT_LESS_THAN_SIGN=11]="RAWTEXT_LESS_THAN_SIGN",u[u.RAWTEXT_END_TAG_OPEN=12]="RAWTEXT_END_TAG_OPEN",u[u.RAWTEXT_END_TAG_NAME=13]="RAWTEXT_END_TAG_NAME",u[u.SCRIPT_DATA_LESS_THAN_SIGN=14]="SCRIPT_DATA_LESS_THAN_SIGN",u[u.SCRIPT_DATA_END_TAG_OPEN=15]="SCRIPT_DATA_END_TAG_OPEN",u[u.SCRIPT_DATA_END_TAG_NAME=16]="SCRIPT_DATA_END_TAG_NAME",u[u.SCRIPT_DATA_ESCAPE_START=17]="SCRIPT_DATA_ESCAPE_START",u[u.SCRIPT_DATA_ESCAPE_START_DASH=18]="SCRIPT_DATA_ESCAPE_START_DASH",u[u.SCRIPT_DATA_ESCAPED=19]="SCRIPT_DATA_ESCAPED",u[u.SCRIPT_DATA_ESCAPED_DASH=20]="SCRIPT_DATA_ESCAPED_DASH",u[u.SCRIPT_DATA_ESCAPED_DASH_DASH=21]="SCRIPT_DATA_ESCAPED_DASH_DASH",u[u.SCRIPT_DATA_ESCAPED_LESS_THAN_SIGN=22]="SCRIPT_DATA_ESCAPED_LESS_THAN_SIGN",u[u.SCRIPT_DATA_ESCAPED_END_TAG_OPEN=23]="SCRIPT_DATA_ESCAPED_END_TAG_OPEN",u[u.SCRIPT_DATA_ESCAPED_END_TAG_NAME=24]="SCRIPT_DATA_ESCAPED_END_TAG_NAME",u[u.SCRIPT_DATA_DOUBLE_ESCAPE_START=25]="SCRIPT_DATA_DOUBLE_ESCAPE_START",u[u.SCRIPT_DATA_DOUBLE_ESCAPED=26]="SCRIPT_DATA_DOUBLE_ESCAPED",u[u.SCRIPT_DATA_DOUBLE_ESCAPED_DASH=27]="SCRIPT_DATA_DOUBLE_ESCAPED_DASH",u[u.SCRIPT_DATA_DOUBLE_ESCAPED_DASH_DASH=28]="SCRIPT_DATA_DOUBLE_ESCAPED_DASH_DASH",u[u.SCRIPT_DATA_DOUBLE_ESCAPED_LESS_THAN_SIGN=29]="SCRIPT_DATA_DOUBLE_ESCAPED_LESS_THAN_SIGN",u[u.SCRIPT_DATA_DOUBLE_ESCAPE_END=30]="SCRIPT_DATA_DOUBLE_ESCAPE_END",u[u.BEFORE_ATTRIBUTE_NAME=31]="BEFORE_ATTRIBUTE_NAME",u[u.ATTRIBUTE_NAME=32]="ATTRIBUTE_NAME",u[u.AFTER_ATTRIBUTE_NAME=33]="AFTER_ATTRIBUTE_NAME",u[u.BEFORE_ATTRIBUTE_VALUE=34]="BEFORE_ATTRIBUTE_VALUE",u[u.ATTRIBUTE_VALUE_DOUBLE_QUOTED=35]="ATTRIBUTE_VALUE_DOUBLE_QUOTED",u[u.ATTRIBUTE_VALUE_SINGLE_QUOTED=36]="ATTRIBUTE_VALUE_SINGLE_QUOTED",u[u.ATTRIBUTE_VALUE_UNQUOTED=37]="ATTRIBUTE_VALUE_UNQUOTED",u[u.AFTER_ATTRIBUTE_VALUE_QUOTED=38]="AFTER_ATTRIBUTE_VALUE_QUOTED",u[u.SELF_CLOSING_START_TAG=39]="SELF_CLOSING_START_TAG",u[u.BOGUS_COMMENT=40]="BOGUS_COMMENT",u[u.MARKUP_DECLARATION_OPEN=41]="MARKUP_DECLARATION_OPEN",u[u.COMMENT_START=42]="COMMENT_START",u[u.COMMENT_START_DASH=43]="COMMENT_START_DASH",u[u.COMMENT=44]="COMMENT",u[u.COMMENT_LESS_THAN_SIGN=45]="COMMENT_LESS_THAN_SIGN",u[u.COMMENT_LESS_THAN_SIGN_BANG=46]="COMMENT_LESS_THAN_SIGN_BANG",u[u.COMMENT_LESS_THAN_SIGN_BANG_DASH=47]="COMMENT_LESS_THAN_SIGN_BANG_DASH",u[u.COMMENT_LESS_THAN_SIGN_BANG_DASH_DASH=48]="COMMENT_LESS_THAN_SIGN_BANG_DASH_DASH",u[u.COMMENT_END_DASH=49]="COMMENT_END_DASH",u[u.COMMENT_END=50]="COMMENT_END",u[u.COMMENT_END_BANG=51]="COMMENT_END_BANG",u[u.DOCTYPE=52]="DOCTYPE",u[u.BEFORE_DOCTYPE_NAME=53]="BEFORE_DOCTYPE_NAME",u[u.DOCTYPE_NAME=54]="DOCTYPE_NAME",u[u.AFTER_DOCTYPE_NAME=55]="AFTER_DOCTYPE_NAME",u[u.AFTER_DOCTYPE_PUBLIC_KEYWORD=56]="AFTER_DOCTYPE_PUBLIC_KEYWORD",u[u.BEFORE_DOCTYPE_PUBLIC_IDENTIFIER=57]="BEFORE_DOCTYPE_PUBLIC_IDENTIFIER",u[u.DOCTYPE_PUBLIC_IDENTIFIER_DOUBLE_QUOTED=58]="DOCTYPE_PUBLIC_IDENTIFIER_DOUBLE_QUOTED",u[u.DOCTYPE_PUBLIC_IDENTIFIER_SINGLE_QUOTED=59]="DOCTYPE_PUBLIC_IDENTIFIER_SINGLE_QUOTED",u[u.AFTER_DOCTYPE_PUBLIC_IDENTIFIER=60]="AFTER_DOCTYPE_PUBLIC_IDENTIFIER",u[u.BETWEEN_DOCTYPE_PUBLIC_AND_SYSTEM_IDENTIFIERS=61]="BETWEEN_DOCTYPE_PUBLIC_AND_SYSTEM_IDENTIFIERS",u[u.AFTER_DOCTYPE_SYSTEM_KEYWORD=62]="AFTER_DOCTYPE_SYSTEM_KEYWORD",u[u.BEFORE_DOCTYPE_SYSTEM_IDENTIFIER=63]="BEFORE_DOCTYPE_SYSTEM_IDENTIFIER",u[u.DOCTYPE_SYSTEM_IDENTIFIER_DOUBLE_QUOTED=64]="DOCTYPE_SYSTEM_IDENTIFIER_DOUBLE_QUOTED",u[u.DOCTYPE_SYSTEM_IDENTIFIER_SINGLE_QUOTED=65]="DOCTYPE_SYSTEM_IDENTIFIER_SINGLE_QUOTED",u[u.AFTER_DOCTYPE_SYSTEM_IDENTIFIER=66]="AFTER_DOCTYPE_SYSTEM_IDENTIFIER",u[u.BOGUS_DOCTYPE=67]="BOGUS_DOCTYPE",u[u.CDATA_SECTION=68]="CDATA_SECTION",u[u.CDATA_SECTION_BRACKET=69]="CDATA_SECTION_BRACKET",u[u.CDATA_SECTION_END=70]="CDATA_SECTION_END",u[u.CHARACTER_REFERENCE=71]="CHARACTER_REFERENCE",u[u.AMBIGUOUS_AMPERSAND=72]="AMBIGUOUS_AMPERSAND"})(o||(o={}));var W={DATA:o.DATA,RCDATA:o.RCDATA,RAWTEXT:o.RAWTEXT,SCRIPT_DATA:o.SCRIPT_DATA,PLAINTEXT:o.PLAINTEXT,CDATA_SECTION:o.CDATA_SECTION};function Mc(u){return u>=c.DIGIT_0&&u<=c.DIGIT_9}function Le(u){return u>=c.LATIN_CAPITAL_A&&u<=c.LATIN_CAPITAL_Z}function kc(u){return u>=c.LATIN_SMALL_A&&u<=c.LATIN_SMALL_Z}function Pu(u){return kc(u)||Le(u)}function Pr(u){return Pu(u)||Mc(u)}function St(u){return u+32}function kr(u){return u===c.SPACE||u===c.LINE_FEED||u===c.TABULATION||u===c.FORM_FEED}function Mr(u){return kr(u)||u===c.SOLIDUS||u===c.GREATER_THAN_SIGN}function wc(u){return u===c.NULL?m.nullCharacterReference:u>1114111?m.characterReferenceOutsideUnicodeRange:Tt(u)?m.surrogateCharacterReference:xt(u)?m.noncharacterCharacterReference:gt(u)||u===c.CARRIAGE_RETURN?m.controlCharacterReference:null}var De=class{constructor(e,t){this.options=e,this.handler=t,this.paused=!1,this.inLoop=!1,this.inForeignNode=!1,this.lastStartTagName="",this.active=!1,this.state=o.DATA,this.returnState=o.DATA,this.entityStartPos=0,this.consumedAfterSnapshot=-1,this.currentCharacterToken=null,this.currentToken=null,this.currentAttr={name:"",value:""},this.preprocessor=new At(t),this.currentLocation=this.getCurrentLocation(-1),this.entityDecoder=new Nt(_t,(a,s)=>{this.preprocessor.pos=this.entityStartPos+s-1,this._flushCodePointConsumedAsCharacterReference(a)},t.onParseError?{missingSemicolonAfterCharacterReference:()=>{this._err(m.missingSemicolonAfterCharacterReference,1)},absenceOfDigitsInNumericCharacterReference:a=>{this._err(m.absenceOfDigitsInNumericCharacterReference,this.entityStartPos-this.preprocessor.pos+a)},validateNumericCharacterReference:a=>{let s=wc(a);s&&this._err(s,1)}}:void 0)}_err(e,t=0){var a,s;(s=(a=this.handler).onParseError)===null||s===void 0||s.call(a,this.preprocessor.getError(e,t))}getCurrentLocation(e){return this.options.sourceCodeLocationInfo?{startLine:this.preprocessor.line,startCol:this.preprocessor.col-e,startOffset:this.preprocessor.offset-e,endLine:-1,endCol:-1,endOffset:-1}:null}_runParsingLoop(){if(!this.inLoop){for(this.inLoop=!0;this.active&&!this.paused;){this.consumedAfterSnapshot=0;let e=this._consume();this._ensureHibernation()||this._callState(e)}this.inLoop=!1}}pause(){this.paused=!0}resume(e){if(!this.paused)throw new Error("Parser was already resumed");this.paused=!1,!this.inLoop&&(this._runParsingLoop(),this.paused||e==null||e())}write(e,t,a){this.active=!0,this.preprocessor.write(e,t),this._runParsingLoop(),this.paused||a==null||a()}insertHtmlAtCurrentPos(e){this.active=!0,this.preprocessor.insertHtmlAtCurrentPos(e),this._runParsingLoop()}_ensureHibernation(){return this.preprocessor.endOfChunkHit?(this.preprocessor.retreat(this.consumedAfterSnapshot),this.consumedAfterSnapshot=0,this.active=!1,!0):!1}_consume(){return this.consumedAfterSnapshot++,this.preprocessor.advance()}_advanceBy(e){this.consumedAfterSnapshot+=e;for(let t=0;t<e;t++)this.preprocessor.advance()}_consumeSequenceIfMatch(e,t){return this.preprocessor.startsWith(e,t)?(this._advanceBy(e.length-1),!0):!1}_createStartTagToken(){this.currentToken={type:L.START_TAG,tagName:"",tagID:r.UNKNOWN,selfClosing:!1,ackSelfClosing:!1,attrs:[],location:this.getCurrentLocation(1)}}_createEndTagToken(){this.currentToken={type:L.END_TAG,tagName:"",tagID:r.UNKNOWN,selfClosing:!1,ackSelfClosing:!1,attrs:[],location:this.getCurrentLocation(2)}}_createCommentToken(e){this.currentToken={type:L.COMMENT,data:"",location:this.getCurrentLocation(e)}}_createDoctypeToken(e){this.currentToken={type:L.DOCTYPE,name:e,forceQuirks:!1,publicId:null,systemId:null,location:this.currentLocation}}_createCharacterToken(e,t){this.currentCharacterToken={type:e,chars:t,location:this.currentLocation}}_createAttr(e){this.currentAttr={name:e,value:""},this.currentLocation=this.getCurrentLocation(0)}_leaveAttrName(){var e,t;let a=this.currentToken;if(Ie(a,this.currentAttr.name)===null){if(a.attrs.push(this.currentAttr),a.location&&this.currentLocation){let s=(e=(t=a.location).attrs)!==null&&e!==void 0?e:t.attrs=Object.create(null);s[this.currentAttr.name]=this.currentLocation,this._leaveAttrValue()}}else this._err(m.duplicateAttribute)}_leaveAttrValue(){this.currentLocation&&(this.currentLocation.endLine=this.preprocessor.line,this.currentLocation.endCol=this.preprocessor.col,this.currentLocation.endOffset=this.preprocessor.offset)}prepareToken(e){this._emitCurrentCharacterToken(e.location),this.currentToken=null,e.location&&(e.location.endLine=this.preprocessor.line,e.location.endCol=this.preprocessor.col+1,e.location.endOffset=this.preprocessor.offset+1),this.currentLocation=this.getCurrentLocation(-1)}emitCurrentTagToken(){let e=this.currentToken;this.prepareToken(e),e.tagID=yu(e.tagName),e.type===L.START_TAG?(this.lastStartTagName=e.tagName,this.handler.onStartTag(e)):(e.attrs.length>0&&this._err(m.endTagWithAttributes),e.selfClosing&&this._err(m.endTagWithTrailingSolidus),this.handler.onEndTag(e)),this.preprocessor.dropParsedChunk()}emitCurrentComment(e){this.prepareToken(e),this.handler.onComment(e),this.preprocessor.dropParsedChunk()}emitCurrentDoctype(e){this.prepareToken(e),this.handler.onDoctype(e),this.preprocessor.dropParsedChunk()}_emitCurrentCharacterToken(e){if(this.currentCharacterToken){switch(e&&this.currentCharacterToken.location&&(this.currentCharacterToken.location.endLine=e.startLine,this.currentCharacterToken.location.endCol=e.startCol,this.currentCharacterToken.location.endOffset=e.startOffset),this.currentCharacterToken.type){case L.CHARACTER:{this.handler.onCharacter(this.currentCharacterToken);break}case L.NULL_CHARACTER:{this.handler.onNullCharacter(this.currentCharacterToken);break}case L.WHITESPACE_CHARACTER:{this.handler.onWhitespaceCharacter(this.currentCharacterToken);break}}this.currentCharacterToken=null}}_emitEOFToken(){let e=this.getCurrentLocation(0);e&&(e.endLine=e.startLine,e.endCol=e.startCol,e.endOffset=e.startOffset),this._emitCurrentCharacterToken(e),this.handler.onEof({type:L.EOF,location:e}),this.active=!1}_appendCharToCurrentCharacterToken(e,t){if(this.currentCharacterToken)if(this.currentCharacterToken.type===e){this.currentCharacterToken.chars+=t;return}else this.currentLocation=this.getCurrentLocation(0),this._emitCurrentCharacterToken(this.currentLocation),this.preprocessor.dropParsedChunk();this._createCharacterToken(e,t)}_emitCodePoint(e){let t=kr(e)?L.WHITESPACE_CHARACTER:e===c.NULL?L.NULL_CHARACTER:L.CHARACTER;this._appendCharToCurrentCharacterToken(t,String.fromCodePoint(e))}_emitChars(e){this._appendCharToCurrentCharacterToken(L.CHARACTER,e)}_startCharacterReference(){this.returnState=this.state,this.state=o.CHARACTER_REFERENCE,this.entityStartPos=this.preprocessor.pos,this.entityDecoder.startEntity(this._isCharacterReferenceInAttribute()?du.Attribute:du.Legacy)}_isCharacterReferenceInAttribute(){return this.returnState===o.ATTRIBUTE_VALUE_DOUBLE_QUOTED||this.returnState===o.ATTRIBUTE_VALUE_SINGLE_QUOTED||this.returnState===o.ATTRIBUTE_VALUE_UNQUOTED}_flushCodePointConsumedAsCharacterReference(e){this._isCharacterReferenceInAttribute()?this.currentAttr.value+=String.fromCodePoint(e):this._emitCodePoint(e)}_callState(e){switch(this.state){case o.DATA:{this._stateData(e);break}case o.RCDATA:{this._stateRcdata(e);break}case o.RAWTEXT:{this._stateRawtext(e);break}case o.SCRIPT_DATA:{this._stateScriptData(e);break}case o.PLAINTEXT:{this._statePlaintext(e);break}case o.TAG_OPEN:{this._stateTagOpen(e);break}case o.END_TAG_OPEN:{this._stateEndTagOpen(e);break}case o.TAG_NAME:{this._stateTagName(e);break}case o.RCDATA_LESS_THAN_SIGN:{this._stateRcdataLessThanSign(e);break}case o.RCDATA_END_TAG_OPEN:{this._stateRcdataEndTagOpen(e);break}case o.RCDATA_END_TAG_NAME:{this._stateRcdataEndTagName(e);break}case o.RAWTEXT_LESS_THAN_SIGN:{this._stateRawtextLessThanSign(e);break}case o.RAWTEXT_END_TAG_OPEN:{this._stateRawtextEndTagOpen(e);break}case o.RAWTEXT_END_TAG_NAME:{this._stateRawtextEndTagName(e);break}case o.SCRIPT_DATA_LESS_THAN_SIGN:{this._stateScriptDataLessThanSign(e);break}case o.SCRIPT_DATA_END_TAG_OPEN:{this._stateScriptDataEndTagOpen(e);break}case o.SCRIPT_DATA_END_TAG_NAME:{this._stateScriptDataEndTagName(e);break}case o.SCRIPT_DATA_ESCAPE_START:{this._stateScriptDataEscapeStart(e);break}case o.SCRIPT_DATA_ESCAPE_START_DASH:{this._stateScriptDataEscapeStartDash(e);break}case o.SCRIPT_DATA_ESCAPED:{this._stateScriptDataEscaped(e);break}case o.SCRIPT_DATA_ESCAPED_DASH:{this._stateScriptDataEscapedDash(e);break}case o.SCRIPT_DATA_ESCAPED_DASH_DASH:{this._stateScriptDataEscapedDashDash(e);break}case o.SCRIPT_DATA_ESCAPED_LESS_THAN_SIGN:{this._stateScriptDataEscapedLessThanSign(e);break}case o.SCRIPT_DATA_ESCAPED_END_TAG_OPEN:{this._stateScriptDataEscapedEndTagOpen(e);break}case o.SCRIPT_DATA_ESCAPED_END_TAG_NAME:{this._stateScriptDataEscapedEndTagName(e);break}case o.SCRIPT_DATA_DOUBLE_ESCAPE_START:{this._stateScriptDataDoubleEscapeStart(e);break}case o.SCRIPT_DATA_DOUBLE_ESCAPED:{this._stateScriptDataDoubleEscaped(e);break}case o.SCRIPT_DATA_DOUBLE_ESCAPED_DASH:{this._stateScriptDataDoubleEscapedDash(e);break}case o.SCRIPT_DATA_DOUBLE_ESCAPED_DASH_DASH:{this._stateScriptDataDoubleEscapedDashDash(e);break}case o.SCRIPT_DATA_DOUBLE_ESCAPED_LESS_THAN_SIGN:{this._stateScriptDataDoubleEscapedLessThanSign(e);break}case o.SCRIPT_DATA_DOUBLE_ESCAPE_END:{this._stateScriptDataDoubleEscapeEnd(e);break}case o.BEFORE_ATTRIBUTE_NAME:{this._stateBeforeAttributeName(e);break}case o.ATTRIBUTE_NAME:{this._stateAttributeName(e);break}case o.AFTER_ATTRIBUTE_NAME:{this._stateAfterAttributeName(e);break}case o.BEFORE_ATTRIBUTE_VALUE:{this._stateBeforeAttributeValue(e);break}case o.ATTRIBUTE_VALUE_DOUBLE_QUOTED:{this._stateAttributeValueDoubleQuoted(e);break}case o.ATTRIBUTE_VALUE_SINGLE_QUOTED:{this._stateAttributeValueSingleQuoted(e);break}case o.ATTRIBUTE_VALUE_UNQUOTED:{this._stateAttributeValueUnquoted(e);break}case o.AFTER_ATTRIBUTE_VALUE_QUOTED:{this._stateAfterAttributeValueQuoted(e);break}case o.SELF_CLOSING_START_TAG:{this._stateSelfClosingStartTag(e);break}case o.BOGUS_COMMENT:{this._stateBogusComment(e);break}case o.MARKUP_DECLARATION_OPEN:{this._stateMarkupDeclarationOpen(e);break}case o.COMMENT_START:{this._stateCommentStart(e);break}case o.COMMENT_START_DASH:{this._stateCommentStartDash(e);break}case o.COMMENT:{this._stateComment(e);break}case o.COMMENT_LESS_THAN_SIGN:{this._stateCommentLessThanSign(e);break}case o.COMMENT_LESS_THAN_SIGN_BANG:{this._stateCommentLessThanSignBang(e);break}case o.COMMENT_LESS_THAN_SIGN_BANG_DASH:{this._stateCommentLessThanSignBangDash(e);break}case o.COMMENT_LESS_THAN_SIGN_BANG_DASH_DASH:{this._stateCommentLessThanSignBangDashDash(e);break}case o.COMMENT_END_DASH:{this._stateCommentEndDash(e);break}case o.COMMENT_END:{this._stateCommentEnd(e);break}case o.COMMENT_END_BANG:{this._stateCommentEndBang(e);break}case o.DOCTYPE:{this._stateDoctype(e);break}case o.BEFORE_DOCTYPE_NAME:{this._stateBeforeDoctypeName(e);break}case o.DOCTYPE_NAME:{this._stateDoctypeName(e);break}case o.AFTER_DOCTYPE_NAME:{this._stateAfterDoctypeName(e);break}case o.AFTER_DOCTYPE_PUBLIC_KEYWORD:{this._stateAfterDoctypePublicKeyword(e);break}case o.BEFORE_DOCTYPE_PUBLIC_IDENTIFIER:{this._stateBeforeDoctypePublicIdentifier(e);break}case o.DOCTYPE_PUBLIC_IDENTIFIER_DOUBLE_QUOTED:{this._stateDoctypePublicIdentifierDoubleQuoted(e);break}case o.DOCTYPE_PUBLIC_IDENTIFIER_SINGLE_QUOTED:{this._stateDoctypePublicIdentifierSingleQuoted(e);break}case o.AFTER_DOCTYPE_PUBLIC_IDENTIFIER:{this._stateAfterDoctypePublicIdentifier(e);break}case o.BETWEEN_DOCTYPE_PUBLIC_AND_SYSTEM_IDENTIFIERS:{this._stateBetweenDoctypePublicAndSystemIdentifiers(e);break}case o.AFTER_DOCTYPE_SYSTEM_KEYWORD:{this._stateAfterDoctypeSystemKeyword(e);break}case o.BEFORE_DOCTYPE_SYSTEM_IDENTIFIER:{this._stateBeforeDoctypeSystemIdentifier(e);break}case o.DOCTYPE_SYSTEM_IDENTIFIER_DOUBLE_QUOTED:{this._stateDoctypeSystemIdentifierDoubleQuoted(e);break}case o.DOCTYPE_SYSTEM_IDENTIFIER_SINGLE_QUOTED:{this._stateDoctypeSystemIdentifierSingleQuoted(e);break}case o.AFTER_DOCTYPE_SYSTEM_IDENTIFIER:{this._stateAfterDoctypeSystemIdentifier(e);break}case o.BOGUS_DOCTYPE:{this._stateBogusDoctype(e);break}case o.CDATA_SECTION:{this._stateCdataSection(e);break}case o.CDATA_SECTION_BRACKET:{this._stateCdataSectionBracket(e);break}case o.CDATA_SECTION_END:{this._stateCdataSectionEnd(e);break}case o.CHARACTER_REFERENCE:{this._stateCharacterReference();break}case o.AMBIGUOUS_AMPERSAND:{this._stateAmbiguousAmpersand(e);break}default:throw new Error("Unknown state")}}_stateData(e){switch(e){case c.LESS_THAN_SIGN:{this.state=o.TAG_OPEN;break}case c.AMPERSAND:{this._startCharacterReference();break}case c.NULL:{this._err(m.unexpectedNullCharacter),this._emitCodePoint(e);break}case c.EOF:{this._emitEOFToken();break}default:this._emitCodePoint(e)}}_stateRcdata(e){switch(e){case c.AMPERSAND:{this._startCharacterReference();break}case c.LESS_THAN_SIGN:{this.state=o.RCDATA_LESS_THAN_SIGN;break}case c.NULL:{this._err(m.unexpectedNullCharacter),this._emitChars(M);break}case c.EOF:{this._emitEOFToken();break}default:this._emitCodePoint(e)}}_stateRawtext(e){switch(e){case c.LESS_THAN_SIGN:{this.state=o.RAWTEXT_LESS_THAN_SIGN;break}case c.NULL:{this._err(m.unexpectedNullCharacter),this._emitChars(M);break}case c.EOF:{this._emitEOFToken();break}default:this._emitCodePoint(e)}}_stateScriptData(e){switch(e){case c.LESS_THAN_SIGN:{this.state=o.SCRIPT_DATA_LESS_THAN_SIGN;break}case c.NULL:{this._err(m.unexpectedNullCharacter),this._emitChars(M);break}case c.EOF:{this._emitEOFToken();break}default:this._emitCodePoint(e)}}_statePlaintext(e){switch(e){case c.NULL:{this._err(m.unexpectedNullCharacter),this._emitChars(M);break}case c.EOF:{this._emitEOFToken();break}default:this._emitCodePoint(e)}}_stateTagOpen(e){if(Pu(e))this._createStartTagToken(),this.state=o.TAG_NAME,this._stateTagName(e);else switch(e){case c.EXCLAMATION_MARK:{this.state=o.MARKUP_DECLARATION_OPEN;break}case c.SOLIDUS:{this.state=o.END_TAG_OPEN;break}case c.QUESTION_MARK:{this._err(m.unexpectedQuestionMarkInsteadOfTagName),this._createCommentToken(1),this.state=o.BOGUS_COMMENT,this._stateBogusComment(e);break}case c.EOF:{this._err(m.eofBeforeTagName),this._emitChars("<"),this._emitEOFToken();break}default:this._err(m.invalidFirstCharacterOfTagName),this._emitChars("<"),this.state=o.DATA,this._stateData(e)}}_stateEndTagOpen(e){if(Pu(e))this._createEndTagToken(),this.state=o.TAG_NAME,this._stateTagName(e);else switch(e){case c.GREATER_THAN_SIGN:{this._err(m.missingEndTagName),this.state=o.DATA;break}case c.EOF:{this._err(m.eofBeforeTagName),this._emitChars("</"),this._emitEOFToken();break}default:this._err(m.invalidFirstCharacterOfTagName),this._createCommentToken(2),this.state=o.BOGUS_COMMENT,this._stateBogusComment(e)}}_stateTagName(e){let t=this.currentToken;switch(e){case c.SPACE:case c.LINE_FEED:case c.TABULATION:case c.FORM_FEED:{this.state=o.BEFORE_ATTRIBUTE_NAME;break}case c.SOLIDUS:{this.state=o.SELF_CLOSING_START_TAG;break}case c.GREATER_THAN_SIGN:{this.state=o.DATA,this.emitCurrentTagToken();break}case c.NULL:{this._err(m.unexpectedNullCharacter),t.tagName+=M;break}case c.EOF:{this._err(m.eofInTag),this._emitEOFToken();break}default:t.tagName+=String.fromCodePoint(Le(e)?St(e):e)}}_stateRcdataLessThanSign(e){e===c.SOLIDUS?this.state=o.RCDATA_END_TAG_OPEN:(this._emitChars("<"),this.state=o.RCDATA,this._stateRcdata(e))}_stateRcdataEndTagOpen(e){Pu(e)?(this.state=o.RCDATA_END_TAG_NAME,this._stateRcdataEndTagName(e)):(this._emitChars("</"),this.state=o.RCDATA,this._stateRcdata(e))}handleSpecialEndTag(e){if(!this.preprocessor.startsWith(this.lastStartTagName,!1))return!this._ensureHibernation();this._createEndTagToken();let t=this.currentToken;switch(t.tagName=this.lastStartTagName,this.preprocessor.peek(this.lastStartTagName.length)){case c.SPACE:case c.LINE_FEED:case c.TABULATION:case c.FORM_FEED:return this._advanceBy(this.lastStartTagName.length),this.state=o.BEFORE_ATTRIBUTE_NAME,!1;case c.SOLIDUS:return this._advanceBy(this.lastStartTagName.length),this.state=o.SELF_CLOSING_START_TAG,!1;case c.GREATER_THAN_SIGN:return this._advanceBy(this.lastStartTagName.length),this.emitCurrentTagToken(),this.state=o.DATA,!1;default:return!this._ensureHibernation()}}_stateRcdataEndTagName(e){this.handleSpecialEndTag(e)&&(this._emitChars("</"),this.state=o.RCDATA,this._stateRcdata(e))}_stateRawtextLessThanSign(e){e===c.SOLIDUS?this.state=o.RAWTEXT_END_TAG_OPEN:(this._emitChars("<"),this.state=o.RAWTEXT,this._stateRawtext(e))}_stateRawtextEndTagOpen(e){Pu(e)?(this.state=o.RAWTEXT_END_TAG_NAME,this._stateRawtextEndTagName(e)):(this._emitChars("</"),this.state=o.RAWTEXT,this._stateRawtext(e))}_stateRawtextEndTagName(e){this.handleSpecialEndTag(e)&&(this._emitChars("</"),this.state=o.RAWTEXT,this._stateRawtext(e))}_stateScriptDataLessThanSign(e){switch(e){case c.SOLIDUS:{this.state=o.SCRIPT_DATA_END_TAG_OPEN;break}case c.EXCLAMATION_MARK:{this.state=o.SCRIPT_DATA_ESCAPE_START,this._emitChars("<!");break}default:this._emitChars("<"),this.state=o.SCRIPT_DATA,this._stateScriptData(e)}}_stateScriptDataEndTagOpen(e){Pu(e)?(this.state=o.SCRIPT_DATA_END_TAG_NAME,this._stateScriptDataEndTagName(e)):(this._emitChars("</"),this.state=o.SCRIPT_DATA,this._stateScriptData(e))}_stateScriptDataEndTagName(e){this.handleSpecialEndTag(e)&&(this._emitChars("</"),this.state=o.SCRIPT_DATA,this._stateScriptData(e))}_stateScriptDataEscapeStart(e){e===c.HYPHEN_MINUS?(this.state=o.SCRIPT_DATA_ESCAPE_START_DASH,this._emitChars("-")):(this.state=o.SCRIPT_DATA,this._stateScriptData(e))}_stateScriptDataEscapeStartDash(e){e===c.HYPHEN_MINUS?(this.state=o.SCRIPT_DATA_ESCAPED_DASH_DASH,this._emitChars("-")):(this.state=o.SCRIPT_DATA,this._stateScriptData(e))}_stateScriptDataEscaped(e){switch(e){case c.HYPHEN_MINUS:{this.state=o.SCRIPT_DATA_ESCAPED_DASH,this._emitChars("-");break}case c.LESS_THAN_SIGN:{this.state=o.SCRIPT_DATA_ESCAPED_LESS_THAN_SIGN;break}case c.NULL:{this._err(m.unexpectedNullCharacter),this._emitChars(M);break}case c.EOF:{this._err(m.eofInScriptHtmlCommentLikeText),this._emitEOFToken();break}default:this._emitCodePoint(e)}}_stateScriptDataEscapedDash(e){switch(e){case c.HYPHEN_MINUS:{this.state=o.SCRIPT_DATA_ESCAPED_DASH_DASH,this._emitChars("-");break}case c.LESS_THAN_SIGN:{this.state=o.SCRIPT_DATA_ESCAPED_LESS_THAN_SIGN;break}case c.NULL:{this._err(m.unexpectedNullCharacter),this.state=o.SCRIPT_DATA_ESCAPED,this._emitChars(M);break}case c.EOF:{this._err(m.eofInScriptHtmlCommentLikeText),this._emitEOFToken();break}default:this.state=o.SCRIPT_DATA_ESCAPED,this._emitCodePoint(e)}}_stateScriptDataEscapedDashDash(e){switch(e){case c.HYPHEN_MINUS:{this._emitChars("-");break}case c.LESS_THAN_SIGN:{this.state=o.SCRIPT_DATA_ESCAPED_LESS_THAN_SIGN;break}case c.GREATER_THAN_SIGN:{this.state=o.SCRIPT_DATA,this._emitChars(">");break}case c.NULL:{this._err(m.unexpectedNullCharacter),this.state=o.SCRIPT_DATA_ESCAPED,this._emitChars(M);break}case c.EOF:{this._err(m.eofInScriptHtmlCommentLikeText),this._emitEOFToken();break}default:this.state=o.SCRIPT_DATA_ESCAPED,this._emitCodePoint(e)}}_stateScriptDataEscapedLessThanSign(e){e===c.SOLIDUS?this.state=o.SCRIPT_DATA_ESCAPED_END_TAG_OPEN:Pu(e)?(this._emitChars("<"),this.state=o.SCRIPT_DATA_DOUBLE_ESCAPE_START,this._stateScriptDataDoubleEscapeStart(e)):(this._emitChars("<"),this.state=o.SCRIPT_DATA_ESCAPED,this._stateScriptDataEscaped(e))}_stateScriptDataEscapedEndTagOpen(e){Pu(e)?(this.state=o.SCRIPT_DATA_ESCAPED_END_TAG_NAME,this._stateScriptDataEscapedEndTagName(e)):(this._emitChars("</"),this.state=o.SCRIPT_DATA_ESCAPED,this._stateScriptDataEscaped(e))}_stateScriptDataEscapedEndTagName(e){this.handleSpecialEndTag(e)&&(this._emitChars("</"),this.state=o.SCRIPT_DATA_ESCAPED,this._stateScriptDataEscaped(e))}_stateScriptDataDoubleEscapeStart(e){if(this.preprocessor.startsWith(X.SCRIPT,!1)&&Mr(this.preprocessor.peek(X.SCRIPT.length))){this._emitCodePoint(e);for(let t=0;t<X.SCRIPT.length;t++)this._emitCodePoint(this._consume());this.state=o.SCRIPT_DATA_DOUBLE_ESCAPED}else this._ensureHibernation()||(this.state=o.SCRIPT_DATA_ESCAPED,this._stateScriptDataEscaped(e))}_stateScriptDataDoubleEscaped(e){switch(e){case c.HYPHEN_MINUS:{this.state=o.SCRIPT_DATA_DOUBLE_ESCAPED_DASH,this._emitChars("-");break}case c.LESS_THAN_SIGN:{this.state=o.SCRIPT_DATA_DOUBLE_ESCAPED_LESS_THAN_SIGN,this._emitChars("<");break}case c.NULL:{this._err(m.unexpectedNullCharacter),this._emitChars(M);break}case c.EOF:{this._err(m.eofInScriptHtmlCommentLikeText),this._emitEOFToken();break}default:this._emitCodePoint(e)}}_stateScriptDataDoubleEscapedDash(e){switch(e){case c.HYPHEN_MINUS:{this.state=o.SCRIPT_DATA_DOUBLE_ESCAPED_DASH_DASH,this._emitChars("-");break}case c.LESS_THAN_SIGN:{this.state=o.SCRIPT_DATA_DOUBLE_ESCAPED_LESS_THAN_SIGN,this._emitChars("<");break}case c.NULL:{this._err(m.unexpectedNullCharacter),this.state=o.SCRIPT_DATA_DOUBLE_ESCAPED,this._emitChars(M);break}case c.EOF:{this._err(m.eofInScriptHtmlCommentLikeText),this._emitEOFToken();break}default:this.state=o.SCRIPT_DATA_DOUBLE_ESCAPED,this._emitCodePoint(e)}}_stateScriptDataDoubleEscapedDashDash(e){switch(e){case c.HYPHEN_MINUS:{this._emitChars("-");break}case c.LESS_THAN_SIGN:{this.state=o.SCRIPT_DATA_DOUBLE_ESCAPED_LESS_THAN_SIGN,this._emitChars("<");break}case c.GREATER_THAN_SIGN:{this.state=o.SCRIPT_DATA,this._emitChars(">");break}case c.NULL:{this._err(m.unexpectedNullCharacter),this.state=o.SCRIPT_DATA_DOUBLE_ESCAPED,this._emitChars(M);break}case c.EOF:{this._err(m.eofInScriptHtmlCommentLikeText),this._emitEOFToken();break}default:this.state=o.SCRIPT_DATA_DOUBLE_ESCAPED,this._emitCodePoint(e)}}_stateScriptDataDoubleEscapedLessThanSign(e){e===c.SOLIDUS?(this.state=o.SCRIPT_DATA_DOUBLE_ESCAPE_END,this._emitChars("/")):(this.state=o.SCRIPT_DATA_DOUBLE_ESCAPED,this._stateScriptDataDoubleEscaped(e))}_stateScriptDataDoubleEscapeEnd(e){if(this.preprocessor.startsWith(X.SCRIPT,!1)&&Mr(this.preprocessor.peek(X.SCRIPT.length))){this._emitCodePoint(e);for(let t=0;t<X.SCRIPT.length;t++)this._emitCodePoint(this._consume());this.state=o.SCRIPT_DATA_ESCAPED}else this._ensureHibernation()||(this.state=o.SCRIPT_DATA_DOUBLE_ESCAPED,this._stateScriptDataDoubleEscaped(e))}_stateBeforeAttributeName(e){switch(e){case c.SPACE:case c.LINE_FEED:case c.TABULATION:case c.FORM_FEED:break;case c.SOLIDUS:case c.GREATER_THAN_SIGN:case c.EOF:{this.state=o.AFTER_ATTRIBUTE_NAME,this._stateAfterAttributeName(e);break}case c.EQUALS_SIGN:{this._err(m.unexpectedEqualsSignBeforeAttributeName),this._createAttr("="),this.state=o.ATTRIBUTE_NAME;break}default:this._createAttr(""),this.state=o.ATTRIBUTE_NAME,this._stateAttributeName(e)}}_stateAttributeName(e){switch(e){case c.SPACE:case c.LINE_FEED:case c.TABULATION:case c.FORM_FEED:case c.SOLIDUS:case c.GREATER_THAN_SIGN:case c.EOF:{this._leaveAttrName(),this.state=o.AFTER_ATTRIBUTE_NAME,this._stateAfterAttributeName(e);break}case c.EQUALS_SIGN:{this._leaveAttrName(),this.state=o.BEFORE_ATTRIBUTE_VALUE;break}case c.QUOTATION_MARK:case c.APOSTROPHE:case c.LESS_THAN_SIGN:{this._err(m.unexpectedCharacterInAttributeName),this.currentAttr.name+=String.fromCodePoint(e);break}case c.NULL:{this._err(m.unexpectedNullCharacter),this.currentAttr.name+=M;break}default:this.currentAttr.name+=String.fromCodePoint(Le(e)?St(e):e)}}_stateAfterAttributeName(e){switch(e){case c.SPACE:case c.LINE_FEED:case c.TABULATION:case c.FORM_FEED:break;case c.SOLIDUS:{this.state=o.SELF_CLOSING_START_TAG;break}case c.EQUALS_SIGN:{this.state=o.BEFORE_ATTRIBUTE_VALUE;break}case c.GREATER_THAN_SIGN:{this.state=o.DATA,this.emitCurrentTagToken();break}case c.EOF:{this._err(m.eofInTag),this._emitEOFToken();break}default:this._createAttr(""),this.state=o.ATTRIBUTE_NAME,this._stateAttributeName(e)}}_stateBeforeAttributeValue(e){switch(e){case c.SPACE:case c.LINE_FEED:case c.TABULATION:case c.FORM_FEED:break;case c.QUOTATION_MARK:{this.state=o.ATTRIBUTE_VALUE_DOUBLE_QUOTED;break}case c.APOSTROPHE:{this.state=o.ATTRIBUTE_VALUE_SINGLE_QUOTED;break}case c.GREATER_THAN_SIGN:{this._err(m.missingAttributeValue),this.state=o.DATA,this.emitCurrentTagToken();break}default:this.state=o.ATTRIBUTE_VALUE_UNQUOTED,this._stateAttributeValueUnquoted(e)}}_stateAttributeValueDoubleQuoted(e){switch(e){case c.QUOTATION_MARK:{this.state=o.AFTER_ATTRIBUTE_VALUE_QUOTED;break}case c.AMPERSAND:{this._startCharacterReference();break}case c.NULL:{this._err(m.unexpectedNullCharacter),this.currentAttr.value+=M;break}case c.EOF:{this._err(m.eofInTag),this._emitEOFToken();break}default:this.currentAttr.value+=String.fromCodePoint(e)}}_stateAttributeValueSingleQuoted(e){switch(e){case c.APOSTROPHE:{this.state=o.AFTER_ATTRIBUTE_VALUE_QUOTED;break}case c.AMPERSAND:{this._startCharacterReference();break}case c.NULL:{this._err(m.unexpectedNullCharacter),this.currentAttr.value+=M;break}case c.EOF:{this._err(m.eofInTag),this._emitEOFToken();break}default:this.currentAttr.value+=String.fromCodePoint(e)}}_stateAttributeValueUnquoted(e){switch(e){case c.SPACE:case c.LINE_FEED:case c.TABULATION:case c.FORM_FEED:{this._leaveAttrValue(),this.state=o.BEFORE_ATTRIBUTE_NAME;break}case c.AMPERSAND:{this._startCharacterReference();break}case c.GREATER_THAN_SIGN:{this._leaveAttrValue(),this.state=o.DATA,this.emitCurrentTagToken();break}case c.NULL:{this._err(m.unexpectedNullCharacter),this.currentAttr.value+=M;break}case c.QUOTATION_MARK:case c.APOSTROPHE:case c.LESS_THAN_SIGN:case c.EQUALS_SIGN:case c.GRAVE_ACCENT:{this._err(m.unexpectedCharacterInUnquotedAttributeValue),this.currentAttr.value+=String.fromCodePoint(e);break}case c.EOF:{this._err(m.eofInTag),this._emitEOFToken();break}default:this.currentAttr.value+=String.fromCodePoint(e)}}_stateAfterAttributeValueQuoted(e){switch(e){case c.SPACE:case c.LINE_FEED:case c.TABULATION:case c.FORM_FEED:{this._leaveAttrValue(),this.state=o.BEFORE_ATTRIBUTE_NAME;break}case c.SOLIDUS:{this._leaveAttrValue(),this.state=o.SELF_CLOSING_START_TAG;break}case c.GREATER_THAN_SIGN:{this._leaveAttrValue(),this.state=o.DATA,this.emitCurrentTagToken();break}case c.EOF:{this._err(m.eofInTag),this._emitEOFToken();break}default:this._err(m.missingWhitespaceBetweenAttributes),this.state=o.BEFORE_ATTRIBUTE_NAME,this._stateBeforeAttributeName(e)}}_stateSelfClosingStartTag(e){switch(e){case c.GREATER_THAN_SIGN:{let t=this.currentToken;t.selfClosing=!0,this.state=o.DATA,this.emitCurrentTagToken();break}case c.EOF:{this._err(m.eofInTag),this._emitEOFToken();break}default:this._err(m.unexpectedSolidusInTag),this.state=o.BEFORE_ATTRIBUTE_NAME,this._stateBeforeAttributeName(e)}}_stateBogusComment(e){let t=this.currentToken;switch(e){case c.GREATER_THAN_SIGN:{this.state=o.DATA,this.emitCurrentComment(t);break}case c.EOF:{this.emitCurrentComment(t),this._emitEOFToken();break}case c.NULL:{this._err(m.unexpectedNullCharacter),t.data+=M;break}default:t.data+=String.fromCodePoint(e)}}_stateMarkupDeclarationOpen(e){this._consumeSequenceIfMatch(X.DASH_DASH,!0)?(this._createCommentToken(X.DASH_DASH.length+1),this.state=o.COMMENT_START):this._consumeSequenceIfMatch(X.DOCTYPE,!1)?(this.currentLocation=this.getCurrentLocation(X.DOCTYPE.length+1),this.state=o.DOCTYPE):this._consumeSequenceIfMatch(X.CDATA_START,!0)?this.inForeignNode?this.state=o.CDATA_SECTION:(this._err(m.cdataInHtmlContent),this._createCommentToken(X.CDATA_START.length+1),this.currentToken.data="[CDATA[",this.state=o.BOGUS_COMMENT):this._ensureHibernation()||(this._err(m.incorrectlyOpenedComment),this._createCommentToken(2),this.state=o.BOGUS_COMMENT,this._stateBogusComment(e))}_stateCommentStart(e){switch(e){case c.HYPHEN_MINUS:{this.state=o.COMMENT_START_DASH;break}case c.GREATER_THAN_SIGN:{this._err(m.abruptClosingOfEmptyComment),this.state=o.DATA;let t=this.currentToken;this.emitCurrentComment(t);break}default:this.state=o.COMMENT,this._stateComment(e)}}_stateCommentStartDash(e){let t=this.currentToken;switch(e){case c.HYPHEN_MINUS:{this.state=o.COMMENT_END;break}case c.GREATER_THAN_SIGN:{this._err(m.abruptClosingOfEmptyComment),this.state=o.DATA,this.emitCurrentComment(t);break}case c.EOF:{this._err(m.eofInComment),this.emitCurrentComment(t),this._emitEOFToken();break}default:t.data+="-",this.state=o.COMMENT,this._stateComment(e)}}_stateComment(e){let t=this.currentToken;switch(e){case c.HYPHEN_MINUS:{this.state=o.COMMENT_END_DASH;break}case c.LESS_THAN_SIGN:{t.data+="<",this.state=o.COMMENT_LESS_THAN_SIGN;break}case c.NULL:{this._err(m.unexpectedNullCharacter),t.data+=M;break}case c.EOF:{this._err(m.eofInComment),this.emitCurrentComment(t),this._emitEOFToken();break}default:t.data+=String.fromCodePoint(e)}}_stateCommentLessThanSign(e){let t=this.currentToken;switch(e){case c.EXCLAMATION_MARK:{t.data+="!",this.state=o.COMMENT_LESS_THAN_SIGN_BANG;break}case c.LESS_THAN_SIGN:{t.data+="<";break}default:this.state=o.COMMENT,this._stateComment(e)}}_stateCommentLessThanSignBang(e){e===c.HYPHEN_MINUS?this.state=o.COMMENT_LESS_THAN_SIGN_BANG_DASH:(this.state=o.COMMENT,this._stateComment(e))}_stateCommentLessThanSignBangDash(e){e===c.HYPHEN_MINUS?this.state=o.COMMENT_LESS_THAN_SIGN_BANG_DASH_DASH:(this.state=o.COMMENT_END_DASH,this._stateCommentEndDash(e))}_stateCommentLessThanSignBangDashDash(e){e!==c.GREATER_THAN_SIGN&&e!==c.EOF&&this._err(m.nestedComment),this.state=o.COMMENT_END,this._stateCommentEnd(e)}_stateCommentEndDash(e){let t=this.currentToken;switch(e){case c.HYPHEN_MINUS:{this.state=o.COMMENT_END;break}case c.EOF:{this._err(m.eofInComment),this.emitCurrentComment(t),this._emitEOFToken();break}default:t.data+="-",this.state=o.COMMENT,this._stateComment(e)}}_stateCommentEnd(e){let t=this.currentToken;switch(e){case c.GREATER_THAN_SIGN:{this.state=o.DATA,this.emitCurrentComment(t);break}case c.EXCLAMATION_MARK:{this.state=o.COMMENT_END_BANG;break}case c.HYPHEN_MINUS:{t.data+="-";break}case c.EOF:{this._err(m.eofInComment),this.emitCurrentComment(t),this._emitEOFToken();break}default:t.data+="--",this.state=o.COMMENT,this._stateComment(e)}}_stateCommentEndBang(e){let t=this.currentToken;switch(e){case c.HYPHEN_MINUS:{t.data+="--!",this.state=o.COMMENT_END_DASH;break}case c.GREATER_THAN_SIGN:{this._err(m.incorrectlyClosedComment),this.state=o.DATA,this.emitCurrentComment(t);break}case c.EOF:{this._err(m.eofInComment),this.emitCurrentComment(t),this._emitEOFToken();break}default:t.data+="--!",this.state=o.COMMENT,this._stateComment(e)}}_stateDoctype(e){switch(e){case c.SPACE:case c.LINE_FEED:case c.TABULATION:case c.FORM_FEED:{this.state=o.BEFORE_DOCTYPE_NAME;break}case c.GREATER_THAN_SIGN:{this.state=o.BEFORE_DOCTYPE_NAME,this._stateBeforeDoctypeName(e);break}case c.EOF:{this._err(m.eofInDoctype),this._createDoctypeToken(null);let t=this.currentToken;t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break}default:this._err(m.missingWhitespaceBeforeDoctypeName),this.state=o.BEFORE_DOCTYPE_NAME,this._stateBeforeDoctypeName(e)}}_stateBeforeDoctypeName(e){if(Le(e))this._createDoctypeToken(String.fromCharCode(St(e))),this.state=o.DOCTYPE_NAME;else switch(e){case c.SPACE:case c.LINE_FEED:case c.TABULATION:case c.FORM_FEED:break;case c.NULL:{this._err(m.unexpectedNullCharacter),this._createDoctypeToken(M),this.state=o.DOCTYPE_NAME;break}case c.GREATER_THAN_SIGN:{this._err(m.missingDoctypeName),this._createDoctypeToken(null);let t=this.currentToken;t.forceQuirks=!0,this.emitCurrentDoctype(t),this.state=o.DATA;break}case c.EOF:{this._err(m.eofInDoctype),this._createDoctypeToken(null);let t=this.currentToken;t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break}default:this._createDoctypeToken(String.fromCodePoint(e)),this.state=o.DOCTYPE_NAME}}_stateDoctypeName(e){let t=this.currentToken;switch(e){case c.SPACE:case c.LINE_FEED:case c.TABULATION:case c.FORM_FEED:{this.state=o.AFTER_DOCTYPE_NAME;break}case c.GREATER_THAN_SIGN:{this.state=o.DATA,this.emitCurrentDoctype(t);break}case c.NULL:{this._err(m.unexpectedNullCharacter),t.name+=M;break}case c.EOF:{this._err(m.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break}default:t.name+=String.fromCodePoint(Le(e)?St(e):e)}}_stateAfterDoctypeName(e){let t=this.currentToken;switch(e){case c.SPACE:case c.LINE_FEED:case c.TABULATION:case c.FORM_FEED:break;case c.GREATER_THAN_SIGN:{this.state=o.DATA,this.emitCurrentDoctype(t);break}case c.EOF:{this._err(m.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break}default:this._consumeSequenceIfMatch(X.PUBLIC,!1)?this.state=o.AFTER_DOCTYPE_PUBLIC_KEYWORD:this._consumeSequenceIfMatch(X.SYSTEM,!1)?this.state=o.AFTER_DOCTYPE_SYSTEM_KEYWORD:this._ensureHibernation()||(this._err(m.invalidCharacterSequenceAfterDoctypeName),t.forceQuirks=!0,this.state=o.BOGUS_DOCTYPE,this._stateBogusDoctype(e))}}_stateAfterDoctypePublicKeyword(e){let t=this.currentToken;switch(e){case c.SPACE:case c.LINE_FEED:case c.TABULATION:case c.FORM_FEED:{this.state=o.BEFORE_DOCTYPE_PUBLIC_IDENTIFIER;break}case c.QUOTATION_MARK:{this._err(m.missingWhitespaceAfterDoctypePublicKeyword),t.publicId="",this.state=o.DOCTYPE_PUBLIC_IDENTIFIER_DOUBLE_QUOTED;break}case c.APOSTROPHE:{this._err(m.missingWhitespaceAfterDoctypePublicKeyword),t.publicId="",this.state=o.DOCTYPE_PUBLIC_IDENTIFIER_SINGLE_QUOTED;break}case c.GREATER_THAN_SIGN:{this._err(m.missingDoctypePublicIdentifier),t.forceQuirks=!0,this.state=o.DATA,this.emitCurrentDoctype(t);break}case c.EOF:{this._err(m.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break}default:this._err(m.missingQuoteBeforeDoctypePublicIdentifier),t.forceQuirks=!0,this.state=o.BOGUS_DOCTYPE,this._stateBogusDoctype(e)}}_stateBeforeDoctypePublicIdentifier(e){let t=this.currentToken;switch(e){case c.SPACE:case c.LINE_FEED:case c.TABULATION:case c.FORM_FEED:break;case c.QUOTATION_MARK:{t.publicId="",this.state=o.DOCTYPE_PUBLIC_IDENTIFIER_DOUBLE_QUOTED;break}case c.APOSTROPHE:{t.publicId="",this.state=o.DOCTYPE_PUBLIC_IDENTIFIER_SINGLE_QUOTED;break}case c.GREATER_THAN_SIGN:{this._err(m.missingDoctypePublicIdentifier),t.forceQuirks=!0,this.state=o.DATA,this.emitCurrentDoctype(t);break}case c.EOF:{this._err(m.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break}default:this._err(m.missingQuoteBeforeDoctypePublicIdentifier),t.forceQuirks=!0,this.state=o.BOGUS_DOCTYPE,this._stateBogusDoctype(e)}}_stateDoctypePublicIdentifierDoubleQuoted(e){let t=this.currentToken;switch(e){case c.QUOTATION_MARK:{this.state=o.AFTER_DOCTYPE_PUBLIC_IDENTIFIER;break}case c.NULL:{this._err(m.unexpectedNullCharacter),t.publicId+=M;break}case c.GREATER_THAN_SIGN:{this._err(m.abruptDoctypePublicIdentifier),t.forceQuirks=!0,this.emitCurrentDoctype(t),this.state=o.DATA;break}case c.EOF:{this._err(m.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break}default:t.publicId+=String.fromCodePoint(e)}}_stateDoctypePublicIdentifierSingleQuoted(e){let t=this.currentToken;switch(e){case c.APOSTROPHE:{this.state=o.AFTER_DOCTYPE_PUBLIC_IDENTIFIER;break}case c.NULL:{this._err(m.unexpectedNullCharacter),t.publicId+=M;break}case c.GREATER_THAN_SIGN:{this._err(m.abruptDoctypePublicIdentifier),t.forceQuirks=!0,this.emitCurrentDoctype(t),this.state=o.DATA;break}case c.EOF:{this._err(m.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break}default:t.publicId+=String.fromCodePoint(e)}}_stateAfterDoctypePublicIdentifier(e){let t=this.currentToken;switch(e){case c.SPACE:case c.LINE_FEED:case c.TABULATION:case c.FORM_FEED:{this.state=o.BETWEEN_DOCTYPE_PUBLIC_AND_SYSTEM_IDENTIFIERS;break}case c.GREATER_THAN_SIGN:{this.state=o.DATA,this.emitCurrentDoctype(t);break}case c.QUOTATION_MARK:{this._err(m.missingWhitespaceBetweenDoctypePublicAndSystemIdentifiers),t.systemId="",this.state=o.DOCTYPE_SYSTEM_IDENTIFIER_DOUBLE_QUOTED;break}case c.APOSTROPHE:{this._err(m.missingWhitespaceBetweenDoctypePublicAndSystemIdentifiers),t.systemId="",this.state=o.DOCTYPE_SYSTEM_IDENTIFIER_SINGLE_QUOTED;break}case c.EOF:{this._err(m.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break}default:this._err(m.missingQuoteBeforeDoctypeSystemIdentifier),t.forceQuirks=!0,this.state=o.BOGUS_DOCTYPE,this._stateBogusDoctype(e)}}_stateBetweenDoctypePublicAndSystemIdentifiers(e){let t=this.currentToken;switch(e){case c.SPACE:case c.LINE_FEED:case c.TABULATION:case c.FORM_FEED:break;case c.GREATER_THAN_SIGN:{this.emitCurrentDoctype(t),this.state=o.DATA;break}case c.QUOTATION_MARK:{t.systemId="",this.state=o.DOCTYPE_SYSTEM_IDENTIFIER_DOUBLE_QUOTED;break}case c.APOSTROPHE:{t.systemId="",this.state=o.DOCTYPE_SYSTEM_IDENTIFIER_SINGLE_QUOTED;break}case c.EOF:{this._err(m.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break}default:this._err(m.missingQuoteBeforeDoctypeSystemIdentifier),t.forceQuirks=!0,this.state=o.BOGUS_DOCTYPE,this._stateBogusDoctype(e)}}_stateAfterDoctypeSystemKeyword(e){let t=this.currentToken;switch(e){case c.SPACE:case c.LINE_FEED:case c.TABULATION:case c.FORM_FEED:{this.state=o.BEFORE_DOCTYPE_SYSTEM_IDENTIFIER;break}case c.QUOTATION_MARK:{this._err(m.missingWhitespaceAfterDoctypeSystemKeyword),t.systemId="",this.state=o.DOCTYPE_SYSTEM_IDENTIFIER_DOUBLE_QUOTED;break}case c.APOSTROPHE:{this._err(m.missingWhitespaceAfterDoctypeSystemKeyword),t.systemId="",this.state=o.DOCTYPE_SYSTEM_IDENTIFIER_SINGLE_QUOTED;break}case c.GREATER_THAN_SIGN:{this._err(m.missingDoctypeSystemIdentifier),t.forceQuirks=!0,this.state=o.DATA,this.emitCurrentDoctype(t);break}case c.EOF:{this._err(m.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break}default:this._err(m.missingQuoteBeforeDoctypeSystemIdentifier),t.forceQuirks=!0,this.state=o.BOGUS_DOCTYPE,this._stateBogusDoctype(e)}}_stateBeforeDoctypeSystemIdentifier(e){let t=this.currentToken;switch(e){case c.SPACE:case c.LINE_FEED:case c.TABULATION:case c.FORM_FEED:break;case c.QUOTATION_MARK:{t.systemId="",this.state=o.DOCTYPE_SYSTEM_IDENTIFIER_DOUBLE_QUOTED;break}case c.APOSTROPHE:{t.systemId="",this.state=o.DOCTYPE_SYSTEM_IDENTIFIER_SINGLE_QUOTED;break}case c.GREATER_THAN_SIGN:{this._err(m.missingDoctypeSystemIdentifier),t.forceQuirks=!0,this.state=o.DATA,this.emitCurrentDoctype(t);break}case c.EOF:{this._err(m.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break}default:this._err(m.missingQuoteBeforeDoctypeSystemIdentifier),t.forceQuirks=!0,this.state=o.BOGUS_DOCTYPE,this._stateBogusDoctype(e)}}_stateDoctypeSystemIdentifierDoubleQuoted(e){let t=this.currentToken;switch(e){case c.QUOTATION_MARK:{this.state=o.AFTER_DOCTYPE_SYSTEM_IDENTIFIER;break}case c.NULL:{this._err(m.unexpectedNullCharacter),t.systemId+=M;break}case c.GREATER_THAN_SIGN:{this._err(m.abruptDoctypeSystemIdentifier),t.forceQuirks=!0,this.emitCurrentDoctype(t),this.state=o.DATA;break}case c.EOF:{this._err(m.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break}default:t.systemId+=String.fromCodePoint(e)}}_stateDoctypeSystemIdentifierSingleQuoted(e){let t=this.currentToken;switch(e){case c.APOSTROPHE:{this.state=o.AFTER_DOCTYPE_SYSTEM_IDENTIFIER;break}case c.NULL:{this._err(m.unexpectedNullCharacter),t.systemId+=M;break}case c.GREATER_THAN_SIGN:{this._err(m.abruptDoctypeSystemIdentifier),t.forceQuirks=!0,this.emitCurrentDoctype(t),this.state=o.DATA;break}case c.EOF:{this._err(m.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break}default:t.systemId+=String.fromCodePoint(e)}}_stateAfterDoctypeSystemIdentifier(e){let t=this.currentToken;switch(e){case c.SPACE:case c.LINE_FEED:case c.TABULATION:case c.FORM_FEED:break;case c.GREATER_THAN_SIGN:{this.emitCurrentDoctype(t),this.state=o.DATA;break}case c.EOF:{this._err(m.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break}default:this._err(m.unexpectedCharacterAfterDoctypeSystemIdentifier),this.state=o.BOGUS_DOCTYPE,this._stateBogusDoctype(e)}}_stateBogusDoctype(e){let t=this.currentToken;switch(e){case c.GREATER_THAN_SIGN:{this.emitCurrentDoctype(t),this.state=o.DATA;break}case c.NULL:{this._err(m.unexpectedNullCharacter);break}case c.EOF:{this.emitCurrentDoctype(t),this._emitEOFToken();break}default:}}_stateCdataSection(e){switch(e){case c.RIGHT_SQUARE_BRACKET:{this.state=o.CDATA_SECTION_BRACKET;break}case c.EOF:{this._err(m.eofInCdata),this._emitEOFToken();break}default:this._emitCodePoint(e)}}_stateCdataSectionBracket(e){e===c.RIGHT_SQUARE_BRACKET?this.state=o.CDATA_SECTION_END:(this._emitChars("]"),this.state=o.CDATA_SECTION,this._stateCdataSection(e))}_stateCdataSectionEnd(e){switch(e){case c.GREATER_THAN_SIGN:{this.state=o.DATA;break}case c.RIGHT_SQUARE_BRACKET:{this._emitChars("]");break}default:this._emitChars("]]"),this.state=o.CDATA_SECTION,this._stateCdataSection(e)}}_stateCharacterReference(){let e=this.entityDecoder.write(this.preprocessor.html,this.preprocessor.pos);if(e<0)if(this.preprocessor.lastChunkWritten)e=this.entityDecoder.end();else{this.active=!1,this.preprocessor.pos=this.preprocessor.html.length-1,this.consumedAfterSnapshot=0,this.preprocessor.endOfChunkHit=!0;return}e===0?(this.preprocessor.pos=this.entityStartPos,this._flushCodePointConsumedAsCharacterReference(c.AMPERSAND),this.state=!this._isCharacterReferenceInAttribute()&&Pr(this.preprocessor.peek(1))?o.AMBIGUOUS_AMPERSAND:this.returnState):this.state=this.returnState}_stateAmbiguousAmpersand(e){Pr(e)?this._flushCodePointConsumedAsCharacterReference(e):(e===c.SEMICOLON&&this._err(m.unknownNamedCharacterReference),this.state=this.returnState,this._callState(e))}};var Ur=new Set([r.DD,r.DT,r.LI,r.OPTGROUP,r.OPTION,r.P,r.RB,r.RP,r.RT,r.RTC]),wr=new Set([...Ur,r.CAPTION,r.COLGROUP,r.TBODY,r.TD,r.TFOOT,r.TH,r.THEAD,r.TR]),It=new Set([r.APPLET,r.CAPTION,r.HTML,r.MARQUEE,r.OBJECT,r.TABLE,r.TD,r.TEMPLATE,r.TH]),Bc=new Set([...It,r.OL,r.UL]),vc=new Set([...It,r.BUTTON]),Br=new Set([r.ANNOTATION_XML,r.MI,r.MN,r.MO,r.MS,r.MTEXT]),vr=new Set([r.DESC,r.FOREIGN_OBJECT,r.TITLE]),Uc=new Set([r.TR,r.TEMPLATE,r.HTML]),Hc=new Set([r.TBODY,r.TFOOT,r.THEAD,r.TEMPLATE,r.HTML]),Fc=new Set([r.TABLE,r.TEMPLATE,r.HTML]),qc=new Set([r.TD,r.TH]),Ct=class{get currentTmplContentOrNode(){return this._isInTemplate()?this.treeAdapter.getTemplateContent(this.current):this.current}constructor(e,t,a){this.treeAdapter=t,this.handler=a,this.items=[],this.tagIDs=[],this.stackTop=-1,this.tmplCount=0,this.currentTagId=r.UNKNOWN,this.current=e}_indexOf(e){return this.items.lastIndexOf(e,this.stackTop)}_isInTemplate(){return this.currentTagId===r.TEMPLATE&&this.treeAdapter.getNamespaceURI(this.current)===E.HTML}_updateCurrentElement(){this.current=this.items[this.stackTop],this.currentTagId=this.tagIDs[this.stackTop]}push(e,t){this.stackTop++,this.items[this.stackTop]=e,this.current=e,this.tagIDs[this.stackTop]=t,this.currentTagId=t,this._isInTemplate()&&this.tmplCount++,this.handler.onItemPush(e,t,!0)}pop(){let e=this.current;this.tmplCount>0&&this._isInTemplate()&&this.tmplCount--,this.stackTop--,this._updateCurrentElement(),this.handler.onItemPop(e,!0)}replace(e,t){let a=this._indexOf(e);this.items[a]=t,a===this.stackTop&&(this.current=t)}insertAfter(e,t,a){let s=this._indexOf(e)+1;this.items.splice(s,0,t),this.tagIDs.splice(s,0,a),this.stackTop++,s===this.stackTop&&this._updateCurrentElement(),this.current&&this.currentTagId!==void 0&&this.handler.onItemPush(this.current,this.currentTagId,s===this.stackTop)}popUntilTagNamePopped(e){let t=this.stackTop+1;do t=this.tagIDs.lastIndexOf(e,t-1);while(t>0&&this.treeAdapter.getNamespaceURI(this.items[t])!==E.HTML);this.shortenToLength(Math.max(t,0))}shortenToLength(e){for(;this.stackTop>=e;){let t=this.current;this.tmplCount>0&&this._isInTemplate()&&(this.tmplCount-=1),this.stackTop--,this._updateCurrentElement(),this.handler.onItemPop(t,this.stackTop<e)}}popUntilElementPopped(e){let t=this._indexOf(e);this.shortenToLength(Math.max(t,0))}popUntilPopped(e,t){let a=this._indexOfTagNames(e,t);this.shortenToLength(Math.max(a,0))}popUntilNumberedHeaderPopped(){this.popUntilPopped(ae,E.HTML)}popUntilTableCellPopped(){this.popUntilPopped(qc,E.HTML)}popAllUpToHtmlElement(){this.tmplCount=0,this.shortenToLength(1)}_indexOfTagNames(e,t){for(let a=this.stackTop;a>=0;a--)if(e.has(this.tagIDs[a])&&this.treeAdapter.getNamespaceURI(this.items[a])===t)return a;return-1}clearBackTo(e,t){let a=this._indexOfTagNames(e,t);this.shortenToLength(a+1)}clearBackToTableContext(){this.clearBackTo(Fc,E.HTML)}clearBackToTableBodyContext(){this.clearBackTo(Hc,E.HTML)}clearBackToTableRowContext(){this.clearBackTo(Uc,E.HTML)}remove(e){let t=this._indexOf(e);t>=0&&(t===this.stackTop?this.pop():(this.items.splice(t,1),this.tagIDs.splice(t,1),this.stackTop--,this._updateCurrentElement(),this.handler.onItemPop(e,!1)))}tryPeekProperlyNestedBodyElement(){return this.stackTop>=1&&this.tagIDs[1]===r.BODY?this.items[1]:null}contains(e){return this._indexOf(e)>-1}getCommonAncestor(e){let t=this._indexOf(e)-1;return t>=0?this.items[t]:null}isRootHtmlElementCurrent(){return this.stackTop===0&&this.tagIDs[0]===r.HTML}hasInDynamicScope(e,t){for(let a=this.stackTop;a>=0;a--){let s=this.tagIDs[a];switch(this.treeAdapter.getNamespaceURI(this.items[a])){case E.HTML:{if(s===e)return!0;if(t.has(s))return!1;break}case E.SVG:{if(vr.has(s))return!1;break}case E.MATHML:{if(Br.has(s))return!1;break}}}return!0}hasInScope(e){return this.hasInDynamicScope(e,It)}hasInListItemScope(e){return this.hasInDynamicScope(e,Bc)}hasInButtonScope(e){return this.hasInDynamicScope(e,vc)}hasNumberedHeaderInScope(){for(let e=this.stackTop;e>=0;e--){let t=this.tagIDs[e];switch(this.treeAdapter.getNamespaceURI(this.items[e])){case E.HTML:{if(ae.has(t))return!0;if(It.has(t))return!1;break}case E.SVG:{if(vr.has(t))return!1;break}case E.MATHML:{if(Br.has(t))return!1;break}}}return!0}hasInTableScope(e){for(let t=this.stackTop;t>=0;t--)if(this.treeAdapter.getNamespaceURI(this.items[t])===E.HTML)switch(this.tagIDs[t]){case e:return!0;case r.TABLE:case r.HTML:return!1}return!0}hasTableBodyContextInTableScope(){for(let e=this.stackTop;e>=0;e--)if(this.treeAdapter.getNamespaceURI(this.items[e])===E.HTML)switch(this.tagIDs[e]){case r.TBODY:case r.THEAD:case r.TFOOT:return!0;case r.TABLE:case r.HTML:return!1}return!0}hasInSelectScope(e){for(let t=this.stackTop;t>=0;t--)if(this.treeAdapter.getNamespaceURI(this.items[t])===E.HTML)switch(this.tagIDs[t]){case e:return!0;case r.OPTION:case r.OPTGROUP:break;default:return!1}return!0}generateImpliedEndTags(){for(;this.currentTagId!==void 0&&Ur.has(this.currentTagId);)this.pop()}generateImpliedEndTagsThoroughly(){for(;this.currentTagId!==void 0&&wr.has(this.currentTagId);)this.pop()}generateImpliedEndTagsWithExclusion(e){for(;this.currentTagId!==void 0&&this.currentTagId!==e&&wr.has(this.currentTagId);)this.pop()}};var nu;(function(u){u[u.Marker=0]="Marker",u[u.Element=1]="Element"})(nu||(nu={}));var Hr={type:nu.Marker},Lt=class{constructor(e){this.treeAdapter=e,this.entries=[],this.bookmark=null}_getNoahArkConditionCandidates(e,t){let a=[],s=t.length,i=this.treeAdapter.getTagName(e),n=this.treeAdapter.getNamespaceURI(e);for(let d=0;d<this.entries.length;d++){let l=this.entries[d];if(l.type===nu.Marker)break;let{element:h}=l;if(this.treeAdapter.getTagName(h)===i&&this.treeAdapter.getNamespaceURI(h)===n){let p=this.treeAdapter.getAttrList(h);p.length===s&&a.push({idx:d,attrs:p})}}return a}_ensureNoahArkCondition(e){if(this.entries.length<3)return;let t=this.treeAdapter.getAttrList(e),a=this._getNoahArkConditionCandidates(e,t);if(a.length<3)return;let s=new Map(t.map(n=>[n.name,n.value])),i=0;for(let n=0;n<a.length;n++){let d=a[n];d.attrs.every(l=>s.get(l.name)===l.value)&&(i+=1,i>=3&&this.entries.splice(d.idx,1))}}insertMarker(){this.entries.unshift(Hr)}pushElement(e,t){this._ensureNoahArkCondition(e),this.entries.unshift({type:nu.Element,element:e,token:t})}insertElementAfterBookmark(e,t){let a=this.entries.indexOf(this.bookmark);this.entries.splice(a,0,{type:nu.Element,element:e,token:t})}removeEntry(e){let t=this.entries.indexOf(e);t!==-1&&this.entries.splice(t,1)}clearToLastMarker(){let e=this.entries.indexOf(Hr);e===-1?this.entries.length=0:this.entries.splice(0,e+1)}getElementEntryInScopeWithTagName(e){let t=this.entries.find(a=>a.type===nu.Marker||this.treeAdapter.getTagName(a.element)===e);return t&&t.type===nu.Element?t:null}getElementEntry(e){return this.entries.find(t=>t.type===nu.Element&&t.element===e)}};var su={createDocument(){return{nodeName:"#document",mode:G.NO_QUIRKS,childNodes:[]}},createDocumentFragment(){return{nodeName:"#document-fragment",childNodes:[]}},createElement(u,e,t){return{nodeName:u,tagName:u,attrs:t,namespaceURI:e,childNodes:[],parentNode:null}},createCommentNode(u){return{nodeName:"#comment",data:u,parentNode:null}},createTextNode(u){return{nodeName:"#text",value:u,parentNode:null}},appendChild(u,e){u.childNodes.push(e),e.parentNode=u},insertBefore(u,e,t){let a=u.childNodes.indexOf(t);u.childNodes.splice(a,0,e),e.parentNode=u},setTemplateContent(u,e){u.content=e},getTemplateContent(u){return u.content},setDocumentType(u,e,t,a){let s=u.childNodes.find(i=>i.nodeName==="#documentType");if(s)s.name=e,s.publicId=t,s.systemId=a;else{let i={nodeName:"#documentType",name:e,publicId:t,systemId:a,parentNode:null};su.appendChild(u,i)}},setDocumentMode(u,e){u.mode=e},getDocumentMode(u){return u.mode},detachNode(u){if(u.parentNode){let e=u.parentNode.childNodes.indexOf(u);u.parentNode.childNodes.splice(e,1),u.parentNode=null}},insertText(u,e){if(u.childNodes.length>0){let t=u.childNodes[u.childNodes.length-1];if(su.isTextNode(t)){t.value+=e;return}}su.appendChild(u,su.createTextNode(e))},insertTextBefore(u,e,t){let a=u.childNodes[u.childNodes.indexOf(t)-1];a&&su.isTextNode(a)?a.value+=e:su.insertBefore(u,su.createTextNode(e),t)},adoptAttributes(u,e){let t=new Set(u.attrs.map(a=>a.name));for(let a=0;a<e.length;a++)t.has(e[a].name)||u.attrs.push(e[a])},getFirstChild(u){return u.childNodes[0]},getChildNodes(u){return u.childNodes},getParentNode(u){return u.parentNode},getAttrList(u){return u.attrs},getTagName(u){return u.tagName},getNamespaceURI(u){return u.namespaceURI},getTextNodeContent(u){return u.value},getCommentNodeContent(u){return u.data},getDocumentTypeNodeName(u){return u.name},getDocumentTypeNodePublicId(u){return u.publicId},getDocumentTypeNodeSystemId(u){return u.systemId},isTextNode(u){return u.nodeName==="#text"},isCommentNode(u){return u.nodeName==="#comment"},isDocumentTypeNode(u){return u.nodeName==="#documentType"},isElementNode(u){return Object.prototype.hasOwnProperty.call(u,"tagName")},setNodeSourceCodeLocation(u,e){u.sourceCodeLocation=e},getNodeSourceCodeLocation(u){return u.sourceCodeLocation},updateNodeSourceCodeLocation(u,e){u.sourceCodeLocation={...u.sourceCodeLocation,...e}}};var qr="html",Yc="about:legacy-compat",Vc="http://www.ibm.com/data/dtd/v11/ibmxhtml1-transitional.dtd",Yr=["+//silmaril//dtd html pro v0r11 19970101//","-//as//dtd html 3.0 aswedit + extensions//","-//advasoft ltd//dtd html 3.0 aswedit + extensions//","-//ietf//dtd html 2.0 level 1//","-//ietf//dtd html 2.0 level 2//","-//ietf//dtd html 2.0 strict level 1//","-//ietf//dtd html 2.0 strict level 2//","-//ietf//dtd html 2.0 strict//","-//ietf//dtd html 2.0//","-//ietf//dtd html 2.1e//","-//ietf//dtd html 3.0//","-//ietf//dtd html 3.2 final//","-//ietf//dtd html 3.2//","-//ietf//dtd html 3//","-//ietf//dtd html level 0//","-//ietf//dtd html level 1//","-//ietf//dtd html level 2//","-//ietf//dtd html level 3//","-//ietf//dtd html strict level 0//","-//ietf//dtd html strict level 1//","-//ietf//dtd html strict level 2//","-//ietf//dtd html strict level 3//","-//ietf//dtd html strict//","-//ietf//dtd html//","-//metrius//dtd metrius presentational//","-//microsoft//dtd internet explorer 2.0 html strict//","-//microsoft//dtd internet explorer 2.0 html//","-//microsoft//dtd internet explorer 2.0 tables//","-//microsoft//dtd internet explorer 3.0 html strict//","-//microsoft//dtd internet explorer 3.0 html//","-//microsoft//dtd internet explorer 3.0 tables//","-//netscape comm. corp.//dtd html//","-//netscape comm. corp.//dtd strict html//","-//o'reilly and associates//dtd html 2.0//","-//o'reilly and associates//dtd html extended 1.0//","-//o'reilly and associates//dtd html extended relaxed 1.0//","-//sq//dtd html 2.0 hotmetal + extensions//","-//softquad software//dtd hotmetal pro 6.0::19990601::extensions to html 4.0//","-//softquad//dtd hotmetal pro 4.0::19971010::extensions to html 4.0//","-//spyglass//dtd html 2.0 extended//","-//sun microsystems corp.//dtd hotjava html//","-//sun microsystems corp.//dtd hotjava strict html//","-//w3c//dtd html 3 1995-03-24//","-//w3c//dtd html 3.2 draft//","-//w3c//dtd html 3.2 final//","-//w3c//dtd html 3.2//","-//w3c//dtd html 3.2s draft//","-//w3c//dtd html 4.0 frameset//","-//w3c//dtd html 4.0 transitional//","-//w3c//dtd html experimental 19960712//","-//w3c//dtd html experimental 970421//","-//w3c//dtd w3 html//","-//w3o//dtd w3 html 3.0//","-//webtechs//dtd mozilla html 2.0//","-//webtechs//dtd mozilla html//"],Gc=[...Yr,"-//w3c//dtd html 4.01 frameset//","-//w3c//dtd html 4.01 transitional//"],Wc=new Set(["-//w3o//dtd w3 html strict 3.0//en//","-/w3c/dtd html 4.0 transitional/en","html"]),Vr=["-//w3c//dtd xhtml 1.0 frameset//","-//w3c//dtd xhtml 1.0 transitional//"],Qc=[...Vr,"-//w3c//dtd html 4.01 frameset//","-//w3c//dtd html 4.01 transitional//"];function Fr(u,e){return e.some(t=>u.startsWith(t))}function Gr(u){return u.name===qr&&u.publicId===null&&(u.systemId===null||u.systemId===Yc)}function Wr(u){if(u.name!==qr)return G.QUIRKS;let{systemId:e}=u;if(e&&e.toLowerCase()===Vc)return G.QUIRKS;let{publicId:t}=u;if(t!==null){if(t=t.toLowerCase(),Wc.has(t))return G.QUIRKS;let a=e===null?Gc:Yr;if(Fr(t,a))return G.QUIRKS;if(a=e===null?Vr:Qc,Fr(t,a))return G.LIMITED_QUIRKS}return G.NO_QUIRKS}var Rt={};$(Rt,{SVG_TAG_NAMES_ADJUSTMENT_MAP:()=>Xr,adjustTokenMathMLAttrs:()=>Dt,adjustTokenSVGAttrs:()=>Ot,adjustTokenSVGTagName:()=>Z0,adjustTokenXMLAttrs:()=>Oe,causesExit:()=>$0,isIntegrationPoint:()=>J0});var Qr={TEXT_HTML:"text/html",APPLICATION_XML:"application/xhtml+xml"},Kc="definitionurl",jc="definitionURL",zc=new Map(["attributeName","attributeType","baseFrequency","baseProfile","calcMode","clipPathUnits","diffuseConstant","edgeMode","filterUnits","glyphRef","gradientTransform","gradientUnits","kernelMatrix","kernelUnitLength","keyPoints","keySplines","keyTimes","lengthAdjust","limitingConeAngle","markerHeight","markerUnits","markerWidth","maskContentUnits","maskUnits","numOctaves","pathLength","patternContentUnits","patternTransform","patternUnits","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","refX","refY","repeatCount","repeatDur","requiredExtensions","requiredFeatures","specularConstant","specularExponent","spreadMethod","startOffset","stdDeviation","stitchTiles","surfaceScale","systemLanguage","tableValues","targetX","targetY","textLength","viewBox","viewTarget","xChannelSelector","yChannelSelector","zoomAndPan"].map(u=>[u.toLowerCase(),u])),$c=new Map([["xlink:actuate",{prefix:"xlink",name:"actuate",namespace:E.XLINK}],["xlink:arcrole",{prefix:"xlink",name:"arcrole",namespace:E.XLINK}],["xlink:href",{prefix:"xlink",name:"href",namespace:E.XLINK}],["xlink:role",{prefix:"xlink",name:"role",namespace:E.XLINK}],["xlink:show",{prefix:"xlink",name:"show",namespace:E.XLINK}],["xlink:title",{prefix:"xlink",name:"title",namespace:E.XLINK}],["xlink:type",{prefix:"xlink",name:"type",namespace:E.XLINK}],["xml:lang",{prefix:"xml",name:"lang",namespace:E.XML}],["xml:space",{prefix:"xml",name:"space",namespace:E.XML}],["xmlns",{prefix:"",name:"xmlns",namespace:E.XMLNS}],["xmlns:xlink",{prefix:"xmlns",name:"xlink",namespace:E.XMLNS}]]),Xr=new Map(["altGlyph","altGlyphDef","altGlyphItem","animateColor","animateMotion","animateTransform","clipPath","feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","foreignObject","glyphRef","linearGradient","radialGradient","textPath"].map(u=>[u.toLowerCase(),u])),Zc=new Set([r.B,r.BIG,r.BLOCKQUOTE,r.BODY,r.BR,r.CENTER,r.CODE,r.DD,r.DIV,r.DL,r.DT,r.EM,r.EMBED,r.H1,r.H2,r.H3,r.H4,r.H5,r.H6,r.HEAD,r.HR,r.I,r.IMG,r.LI,r.LISTING,r.MENU,r.META,r.NOBR,r.OL,r.P,r.PRE,r.RUBY,r.S,r.SMALL,r.SPAN,r.STRONG,r.STRIKE,r.SUB,r.SUP,r.TABLE,r.TT,r.U,r.UL,r.VAR]);function $0(u){let e=u.tagID;return e===r.FONT&&u.attrs.some(({name:a})=>a===fu.COLOR||a===fu.SIZE||a===fu.FACE)||Zc.has(e)}function Dt(u){for(let e=0;e<u.attrs.length;e++)if(u.attrs[e].name===Kc){u.attrs[e].name=jc;break}}function Ot(u){for(let e=0;e<u.attrs.length;e++){let t=zc.get(u.attrs[e].name);t!=null&&(u.attrs[e].name=t)}}function Oe(u){for(let e=0;e<u.attrs.length;e++){let t=$c.get(u.attrs[e].name);t&&(u.attrs[e].prefix=t.prefix,u.attrs[e].name=t.name,u.attrs[e].namespace=t.namespace)}}function Z0(u){let e=Xr.get(u.tagName);e!=null&&(u.tagName=e,u.tagID=yu(u.tagName))}function Jc(u,e){return e===E.MATHML&&(u===r.MI||u===r.MO||u===r.MN||u===r.MS||u===r.MTEXT)}function u1(u,e,t){if(e===E.MATHML&&u===r.ANNOTATION_XML){for(let a=0;a<t.length;a++)if(t[a].name===fu.ENCODING){let s=t[a].value.toLowerCase();return s===Qr.TEXT_HTML||s===Qr.APPLICATION_XML}}return e===E.SVG&&(u===r.FOREIGN_OBJECT||u===r.DESC||u===r.TITLE)}function J0(u,e,t,a){return(!a||a===E.HTML)&&u1(u,e,t)||(!a||a===E.MATHML)&&Jc(u,e)}var e1="hidden",t1=8,a1=3,f;(function(u){u[u.INITIAL=0]="INITIAL",u[u.BEFORE_HTML=1]="BEFORE_HTML",u[u.BEFORE_HEAD=2]="BEFORE_HEAD",u[u.IN_HEAD=3]="IN_HEAD",u[u.IN_HEAD_NO_SCRIPT=4]="IN_HEAD_NO_SCRIPT",u[u.AFTER_HEAD=5]="AFTER_HEAD",u[u.IN_BODY=6]="IN_BODY",u[u.TEXT=7]="TEXT",u[u.IN_TABLE=8]="IN_TABLE",u[u.IN_TABLE_TEXT=9]="IN_TABLE_TEXT",u[u.IN_CAPTION=10]="IN_CAPTION",u[u.IN_COLUMN_GROUP=11]="IN_COLUMN_GROUP",u[u.IN_TABLE_BODY=12]="IN_TABLE_BODY",u[u.IN_ROW=13]="IN_ROW",u[u.IN_CELL=14]="IN_CELL",u[u.IN_SELECT=15]="IN_SELECT",u[u.IN_SELECT_IN_TABLE=16]="IN_SELECT_IN_TABLE",u[u.IN_TEMPLATE=17]="IN_TEMPLATE",u[u.AFTER_BODY=18]="AFTER_BODY",u[u.IN_FRAMESET=19]="IN_FRAMESET",u[u.AFTER_FRAMESET=20]="AFTER_FRAMESET",u[u.AFTER_AFTER_BODY=21]="AFTER_AFTER_BODY",u[u.AFTER_AFTER_FRAMESET=22]="AFTER_AFTER_FRAMESET"})(f||(f={}));var r1={startLine:-1,startCol:-1,startOffset:-1,endLine:-1,endCol:-1,endOffset:-1},$r=new Set([r.TABLE,r.TBODY,r.TFOOT,r.THEAD,r.TR]),Kr={scriptingEnabled:!0,sourceCodeLocationInfo:!1,treeAdapter:su,onParseError:null},re=class{constructor(e,t,a=null,s=null){this.fragmentContext=a,this.scriptHandler=s,this.currentToken=null,this.stopped=!1,this.insertionMode=f.INITIAL,this.originalInsertionMode=f.INITIAL,this.headElement=null,this.formElement=null,this.currentNotInHTML=!1,this.tmplInsertionModeStack=[],this.pendingCharacterTokens=[],this.hasNonWhitespacePendingCharacterToken=!1,this.framesetOk=!0,this.skipNextNewLine=!1,this.fosterParentingEnabled=!1,this.options={...Kr,...e},this.treeAdapter=this.options.treeAdapter,this.onParseError=this.options.onParseError,this.onParseError&&(this.options.sourceCodeLocationInfo=!0),this.document=t!=null?t:this.treeAdapter.createDocument(),this.tokenizer=new De(this.options,this),this.activeFormattingElements=new Lt(this.treeAdapter),this.fragmentContextID=a?yu(this.treeAdapter.getTagName(a)):r.UNKNOWN,this._setContextModes(a!=null?a:this.document,this.fragmentContextID),this.openElements=new Ct(this.document,this.treeAdapter,this)}static parse(e,t){let a=new this(t);return a.tokenizer.write(e,!0),a.document}static getFragmentParser(e,t){let a={...Kr,...t};e!=null||(e=a.treeAdapter.createElement(b.TEMPLATE,E.HTML,[]));let s=a.treeAdapter.createElement("documentmock",E.HTML,[]),i=new this(a,s,e);return i.fragmentContextID===r.TEMPLATE&&i.tmplInsertionModeStack.unshift(f.IN_TEMPLATE),i._initTokenizerForFragmentParsing(),i._insertFakeRootElement(),i._resetInsertionMode(),i._findFormInFragmentContext(),i}getFragment(){let e=this.treeAdapter.getFirstChild(this.document),t=this.treeAdapter.createDocumentFragment();return this._adoptNodes(e,t),t}_err(e,t,a){var s;if(!this.onParseError)return;let i=(s=e.location)!==null&&s!==void 0?s:r1,n={code:t,startLine:i.startLine,startCol:i.startCol,startOffset:i.startOffset,endLine:a?i.startLine:i.endLine,endCol:a?i.startCol:i.endCol,endOffset:a?i.startOffset:i.endOffset};this.onParseError(n)}onItemPush(e,t,a){var s,i;(i=(s=this.treeAdapter).onItemPush)===null||i===void 0||i.call(s,e),a&&this.openElements.stackTop>0&&this._setContextModes(e,t)}onItemPop(e,t){var a,s;if(this.options.sourceCodeLocationInfo&&this._setEndLocation(e,this.currentToken),(s=(a=this.treeAdapter).onItemPop)===null||s===void 0||s.call(a,e,this.openElements.current),t){let i,n;this.openElements.stackTop===0&&this.fragmentContext?(i=this.fragmentContext,n=this.fragmentContextID):{current:i,currentTagId:n}=this.openElements,this._setContextModes(i,n)}}_setContextModes(e,t){let a=e===this.document||e&&this.treeAdapter.getNamespaceURI(e)===E.HTML;this.currentNotInHTML=!a,this.tokenizer.inForeignNode=!a&&e!==void 0&&t!==void 0&&!this._isIntegrationPoint(t,e)}_switchToTextParsing(e,t){this._insertElement(e,E.HTML),this.tokenizer.state=t,this.originalInsertionMode=this.insertionMode,this.insertionMode=f.TEXT}switchToPlaintextParsing(){this.insertionMode=f.TEXT,this.originalInsertionMode=f.IN_BODY,this.tokenizer.state=W.PLAINTEXT}_getAdjustedCurrentElement(){return this.openElements.stackTop===0&&this.fragmentContext?this.fragmentContext:this.openElements.current}_findFormInFragmentContext(){let e=this.fragmentContext;for(;e;){if(this.treeAdapter.getTagName(e)===b.FORM){this.formElement=e;break}e=this.treeAdapter.getParentNode(e)}}_initTokenizerForFragmentParsing(){if(!(!this.fragmentContext||this.treeAdapter.getNamespaceURI(this.fragmentContext)!==E.HTML))switch(this.fragmentContextID){case r.TITLE:case r.TEXTAREA:{this.tokenizer.state=W.RCDATA;break}case r.STYLE:case r.XMP:case r.IFRAME:case r.NOEMBED:case r.NOFRAMES:case r.NOSCRIPT:{this.tokenizer.state=W.RAWTEXT;break}case r.SCRIPT:{this.tokenizer.state=W.SCRIPT_DATA;break}case r.PLAINTEXT:{this.tokenizer.state=W.PLAINTEXT;break}default:}}_setDocumentType(e){let t=e.name||"",a=e.publicId||"",s=e.systemId||"";if(this.treeAdapter.setDocumentType(this.document,t,a,s),e.location){let n=this.treeAdapter.getChildNodes(this.document).find(d=>this.treeAdapter.isDocumentTypeNode(d));n&&this.treeAdapter.setNodeSourceCodeLocation(n,e.location)}}_attachElementToTree(e,t){if(this.options.sourceCodeLocationInfo){let a=t&&{...t,startTag:t};this.treeAdapter.setNodeSourceCodeLocation(e,a)}if(this._shouldFosterParentOnInsertion())this._fosterParentElement(e);else{let a=this.openElements.currentTmplContentOrNode;this.treeAdapter.appendChild(a!=null?a:this.document,e)}}_appendElement(e,t){let a=this.treeAdapter.createElement(e.tagName,t,e.attrs);this._attachElementToTree(a,e.location)}_insertElement(e,t){let a=this.treeAdapter.createElement(e.tagName,t,e.attrs);this._attachElementToTree(a,e.location),this.openElements.push(a,e.tagID)}_insertFakeElement(e,t){let a=this.treeAdapter.createElement(e,E.HTML,[]);this._attachElementToTree(a,null),this.openElements.push(a,t)}_insertTemplate(e){let t=this.treeAdapter.createElement(e.tagName,E.HTML,e.attrs),a=this.treeAdapter.createDocumentFragment();this.treeAdapter.setTemplateContent(t,a),this._attachElementToTree(t,e.location),this.openElements.push(t,e.tagID),this.options.sourceCodeLocationInfo&&this.treeAdapter.setNodeSourceCodeLocation(a,null)}_insertFakeRootElement(){let e=this.treeAdapter.createElement(b.HTML,E.HTML,[]);this.options.sourceCodeLocationInfo&&this.treeAdapter.setNodeSourceCodeLocation(e,null),this.treeAdapter.appendChild(this.openElements.current,e),this.openElements.push(e,r.HTML)}_appendCommentNode(e,t){let a=this.treeAdapter.createCommentNode(e.data);this.treeAdapter.appendChild(t,a),this.options.sourceCodeLocationInfo&&this.treeAdapter.setNodeSourceCodeLocation(a,e.location)}_insertCharacters(e){let t,a;if(this._shouldFosterParentOnInsertion()?({parent:t,beforeElement:a}=this._findFosterParentingLocation(),a?this.treeAdapter.insertTextBefore(t,e.chars,a):this.treeAdapter.insertText(t,e.chars)):(t=this.openElements.currentTmplContentOrNode,this.treeAdapter.insertText(t,e.chars)),!e.location)return;let s=this.treeAdapter.getChildNodes(t),i=a?s.lastIndexOf(a):s.length,n=s[i-1];if(this.treeAdapter.getNodeSourceCodeLocation(n)){let{endLine:l,endCol:h,endOffset:p}=e.location;this.treeAdapter.updateNodeSourceCodeLocation(n,{endLine:l,endCol:h,endOffset:p})}else this.options.sourceCodeLocationInfo&&this.treeAdapter.setNodeSourceCodeLocation(n,e.location)}_adoptNodes(e,t){for(let a=this.treeAdapter.getFirstChild(e);a;a=this.treeAdapter.getFirstChild(e))this.treeAdapter.detachNode(a),this.treeAdapter.appendChild(t,a)}_setEndLocation(e,t){if(this.treeAdapter.getNodeSourceCodeLocation(e)&&t.location){let a=t.location,s=this.treeAdapter.getTagName(e),i=t.type===L.END_TAG&&s===t.tagName?{endTag:{...a},endLine:a.endLine,endCol:a.endCol,endOffset:a.endOffset}:{endLine:a.startLine,endCol:a.startCol,endOffset:a.startOffset};this.treeAdapter.updateNodeSourceCodeLocation(e,i)}}shouldProcessStartTagTokenInForeignContent(e){if(!this.currentNotInHTML)return!1;let t,a;return this.openElements.stackTop===0&&this.fragmentContext?(t=this.fragmentContext,a=this.fragmentContextID):{current:t,currentTagId:a}=this.openElements,e.tagID===r.SVG&&this.treeAdapter.getTagName(t)===b.ANNOTATION_XML&&this.treeAdapter.getNamespaceURI(t)===E.MATHML?!1:this.tokenizer.inForeignNode||(e.tagID===r.MGLYPH||e.tagID===r.MALIGNMARK)&&a!==void 0&&!this._isIntegrationPoint(a,t,E.HTML)}_processToken(e){switch(e.type){case L.CHARACTER:{this.onCharacter(e);break}case L.NULL_CHARACTER:{this.onNullCharacter(e);break}case L.COMMENT:{this.onComment(e);break}case L.DOCTYPE:{this.onDoctype(e);break}case L.START_TAG:{this._processStartTag(e);break}case L.END_TAG:{this.onEndTag(e);break}case L.EOF:{this.onEof(e);break}case L.WHITESPACE_CHARACTER:{this.onWhitespaceCharacter(e);break}}}_isIntegrationPoint(e,t,a){let s=this.treeAdapter.getNamespaceURI(t),i=this.treeAdapter.getAttrList(t);return J0(e,s,i,a)}_reconstructActiveFormattingElements(){let e=this.activeFormattingElements.entries.length;if(e){let t=this.activeFormattingElements.entries.findIndex(s=>s.type===nu.Marker||this.openElements.contains(s.element)),a=t===-1?e-1:t-1;for(let s=a;s>=0;s--){let i=this.activeFormattingElements.entries[s];this._insertElement(i.token,this.treeAdapter.getNamespaceURI(i.element)),i.element=this.openElements.current}}}_closeTableCell(){this.openElements.generateImpliedEndTags(),this.openElements.popUntilTableCellPopped(),this.activeFormattingElements.clearToLastMarker(),this.insertionMode=f.IN_ROW}_closePElement(){this.openElements.generateImpliedEndTagsWithExclusion(r.P),this.openElements.popUntilTagNamePopped(r.P)}_resetInsertionMode(){for(let e=this.openElements.stackTop;e>=0;e--)switch(e===0&&this.fragmentContext?this.fragmentContextID:this.openElements.tagIDs[e]){case r.TR:{this.insertionMode=f.IN_ROW;return}case r.TBODY:case r.THEAD:case r.TFOOT:{this.insertionMode=f.IN_TABLE_BODY;return}case r.CAPTION:{this.insertionMode=f.IN_CAPTION;return}case r.COLGROUP:{this.insertionMode=f.IN_COLUMN_GROUP;return}case r.TABLE:{this.insertionMode=f.IN_TABLE;return}case r.BODY:{this.insertionMode=f.IN_BODY;return}case r.FRAMESET:{this.insertionMode=f.IN_FRAMESET;return}case r.SELECT:{this._resetInsertionModeForSelect(e);return}case r.TEMPLATE:{this.insertionMode=this.tmplInsertionModeStack[0];return}case r.HTML:{this.insertionMode=this.headElement?f.AFTER_HEAD:f.BEFORE_HEAD;return}case r.TD:case r.TH:{if(e>0){this.insertionMode=f.IN_CELL;return}break}case r.HEAD:{if(e>0){this.insertionMode=f.IN_HEAD;return}break}}this.insertionMode=f.IN_BODY}_resetInsertionModeForSelect(e){if(e>0)for(let t=e-1;t>0;t--){let a=this.openElements.tagIDs[t];if(a===r.TEMPLATE)break;if(a===r.TABLE){this.insertionMode=f.IN_SELECT_IN_TABLE;return}}this.insertionMode=f.IN_SELECT}_isElementCausesFosterParenting(e){return $r.has(e)}_shouldFosterParentOnInsertion(){return this.fosterParentingEnabled&&this.openElements.currentTagId!==void 0&&this._isElementCausesFosterParenting(this.openElements.currentTagId)}_findFosterParentingLocation(){for(let e=this.openElements.stackTop;e>=0;e--){let t=this.openElements.items[e];switch(this.openElements.tagIDs[e]){case r.TEMPLATE:{if(this.treeAdapter.getNamespaceURI(t)===E.HTML)return{parent:this.treeAdapter.getTemplateContent(t),beforeElement:null};break}case r.TABLE:{let a=this.treeAdapter.getParentNode(t);return a?{parent:a,beforeElement:t}:{parent:this.openElements.items[e-1],beforeElement:null}}default:}}return{parent:this.openElements.items[0],beforeElement:null}}_fosterParentElement(e){let t=this._findFosterParentingLocation();t.beforeElement?this.treeAdapter.insertBefore(t.parent,e,t.beforeElement):this.treeAdapter.appendChild(t.parent,e)}_isSpecialElement(e,t){let a=this.treeAdapter.getNamespaceURI(e);return j0[a].has(t)}onCharacter(e){if(this.skipNextNewLine=!1,this.tokenizer.inForeignNode){wo(this,e);return}switch(this.insertionMode){case f.INITIAL:{Re(this,e);break}case f.BEFORE_HTML:{Pe(this,e);break}case f.BEFORE_HEAD:{Me(this,e);break}case f.IN_HEAD:{ke(this,e);break}case f.IN_HEAD_NO_SCRIPT:{we(this,e);break}case f.AFTER_HEAD:{Be(this,e);break}case f.IN_BODY:case f.IN_CAPTION:case f.IN_CELL:case f.IN_TEMPLATE:{Jr(this,e);break}case f.TEXT:case f.IN_SELECT:case f.IN_SELECT_IN_TABLE:{this._insertCharacters(e);break}case f.IN_TABLE:case f.IN_TABLE_BODY:case f.IN_ROW:{ua(this,e);break}case f.IN_TABLE_TEXT:{ss(this,e);break}case f.IN_COLUMN_GROUP:{Pt(this,e);break}case f.AFTER_BODY:{Mt(this,e);break}case f.AFTER_AFTER_BODY:{yt(this,e);break}default:}}onNullCharacter(e){if(this.skipNextNewLine=!1,this.tokenizer.inForeignNode){ko(this,e);return}switch(this.insertionMode){case f.INITIAL:{Re(this,e);break}case f.BEFORE_HTML:{Pe(this,e);break}case f.BEFORE_HEAD:{Me(this,e);break}case f.IN_HEAD:{ke(this,e);break}case f.IN_HEAD_NO_SCRIPT:{we(this,e);break}case f.AFTER_HEAD:{Be(this,e);break}case f.TEXT:{this._insertCharacters(e);break}case f.IN_TABLE:case f.IN_TABLE_BODY:case f.IN_ROW:{ua(this,e);break}case f.IN_COLUMN_GROUP:{Pt(this,e);break}case f.AFTER_BODY:{Mt(this,e);break}case f.AFTER_AFTER_BODY:{yt(this,e);break}default:}}onComment(e){if(this.skipNextNewLine=!1,this.currentNotInHTML){ea(this,e);return}switch(this.insertionMode){case f.INITIAL:case f.BEFORE_HTML:case f.BEFORE_HEAD:case f.IN_HEAD:case f.IN_HEAD_NO_SCRIPT:case f.AFTER_HEAD:case f.IN_BODY:case f.IN_TABLE:case f.IN_CAPTION:case f.IN_COLUMN_GROUP:case f.IN_TABLE_BODY:case f.IN_ROW:case f.IN_CELL:case f.IN_SELECT:case f.IN_SELECT_IN_TABLE:case f.IN_TEMPLATE:case f.IN_FRAMESET:case f.AFTER_FRAMESET:{ea(this,e);break}case f.IN_TABLE_TEXT:{ye(this,e);break}case f.AFTER_BODY:{f1(this,e);break}case f.AFTER_AFTER_BODY:case f.AFTER_AFTER_FRAMESET:{l1(this,e);break}default:}}onDoctype(e){switch(this.skipNextNewLine=!1,this.insertionMode){case f.INITIAL:{b1(this,e);break}case f.BEFORE_HEAD:case f.IN_HEAD:case f.IN_HEAD_NO_SCRIPT:case f.AFTER_HEAD:{this._err(e,m.misplacedDoctype);break}case f.IN_TABLE_TEXT:{ye(this,e);break}default:}}onStartTag(e){this.skipNextNewLine=!1,this.currentToken=e,this._processStartTag(e),e.selfClosing&&!e.ackSelfClosing&&this._err(e,m.nonVoidHtmlElementStartTagWithTrailingSolidus)}_processStartTag(e){this.shouldProcessStartTagTokenInForeignContent(e)?Bo(this,e):this._startTagOutsideForeignContent(e)}_startTagOutsideForeignContent(e){switch(this.insertionMode){case f.INITIAL:{Re(this,e);break}case f.BEFORE_HTML:{h1(this,e);break}case f.BEFORE_HEAD:{E1(this,e);break}case f.IN_HEAD:{cu(this,e);break}case f.IN_HEAD_NO_SCRIPT:{g1(this,e);break}case f.AFTER_HEAD:{A1(this,e);break}case f.IN_BODY:{Q(this,e);break}case f.IN_TABLE:{se(this,e);break}case f.IN_TABLE_TEXT:{ye(this,e);break}case f.IN_CAPTION:{To(this,e);break}case f.IN_COLUMN_GROUP:{sa(this,e);break}case f.IN_TABLE_BODY:{Bt(this,e);break}case f.IN_ROW:{vt(this,e);break}case f.IN_CELL:{Ao(this,e);break}case f.IN_SELECT:{cs(this,e);break}case f.IN_SELECT_IN_TABLE:{No(this,e);break}case f.IN_TEMPLATE:{Io(this,e);break}case f.AFTER_BODY:{Lo(this,e);break}case f.IN_FRAMESET:{Do(this,e);break}case f.AFTER_FRAMESET:{Ro(this,e);break}case f.AFTER_AFTER_BODY:{Po(this,e);break}case f.AFTER_AFTER_FRAMESET:{Mo(this,e);break}default:}}onEndTag(e){this.skipNextNewLine=!1,this.currentToken=e,this.currentNotInHTML?vo(this,e):this._endTagOutsideForeignContent(e)}_endTagOutsideForeignContent(e){switch(this.insertionMode){case f.INITIAL:{Re(this,e);break}case f.BEFORE_HTML:{m1(this,e);break}case f.BEFORE_HEAD:{p1(this,e);break}case f.IN_HEAD:{T1(this,e);break}case f.IN_HEAD_NO_SCRIPT:{x1(this,e);break}case f.AFTER_HEAD:{_1(this,e);break}case f.IN_BODY:{wt(this,e);break}case f.TEXT:{no(this,e);break}case f.IN_TABLE:{ve(this,e);break}case f.IN_TABLE_TEXT:{ye(this,e);break}case f.IN_CAPTION:{go(this,e);break}case f.IN_COLUMN_GROUP:{xo(this,e);break}case f.IN_TABLE_BODY:{ta(this,e);break}case f.IN_ROW:{ns(this,e);break}case f.IN_CELL:{_o(this,e);break}case f.IN_SELECT:{os(this,e);break}case f.IN_SELECT_IN_TABLE:{So(this,e);break}case f.IN_TEMPLATE:{Co(this,e);break}case f.AFTER_BODY:{fs(this,e);break}case f.IN_FRAMESET:{Oo(this,e);break}case f.AFTER_FRAMESET:{yo(this,e);break}case f.AFTER_AFTER_BODY:{yt(this,e);break}default:}}onEof(e){switch(this.insertionMode){case f.INITIAL:{Re(this,e);break}case f.BEFORE_HTML:{Pe(this,e);break}case f.BEFORE_HEAD:{Me(this,e);break}case f.IN_HEAD:{ke(this,e);break}case f.IN_HEAD_NO_SCRIPT:{we(this,e);break}case f.AFTER_HEAD:{Be(this,e);break}case f.IN_BODY:case f.IN_TABLE:case f.IN_CAPTION:case f.IN_COLUMN_GROUP:case f.IN_TABLE_BODY:case f.IN_ROW:case f.IN_CELL:case f.IN_SELECT:case f.IN_SELECT_IN_TABLE:{as(this,e);break}case f.TEXT:{co(this,e);break}case f.IN_TABLE_TEXT:{ye(this,e);break}case f.IN_TEMPLATE:{ds(this,e);break}case f.AFTER_BODY:case f.IN_FRAMESET:case f.AFTER_FRAMESET:case f.AFTER_AFTER_BODY:case f.AFTER_AFTER_FRAMESET:{ra(this,e);break}default:}}onWhitespaceCharacter(e){if(this.skipNextNewLine&&(this.skipNextNewLine=!1,e.chars.charCodeAt(0)===c.LINE_FEED)){if(e.chars.length===1)return;e.chars=e.chars.substr(1)}if(this.tokenizer.inForeignNode){this._insertCharacters(e);return}switch(this.insertionMode){case f.IN_HEAD:case f.IN_HEAD_NO_SCRIPT:case f.AFTER_HEAD:case f.TEXT:case f.IN_COLUMN_GROUP:case f.IN_SELECT:case f.IN_SELECT_IN_TABLE:case f.IN_FRAMESET:case f.AFTER_FRAMESET:{this._insertCharacters(e);break}case f.IN_BODY:case f.IN_CAPTION:case f.IN_CELL:case f.IN_TEMPLATE:case f.AFTER_BODY:case f.AFTER_AFTER_BODY:case f.AFTER_AFTER_FRAMESET:{Zr(this,e);break}case f.IN_TABLE:case f.IN_TABLE_BODY:case f.IN_ROW:{ua(this,e);break}case f.IN_TABLE_TEXT:{rs(this,e);break}default:}}};function s1(u,e){let t=u.activeFormattingElements.getElementEntryInScopeWithTagName(e.tagName);return t?u.openElements.contains(t.element)?u.openElements.hasInScope(e.tagID)||(t=null):(u.activeFormattingElements.removeEntry(t),t=null):ts(u,e),t}function i1(u,e){let t=null,a=u.openElements.stackTop;for(;a>=0;a--){let s=u.openElements.items[a];if(s===e.element)break;u._isSpecialElement(s,u.openElements.tagIDs[a])&&(t=s)}return t||(u.openElements.shortenToLength(Math.max(a,0)),u.activeFormattingElements.removeEntry(e)),t}function n1(u,e,t){let a=e,s=u.openElements.getCommonAncestor(e);for(let i=0,n=s;n!==t;i++,n=s){s=u.openElements.getCommonAncestor(n);let d=u.activeFormattingElements.getElementEntry(n),l=d&&i>=a1;!d||l?(l&&u.activeFormattingElements.removeEntry(d),u.openElements.remove(n)):(n=c1(u,d),a===e&&(u.activeFormattingElements.bookmark=d),u.treeAdapter.detachNode(a),u.treeAdapter.appendChild(n,a),a=n)}return a}function c1(u,e){let t=u.treeAdapter.getNamespaceURI(e.element),a=u.treeAdapter.createElement(e.token.tagName,t,e.token.attrs);return u.openElements.replace(e.element,a),e.element=a,a}function o1(u,e,t){let a=u.treeAdapter.getTagName(e),s=yu(a);if(u._isElementCausesFosterParenting(s))u._fosterParentElement(t);else{let i=u.treeAdapter.getNamespaceURI(e);s===r.TEMPLATE&&i===E.HTML&&(e=u.treeAdapter.getTemplateContent(e)),u.treeAdapter.appendChild(e,t)}}function d1(u,e,t){let a=u.treeAdapter.getNamespaceURI(t.element),{token:s}=t,i=u.treeAdapter.createElement(s.tagName,a,s.attrs);u._adoptNodes(e,i),u.treeAdapter.appendChild(e,i),u.activeFormattingElements.insertElementAfterBookmark(i,s),u.activeFormattingElements.removeEntry(t),u.openElements.remove(t.element),u.openElements.insertAfter(e,i,s.tagID)}function aa(u,e){for(let t=0;t<t1;t++){let a=s1(u,e);if(!a)break;let s=i1(u,a);if(!s)break;u.activeFormattingElements.bookmark=a;let i=n1(u,s,a.element),n=u.openElements.getCommonAncestor(a.element);u.treeAdapter.detachNode(i),n&&o1(u,n,i),d1(u,s,a)}}function ea(u,e){u._appendCommentNode(e,u.openElements.currentTmplContentOrNode)}function f1(u,e){u._appendCommentNode(e,u.openElements.items[0])}function l1(u,e){u._appendCommentNode(e,u.document)}function ra(u,e){if(u.stopped=!0,e.location){let t=u.fragmentContext?0:2;for(let a=u.openElements.stackTop;a>=t;a--)u._setEndLocation(u.openElements.items[a],e);if(!u.fragmentContext&&u.openElements.stackTop>=0){let a=u.openElements.items[0],s=u.treeAdapter.getNodeSourceCodeLocation(a);if(s&&!s.endTag&&(u._setEndLocation(a,e),u.openElements.stackTop>=1)){let i=u.openElements.items[1],n=u.treeAdapter.getNodeSourceCodeLocation(i);n&&!n.endTag&&u._setEndLocation(i,e)}}}}function b1(u,e){u._setDocumentType(e);let t=e.forceQuirks?G.QUIRKS:Wr(e);Gr(e)||u._err(e,m.nonConformingDoctype),u.treeAdapter.setDocumentMode(u.document,t),u.insertionMode=f.BEFORE_HTML}function Re(u,e){u._err(e,m.missingDoctype,!0),u.treeAdapter.setDocumentMode(u.document,G.QUIRKS),u.insertionMode=f.BEFORE_HTML,u._processToken(e)}function h1(u,e){e.tagID===r.HTML?(u._insertElement(e,E.HTML),u.insertionMode=f.BEFORE_HEAD):Pe(u,e)}function m1(u,e){let t=e.tagID;(t===r.HTML||t===r.HEAD||t===r.BODY||t===r.BR)&&Pe(u,e)}function Pe(u,e){u._insertFakeRootElement(),u.insertionMode=f.BEFORE_HEAD,u._processToken(e)}function E1(u,e){switch(e.tagID){case r.HTML:{Q(u,e);break}case r.HEAD:{u._insertElement(e,E.HTML),u.headElement=u.openElements.current,u.insertionMode=f.IN_HEAD;break}default:Me(u,e)}}function p1(u,e){let t=e.tagID;t===r.HEAD||t===r.BODY||t===r.HTML||t===r.BR?Me(u,e):u._err(e,m.endTagWithoutMatchingOpenElement)}function Me(u,e){u._insertFakeElement(b.HEAD,r.HEAD),u.headElement=u.openElements.current,u.insertionMode=f.IN_HEAD,u._processToken(e)}function cu(u,e){switch(e.tagID){case r.HTML:{Q(u,e);break}case r.BASE:case r.BASEFONT:case r.BGSOUND:case r.LINK:case r.META:{u._appendElement(e,E.HTML),e.ackSelfClosing=!0;break}case r.TITLE:{u._switchToTextParsing(e,W.RCDATA);break}case r.NOSCRIPT:{u.options.scriptingEnabled?u._switchToTextParsing(e,W.RAWTEXT):(u._insertElement(e,E.HTML),u.insertionMode=f.IN_HEAD_NO_SCRIPT);break}case r.NOFRAMES:case r.STYLE:{u._switchToTextParsing(e,W.RAWTEXT);break}case r.SCRIPT:{u._switchToTextParsing(e,W.SCRIPT_DATA);break}case r.TEMPLATE:{u._insertTemplate(e),u.activeFormattingElements.insertMarker(),u.framesetOk=!1,u.insertionMode=f.IN_TEMPLATE,u.tmplInsertionModeStack.unshift(f.IN_TEMPLATE);break}case r.HEAD:{u._err(e,m.misplacedStartTagForHeadElement);break}default:ke(u,e)}}function T1(u,e){switch(e.tagID){case r.HEAD:{u.openElements.pop(),u.insertionMode=f.AFTER_HEAD;break}case r.BODY:case r.BR:case r.HTML:{ke(u,e);break}case r.TEMPLATE:{Gu(u,e);break}default:u._err(e,m.endTagWithoutMatchingOpenElement)}}function Gu(u,e){u.openElements.tmplCount>0?(u.openElements.generateImpliedEndTagsThoroughly(),u.openElements.currentTagId!==r.TEMPLATE&&u._err(e,m.closingOfElementWithOpenChildElements),u.openElements.popUntilTagNamePopped(r.TEMPLATE),u.activeFormattingElements.clearToLastMarker(),u.tmplInsertionModeStack.shift(),u._resetInsertionMode()):u._err(e,m.endTagWithoutMatchingOpenElement)}function ke(u,e){u.openElements.pop(),u.insertionMode=f.AFTER_HEAD,u._processToken(e)}function g1(u,e){switch(e.tagID){case r.HTML:{Q(u,e);break}case r.BASEFONT:case r.BGSOUND:case r.HEAD:case r.LINK:case r.META:case r.NOFRAMES:case r.STYLE:{cu(u,e);break}case r.NOSCRIPT:{u._err(e,m.nestedNoscriptInHead);break}default:we(u,e)}}function x1(u,e){switch(e.tagID){case r.NOSCRIPT:{u.openElements.pop(),u.insertionMode=f.IN_HEAD;break}case r.BR:{we(u,e);break}default:u._err(e,m.endTagWithoutMatchingOpenElement)}}function we(u,e){let t=e.type===L.EOF?m.openElementsLeftAfterEof:m.disallowedContentInNoscriptInHead;u._err(e,t),u.openElements.pop(),u.insertionMode=f.IN_HEAD,u._processToken(e)}function A1(u,e){switch(e.tagID){case r.HTML:{Q(u,e);break}case r.BODY:{u._insertElement(e,E.HTML),u.framesetOk=!1,u.insertionMode=f.IN_BODY;break}case r.FRAMESET:{u._insertElement(e,E.HTML),u.insertionMode=f.IN_FRAMESET;break}case r.BASE:case r.BASEFONT:case r.BGSOUND:case r.LINK:case r.META:case r.NOFRAMES:case r.SCRIPT:case r.STYLE:case r.TEMPLATE:case r.TITLE:{u._err(e,m.abandonedHeadElementChild),u.openElements.push(u.headElement,r.HEAD),cu(u,e),u.openElements.remove(u.headElement);break}case r.HEAD:{u._err(e,m.misplacedStartTagForHeadElement);break}default:Be(u,e)}}function _1(u,e){switch(e.tagID){case r.BODY:case r.HTML:case r.BR:{Be(u,e);break}case r.TEMPLATE:{Gu(u,e);break}default:u._err(e,m.endTagWithoutMatchingOpenElement)}}function Be(u,e){u._insertFakeElement(b.BODY,r.BODY),u.insertionMode=f.IN_BODY,kt(u,e)}function kt(u,e){switch(e.type){case L.CHARACTER:{Jr(u,e);break}case L.WHITESPACE_CHARACTER:{Zr(u,e);break}case L.COMMENT:{ea(u,e);break}case L.START_TAG:{Q(u,e);break}case L.END_TAG:{wt(u,e);break}case L.EOF:{as(u,e);break}default:}}function Zr(u,e){u._reconstructActiveFormattingElements(),u._insertCharacters(e)}function Jr(u,e){u._reconstructActiveFormattingElements(),u._insertCharacters(e),u.framesetOk=!1}function N1(u,e){u.openElements.tmplCount===0&&u.treeAdapter.adoptAttributes(u.openElements.items[0],e.attrs)}function S1(u,e){let t=u.openElements.tryPeekProperlyNestedBodyElement();t&&u.openElements.tmplCount===0&&(u.framesetOk=!1,u.treeAdapter.adoptAttributes(t,e.attrs))}function I1(u,e){let t=u.openElements.tryPeekProperlyNestedBodyElement();u.framesetOk&&t&&(u.treeAdapter.detachNode(t),u.openElements.popAllUpToHtmlElement(),u._insertElement(e,E.HTML),u.insertionMode=f.IN_FRAMESET)}function C1(u,e){u.openElements.hasInButtonScope(r.P)&&u._closePElement(),u._insertElement(e,E.HTML)}function L1(u,e){u.openElements.hasInButtonScope(r.P)&&u._closePElement(),u.openElements.currentTagId!==void 0&&ae.has(u.openElements.currentTagId)&&u.openElements.pop(),u._insertElement(e,E.HTML)}function D1(u,e){u.openElements.hasInButtonScope(r.P)&&u._closePElement(),u._insertElement(e,E.HTML),u.skipNextNewLine=!0,u.framesetOk=!1}function O1(u,e){let t=u.openElements.tmplCount>0;(!u.formElement||t)&&(u.openElements.hasInButtonScope(r.P)&&u._closePElement(),u._insertElement(e,E.HTML),t||(u.formElement=u.openElements.current))}function R1(u,e){u.framesetOk=!1;let t=e.tagID;for(let a=u.openElements.stackTop;a>=0;a--){let s=u.openElements.tagIDs[a];if(t===r.LI&&s===r.LI||(t===r.DD||t===r.DT)&&(s===r.DD||s===r.DT)){u.openElements.generateImpliedEndTagsWithExclusion(s),u.openElements.popUntilTagNamePopped(s);break}if(s!==r.ADDRESS&&s!==r.DIV&&s!==r.P&&u._isSpecialElement(u.openElements.items[a],s))break}u.openElements.hasInButtonScope(r.P)&&u._closePElement(),u._insertElement(e,E.HTML)}function y1(u,e){u.openElements.hasInButtonScope(r.P)&&u._closePElement(),u._insertElement(e,E.HTML),u.tokenizer.state=W.PLAINTEXT}function P1(u,e){u.openElements.hasInScope(r.BUTTON)&&(u.openElements.generateImpliedEndTags(),u.openElements.popUntilTagNamePopped(r.BUTTON)),u._reconstructActiveFormattingElements(),u._insertElement(e,E.HTML),u.framesetOk=!1}function M1(u,e){let t=u.activeFormattingElements.getElementEntryInScopeWithTagName(b.A);t&&(aa(u,e),u.openElements.remove(t.element),u.activeFormattingElements.removeEntry(t)),u._reconstructActiveFormattingElements(),u._insertElement(e,E.HTML),u.activeFormattingElements.pushElement(u.openElements.current,e)}function k1(u,e){u._reconstructActiveFormattingElements(),u._insertElement(e,E.HTML),u.activeFormattingElements.pushElement(u.openElements.current,e)}function w1(u,e){u._reconstructActiveFormattingElements(),u.openElements.hasInScope(r.NOBR)&&(aa(u,e),u._reconstructActiveFormattingElements()),u._insertElement(e,E.HTML),u.activeFormattingElements.pushElement(u.openElements.current,e)}function B1(u,e){u._reconstructActiveFormattingElements(),u._insertElement(e,E.HTML),u.activeFormattingElements.insertMarker(),u.framesetOk=!1}function v1(u,e){u.treeAdapter.getDocumentMode(u.document)!==G.QUIRKS&&u.openElements.hasInButtonScope(r.P)&&u._closePElement(),u._insertElement(e,E.HTML),u.framesetOk=!1,u.insertionMode=f.IN_TABLE}function us(u,e){u._reconstructActiveFormattingElements(),u._appendElement(e,E.HTML),u.framesetOk=!1,e.ackSelfClosing=!0}function es(u){let e=Ie(u,fu.TYPE);return e!=null&&e.toLowerCase()===e1}function U1(u,e){u._reconstructActiveFormattingElements(),u._appendElement(e,E.HTML),es(e)||(u.framesetOk=!1),e.ackSelfClosing=!0}function H1(u,e){u._appendElement(e,E.HTML),e.ackSelfClosing=!0}function F1(u,e){u.openElements.hasInButtonScope(r.P)&&u._closePElement(),u._appendElement(e,E.HTML),u.framesetOk=!1,e.ackSelfClosing=!0}function q1(u,e){e.tagName=b.IMG,e.tagID=r.IMG,us(u,e)}function Y1(u,e){u._insertElement(e,E.HTML),u.skipNextNewLine=!0,u.tokenizer.state=W.RCDATA,u.originalInsertionMode=u.insertionMode,u.framesetOk=!1,u.insertionMode=f.TEXT}function V1(u,e){u.openElements.hasInButtonScope(r.P)&&u._closePElement(),u._reconstructActiveFormattingElements(),u.framesetOk=!1,u._switchToTextParsing(e,W.RAWTEXT)}function G1(u,e){u.framesetOk=!1,u._switchToTextParsing(e,W.RAWTEXT)}function jr(u,e){u._switchToTextParsing(e,W.RAWTEXT)}function W1(u,e){u._reconstructActiveFormattingElements(),u._insertElement(e,E.HTML),u.framesetOk=!1,u.insertionMode=u.insertionMode===f.IN_TABLE||u.insertionMode===f.IN_CAPTION||u.insertionMode===f.IN_TABLE_BODY||u.insertionMode===f.IN_ROW||u.insertionMode===f.IN_CELL?f.IN_SELECT_IN_TABLE:f.IN_SELECT}function Q1(u,e){u.openElements.currentTagId===r.OPTION&&u.openElements.pop(),u._reconstructActiveFormattingElements(),u._insertElement(e,E.HTML)}function X1(u,e){u.openElements.hasInScope(r.RUBY)&&u.openElements.generateImpliedEndTags(),u._insertElement(e,E.HTML)}function K1(u,e){u.openElements.hasInScope(r.RUBY)&&u.openElements.generateImpliedEndTagsWithExclusion(r.RTC),u._insertElement(e,E.HTML)}function j1(u,e){u._reconstructActiveFormattingElements(),Dt(e),Oe(e),e.selfClosing?u._appendElement(e,E.MATHML):u._insertElement(e,E.MATHML),e.ackSelfClosing=!0}function z1(u,e){u._reconstructActiveFormattingElements(),Ot(e),Oe(e),e.selfClosing?u._appendElement(e,E.SVG):u._insertElement(e,E.SVG),e.ackSelfClosing=!0}function zr(u,e){u._reconstructActiveFormattingElements(),u._insertElement(e,E.HTML)}function Q(u,e){switch(e.tagID){case r.I:case r.S:case r.B:case r.U:case r.EM:case r.TT:case r.BIG:case r.CODE:case r.FONT:case r.SMALL:case r.STRIKE:case r.STRONG:{k1(u,e);break}case r.A:{M1(u,e);break}case r.H1:case r.H2:case r.H3:case r.H4:case r.H5:case r.H6:{L1(u,e);break}case r.P:case r.DL:case r.OL:case r.UL:case r.DIV:case r.DIR:case r.NAV:case r.MAIN:case r.MENU:case r.ASIDE:case r.CENTER:case r.FIGURE:case r.FOOTER:case r.HEADER:case r.HGROUP:case r.DIALOG:case r.DETAILS:case r.ADDRESS:case r.ARTICLE:case r.SEARCH:case r.SECTION:case r.SUMMARY:case r.FIELDSET:case r.BLOCKQUOTE:case r.FIGCAPTION:{C1(u,e);break}case r.LI:case r.DD:case r.DT:{R1(u,e);break}case r.BR:case r.IMG:case r.WBR:case r.AREA:case r.EMBED:case r.KEYGEN:{us(u,e);break}case r.HR:{F1(u,e);break}case r.RB:case r.RTC:{X1(u,e);break}case r.RT:case r.RP:{K1(u,e);break}case r.PRE:case r.LISTING:{D1(u,e);break}case r.XMP:{V1(u,e);break}case r.SVG:{z1(u,e);break}case r.HTML:{N1(u,e);break}case r.BASE:case r.LINK:case r.META:case r.STYLE:case r.TITLE:case r.SCRIPT:case r.BGSOUND:case r.BASEFONT:case r.TEMPLATE:{cu(u,e);break}case r.BODY:{S1(u,e);break}case r.FORM:{O1(u,e);break}case r.NOBR:{w1(u,e);break}case r.MATH:{j1(u,e);break}case r.TABLE:{v1(u,e);break}case r.INPUT:{U1(u,e);break}case r.PARAM:case r.TRACK:case r.SOURCE:{H1(u,e);break}case r.IMAGE:{q1(u,e);break}case r.BUTTON:{P1(u,e);break}case r.APPLET:case r.OBJECT:case r.MARQUEE:{B1(u,e);break}case r.IFRAME:{G1(u,e);break}case r.SELECT:{W1(u,e);break}case r.OPTION:case r.OPTGROUP:{Q1(u,e);break}case r.NOEMBED:case r.NOFRAMES:{jr(u,e);break}case r.FRAMESET:{I1(u,e);break}case r.TEXTAREA:{Y1(u,e);break}case r.NOSCRIPT:{u.options.scriptingEnabled?jr(u,e):zr(u,e);break}case r.PLAINTEXT:{y1(u,e);break}case r.COL:case r.TH:case r.TD:case r.TR:case r.HEAD:case r.FRAME:case r.TBODY:case r.TFOOT:case r.THEAD:case r.CAPTION:case r.COLGROUP:break;default:zr(u,e)}}function $1(u,e){if(u.openElements.hasInScope(r.BODY)&&(u.insertionMode=f.AFTER_BODY,u.options.sourceCodeLocationInfo)){let t=u.openElements.tryPeekProperlyNestedBodyElement();t&&u._setEndLocation(t,e)}}function Z1(u,e){u.openElements.hasInScope(r.BODY)&&(u.insertionMode=f.AFTER_BODY,fs(u,e))}function J1(u,e){let t=e.tagID;u.openElements.hasInScope(t)&&(u.openElements.generateImpliedEndTags(),u.openElements.popUntilTagNamePopped(t))}function uo(u){let e=u.openElements.tmplCount>0,{formElement:t}=u;e||(u.formElement=null),(t||e)&&u.openElements.hasInScope(r.FORM)&&(u.openElements.generateImpliedEndTags(),e?u.openElements.popUntilTagNamePopped(r.FORM):t&&u.openElements.remove(t))}function eo(u){u.openElements.hasInButtonScope(r.P)||u._insertFakeElement(b.P,r.P),u._closePElement()}function to(u){u.openElements.hasInListItemScope(r.LI)&&(u.openElements.generateImpliedEndTagsWithExclusion(r.LI),u.openElements.popUntilTagNamePopped(r.LI))}function ao(u,e){let t=e.tagID;u.openElements.hasInScope(t)&&(u.openElements.generateImpliedEndTagsWithExclusion(t),u.openElements.popUntilTagNamePopped(t))}function ro(u){u.openElements.hasNumberedHeaderInScope()&&(u.openElements.generateImpliedEndTags(),u.openElements.popUntilNumberedHeaderPopped())}function so(u,e){let t=e.tagID;u.openElements.hasInScope(t)&&(u.openElements.generateImpliedEndTags(),u.openElements.popUntilTagNamePopped(t),u.activeFormattingElements.clearToLastMarker())}function io(u){u._reconstructActiveFormattingElements(),u._insertFakeElement(b.BR,r.BR),u.openElements.pop(),u.framesetOk=!1}function ts(u,e){let t=e.tagName,a=e.tagID;for(let s=u.openElements.stackTop;s>0;s--){let i=u.openElements.items[s],n=u.openElements.tagIDs[s];if(a===n&&(a!==r.UNKNOWN||u.treeAdapter.getTagName(i)===t)){u.openElements.generateImpliedEndTagsWithExclusion(a),u.openElements.stackTop>=s&&u.openElements.shortenToLength(s);break}if(u._isSpecialElement(i,n))break}}function wt(u,e){switch(e.tagID){case r.A:case r.B:case r.I:case r.S:case r.U:case r.EM:case r.TT:case r.BIG:case r.CODE:case r.FONT:case r.NOBR:case r.SMALL:case r.STRIKE:case r.STRONG:{aa(u,e);break}case r.P:{eo(u);break}case r.DL:case r.UL:case r.OL:case r.DIR:case r.DIV:case r.NAV:case r.PRE:case r.MAIN:case r.MENU:case r.ASIDE:case r.BUTTON:case r.CENTER:case r.FIGURE:case r.FOOTER:case r.HEADER:case r.HGROUP:case r.DIALOG:case r.ADDRESS:case r.ARTICLE:case r.DETAILS:case r.SEARCH:case r.SECTION:case r.SUMMARY:case r.LISTING:case r.FIELDSET:case r.BLOCKQUOTE:case r.FIGCAPTION:{J1(u,e);break}case r.LI:{to(u);break}case r.DD:case r.DT:{ao(u,e);break}case r.H1:case r.H2:case r.H3:case r.H4:case r.H5:case r.H6:{ro(u);break}case r.BR:{io(u);break}case r.BODY:{$1(u,e);break}case r.HTML:{Z1(u,e);break}case r.FORM:{uo(u);break}case r.APPLET:case r.OBJECT:case r.MARQUEE:{so(u,e);break}case r.TEMPLATE:{Gu(u,e);break}default:ts(u,e)}}function as(u,e){u.tmplInsertionModeStack.length>0?ds(u,e):ra(u,e)}function no(u,e){var t;e.tagID===r.SCRIPT&&((t=u.scriptHandler)===null||t===void 0||t.call(u,u.openElements.current)),u.openElements.pop(),u.insertionMode=u.originalInsertionMode}function co(u,e){u._err(e,m.eofInElementThatCanContainOnlyText),u.openElements.pop(),u.insertionMode=u.originalInsertionMode,u.onEof(e)}function ua(u,e){if(u.openElements.currentTagId!==void 0&&$r.has(u.openElements.currentTagId))switch(u.pendingCharacterTokens.length=0,u.hasNonWhitespacePendingCharacterToken=!1,u.originalInsertionMode=u.insertionMode,u.insertionMode=f.IN_TABLE_TEXT,e.type){case L.CHARACTER:{ss(u,e);break}case L.WHITESPACE_CHARACTER:{rs(u,e);break}}else Ue(u,e)}function oo(u,e){u.openElements.clearBackToTableContext(),u.activeFormattingElements.insertMarker(),u._insertElement(e,E.HTML),u.insertionMode=f.IN_CAPTION}function fo(u,e){u.openElements.clearBackToTableContext(),u._insertElement(e,E.HTML),u.insertionMode=f.IN_COLUMN_GROUP}function lo(u,e){u.openElements.clearBackToTableContext(),u._insertFakeElement(b.COLGROUP,r.COLGROUP),u.insertionMode=f.IN_COLUMN_GROUP,sa(u,e)}function bo(u,e){u.openElements.clearBackToTableContext(),u._insertElement(e,E.HTML),u.insertionMode=f.IN_TABLE_BODY}function ho(u,e){u.openElements.clearBackToTableContext(),u._insertFakeElement(b.TBODY,r.TBODY),u.insertionMode=f.IN_TABLE_BODY,Bt(u,e)}function mo(u,e){u.openElements.hasInTableScope(r.TABLE)&&(u.openElements.popUntilTagNamePopped(r.TABLE),u._resetInsertionMode(),u._processStartTag(e))}function Eo(u,e){es(e)?u._appendElement(e,E.HTML):Ue(u,e),e.ackSelfClosing=!0}function po(u,e){!u.formElement&&u.openElements.tmplCount===0&&(u._insertElement(e,E.HTML),u.formElement=u.openElements.current,u.openElements.pop())}function se(u,e){switch(e.tagID){case r.TD:case r.TH:case r.TR:{ho(u,e);break}case r.STYLE:case r.SCRIPT:case r.TEMPLATE:{cu(u,e);break}case r.COL:{lo(u,e);break}case r.FORM:{po(u,e);break}case r.TABLE:{mo(u,e);break}case r.TBODY:case r.TFOOT:case r.THEAD:{bo(u,e);break}case r.INPUT:{Eo(u,e);break}case r.CAPTION:{oo(u,e);break}case r.COLGROUP:{fo(u,e);break}default:Ue(u,e)}}function ve(u,e){switch(e.tagID){case r.TABLE:{u.openElements.hasInTableScope(r.TABLE)&&(u.openElements.popUntilTagNamePopped(r.TABLE),u._resetInsertionMode());break}case r.TEMPLATE:{Gu(u,e);break}case r.BODY:case r.CAPTION:case r.COL:case r.COLGROUP:case r.HTML:case r.TBODY:case r.TD:case r.TFOOT:case r.TH:case r.THEAD:case r.TR:break;default:Ue(u,e)}}function Ue(u,e){let t=u.fosterParentingEnabled;u.fosterParentingEnabled=!0,kt(u,e),u.fosterParentingEnabled=t}function rs(u,e){u.pendingCharacterTokens.push(e)}function ss(u,e){u.pendingCharacterTokens.push(e),u.hasNonWhitespacePendingCharacterToken=!0}function ye(u,e){let t=0;if(u.hasNonWhitespacePendingCharacterToken)for(;t<u.pendingCharacterTokens.length;t++)Ue(u,u.pendingCharacterTokens[t]);else for(;t<u.pendingCharacterTokens.length;t++)u._insertCharacters(u.pendingCharacterTokens[t]);u.insertionMode=u.originalInsertionMode,u._processToken(e)}var is=new Set([r.CAPTION,r.COL,r.COLGROUP,r.TBODY,r.TD,r.TFOOT,r.TH,r.THEAD,r.TR]);function To(u,e){let t=e.tagID;is.has(t)?u.openElements.hasInTableScope(r.CAPTION)&&(u.openElements.generateImpliedEndTags(),u.openElements.popUntilTagNamePopped(r.CAPTION),u.activeFormattingElements.clearToLastMarker(),u.insertionMode=f.IN_TABLE,se(u,e)):Q(u,e)}function go(u,e){let t=e.tagID;switch(t){case r.CAPTION:case r.TABLE:{u.openElements.hasInTableScope(r.CAPTION)&&(u.openElements.generateImpliedEndTags(),u.openElements.popUntilTagNamePopped(r.CAPTION),u.activeFormattingElements.clearToLastMarker(),u.insertionMode=f.IN_TABLE,t===r.TABLE&&ve(u,e));break}case r.BODY:case r.COL:case r.COLGROUP:case r.HTML:case r.TBODY:case r.TD:case r.TFOOT:case r.TH:case r.THEAD:case r.TR:break;default:wt(u,e)}}function sa(u,e){switch(e.tagID){case r.HTML:{Q(u,e);break}case r.COL:{u._appendElement(e,E.HTML),e.ackSelfClosing=!0;break}case r.TEMPLATE:{cu(u,e);break}default:Pt(u,e)}}function xo(u,e){switch(e.tagID){case r.COLGROUP:{u.openElements.currentTagId===r.COLGROUP&&(u.openElements.pop(),u.insertionMode=f.IN_TABLE);break}case r.TEMPLATE:{Gu(u,e);break}case r.COL:break;default:Pt(u,e)}}function Pt(u,e){u.openElements.currentTagId===r.COLGROUP&&(u.openElements.pop(),u.insertionMode=f.IN_TABLE,u._processToken(e))}function Bt(u,e){switch(e.tagID){case r.TR:{u.openElements.clearBackToTableBodyContext(),u._insertElement(e,E.HTML),u.insertionMode=f.IN_ROW;break}case r.TH:case r.TD:{u.openElements.clearBackToTableBodyContext(),u._insertFakeElement(b.TR,r.TR),u.insertionMode=f.IN_ROW,vt(u,e);break}case r.CAPTION:case r.COL:case r.COLGROUP:case r.TBODY:case r.TFOOT:case r.THEAD:{u.openElements.hasTableBodyContextInTableScope()&&(u.openElements.clearBackToTableBodyContext(),u.openElements.pop(),u.insertionMode=f.IN_TABLE,se(u,e));break}default:se(u,e)}}function ta(u,e){let t=e.tagID;switch(e.tagID){case r.TBODY:case r.TFOOT:case r.THEAD:{u.openElements.hasInTableScope(t)&&(u.openElements.clearBackToTableBodyContext(),u.openElements.pop(),u.insertionMode=f.IN_TABLE);break}case r.TABLE:{u.openElements.hasTableBodyContextInTableScope()&&(u.openElements.clearBackToTableBodyContext(),u.openElements.pop(),u.insertionMode=f.IN_TABLE,ve(u,e));break}case r.BODY:case r.CAPTION:case r.COL:case r.COLGROUP:case r.HTML:case r.TD:case r.TH:case r.TR:break;default:ve(u,e)}}function vt(u,e){switch(e.tagID){case r.TH:case r.TD:{u.openElements.clearBackToTableRowContext(),u._insertElement(e,E.HTML),u.insertionMode=f.IN_CELL,u.activeFormattingElements.insertMarker();break}case r.CAPTION:case r.COL:case r.COLGROUP:case r.TBODY:case r.TFOOT:case r.THEAD:case r.TR:{u.openElements.hasInTableScope(r.TR)&&(u.openElements.clearBackToTableRowContext(),u.openElements.pop(),u.insertionMode=f.IN_TABLE_BODY,Bt(u,e));break}default:se(u,e)}}function ns(u,e){switch(e.tagID){case r.TR:{u.openElements.hasInTableScope(r.TR)&&(u.openElements.clearBackToTableRowContext(),u.openElements.pop(),u.insertionMode=f.IN_TABLE_BODY);break}case r.TABLE:{u.openElements.hasInTableScope(r.TR)&&(u.openElements.clearBackToTableRowContext(),u.openElements.pop(),u.insertionMode=f.IN_TABLE_BODY,ta(u,e));break}case r.TBODY:case r.TFOOT:case r.THEAD:{(u.openElements.hasInTableScope(e.tagID)||u.openElements.hasInTableScope(r.TR))&&(u.openElements.clearBackToTableRowContext(),u.openElements.pop(),u.insertionMode=f.IN_TABLE_BODY,ta(u,e));break}case r.BODY:case r.CAPTION:case r.COL:case r.COLGROUP:case r.HTML:case r.TD:case r.TH:break;default:ve(u,e)}}function Ao(u,e){let t=e.tagID;is.has(t)?(u.openElements.hasInTableScope(r.TD)||u.openElements.hasInTableScope(r.TH))&&(u._closeTableCell(),vt(u,e)):Q(u,e)}function _o(u,e){let t=e.tagID;switch(t){case r.TD:case r.TH:{u.openElements.hasInTableScope(t)&&(u.openElements.generateImpliedEndTags(),u.openElements.popUntilTagNamePopped(t),u.activeFormattingElements.clearToLastMarker(),u.insertionMode=f.IN_ROW);break}case r.TABLE:case r.TBODY:case r.TFOOT:case r.THEAD:case r.TR:{u.openElements.hasInTableScope(t)&&(u._closeTableCell(),ns(u,e));break}case r.BODY:case r.CAPTION:case r.COL:case r.COLGROUP:case r.HTML:break;default:wt(u,e)}}function cs(u,e){switch(e.tagID){case r.HTML:{Q(u,e);break}case r.OPTION:{u.openElements.currentTagId===r.OPTION&&u.openElements.pop(),u._insertElement(e,E.HTML);break}case r.OPTGROUP:{u.openElements.currentTagId===r.OPTION&&u.openElements.pop(),u.openElements.currentTagId===r.OPTGROUP&&u.openElements.pop(),u._insertElement(e,E.HTML);break}case r.HR:{u.openElements.currentTagId===r.OPTION&&u.openElements.pop(),u.openElements.currentTagId===r.OPTGROUP&&u.openElements.pop(),u._appendElement(e,E.HTML),e.ackSelfClosing=!0;break}case r.INPUT:case r.KEYGEN:case r.TEXTAREA:case r.SELECT:{u.openElements.hasInSelectScope(r.SELECT)&&(u.openElements.popUntilTagNamePopped(r.SELECT),u._resetInsertionMode(),e.tagID!==r.SELECT&&u._processStartTag(e));break}case r.SCRIPT:case r.TEMPLATE:{cu(u,e);break}default:}}function os(u,e){switch(e.tagID){case r.OPTGROUP:{u.openElements.stackTop>0&&u.openElements.currentTagId===r.OPTION&&u.openElements.tagIDs[u.openElements.stackTop-1]===r.OPTGROUP&&u.openElements.pop(),u.openElements.currentTagId===r.OPTGROUP&&u.openElements.pop();break}case r.OPTION:{u.openElements.currentTagId===r.OPTION&&u.openElements.pop();break}case r.SELECT:{u.openElements.hasInSelectScope(r.SELECT)&&(u.openElements.popUntilTagNamePopped(r.SELECT),u._resetInsertionMode());break}case r.TEMPLATE:{Gu(u,e);break}default:}}function No(u,e){let t=e.tagID;t===r.CAPTION||t===r.TABLE||t===r.TBODY||t===r.TFOOT||t===r.THEAD||t===r.TR||t===r.TD||t===r.TH?(u.openElements.popUntilTagNamePopped(r.SELECT),u._resetInsertionMode(),u._processStartTag(e)):cs(u,e)}function So(u,e){let t=e.tagID;t===r.CAPTION||t===r.TABLE||t===r.TBODY||t===r.TFOOT||t===r.THEAD||t===r.TR||t===r.TD||t===r.TH?u.openElements.hasInTableScope(t)&&(u.openElements.popUntilTagNamePopped(r.SELECT),u._resetInsertionMode(),u.onEndTag(e)):os(u,e)}function Io(u,e){switch(e.tagID){case r.BASE:case r.BASEFONT:case r.BGSOUND:case r.LINK:case r.META:case r.NOFRAMES:case r.SCRIPT:case r.STYLE:case r.TEMPLATE:case r.TITLE:{cu(u,e);break}case r.CAPTION:case r.COLGROUP:case r.TBODY:case r.TFOOT:case r.THEAD:{u.tmplInsertionModeStack[0]=f.IN_TABLE,u.insertionMode=f.IN_TABLE,se(u,e);break}case r.COL:{u.tmplInsertionModeStack[0]=f.IN_COLUMN_GROUP,u.insertionMode=f.IN_COLUMN_GROUP,sa(u,e);break}case r.TR:{u.tmplInsertionModeStack[0]=f.IN_TABLE_BODY,u.insertionMode=f.IN_TABLE_BODY,Bt(u,e);break}case r.TD:case r.TH:{u.tmplInsertionModeStack[0]=f.IN_ROW,u.insertionMode=f.IN_ROW,vt(u,e);break}default:u.tmplInsertionModeStack[0]=f.IN_BODY,u.insertionMode=f.IN_BODY,Q(u,e)}}function Co(u,e){e.tagID===r.TEMPLATE&&Gu(u,e)}function ds(u,e){u.openElements.tmplCount>0?(u.openElements.popUntilTagNamePopped(r.TEMPLATE),u.activeFormattingElements.clearToLastMarker(),u.tmplInsertionModeStack.shift(),u._resetInsertionMode(),u.onEof(e)):ra(u,e)}function Lo(u,e){e.tagID===r.HTML?Q(u,e):Mt(u,e)}function fs(u,e){var t;if(e.tagID===r.HTML){if(u.fragmentContext||(u.insertionMode=f.AFTER_AFTER_BODY),u.options.sourceCodeLocationInfo&&u.openElements.tagIDs[0]===r.HTML){u._setEndLocation(u.openElements.items[0],e);let a=u.openElements.items[1];a&&!(!((t=u.treeAdapter.getNodeSourceCodeLocation(a))===null||t===void 0)&&t.endTag)&&u._setEndLocation(a,e)}}else Mt(u,e)}function Mt(u,e){u.insertionMode=f.IN_BODY,kt(u,e)}function Do(u,e){switch(e.tagID){case r.HTML:{Q(u,e);break}case r.FRAMESET:{u._insertElement(e,E.HTML);break}case r.FRAME:{u._appendElement(e,E.HTML),e.ackSelfClosing=!0;break}case r.NOFRAMES:{cu(u,e);break}default:}}function Oo(u,e){e.tagID===r.FRAMESET&&!u.openElements.isRootHtmlElementCurrent()&&(u.openElements.pop(),!u.fragmentContext&&u.openElements.currentTagId!==r.FRAMESET&&(u.insertionMode=f.AFTER_FRAMESET))}function Ro(u,e){switch(e.tagID){case r.HTML:{Q(u,e);break}case r.NOFRAMES:{cu(u,e);break}default:}}function yo(u,e){e.tagID===r.HTML&&(u.insertionMode=f.AFTER_AFTER_FRAMESET)}function Po(u,e){e.tagID===r.HTML?Q(u,e):yt(u,e)}function yt(u,e){u.insertionMode=f.IN_BODY,kt(u,e)}function Mo(u,e){switch(e.tagID){case r.HTML:{Q(u,e);break}case r.NOFRAMES:{cu(u,e);break}default:}}function ko(u,e){e.chars=M,u._insertCharacters(e)}function wo(u,e){u._insertCharacters(e),u.framesetOk=!1}function ls(u){for(;u.treeAdapter.getNamespaceURI(u.openElements.current)!==E.HTML&&u.openElements.currentTagId!==void 0&&!u._isIntegrationPoint(u.openElements.currentTagId,u.openElements.current);)u.openElements.pop()}function Bo(u,e){if($0(e))ls(u),u._startTagOutsideForeignContent(e);else{let t=u._getAdjustedCurrentElement(),a=u.treeAdapter.getNamespaceURI(t);a===E.MATHML?Dt(e):a===E.SVG&&(Z0(e),Ot(e)),Oe(e),e.selfClosing?u._appendElement(e,a):u._insertElement(e,a),e.ackSelfClosing=!0}}function vo(u,e){if(e.tagID===r.P||e.tagID===r.BR){ls(u),u._endTagOutsideForeignContent(e);return}for(let t=u.openElements.stackTop;t>0;t--){let a=u.openElements.items[t];if(u.treeAdapter.getNamespaceURI(a)===E.HTML){u._endTagOutsideForeignContent(e);break}let s=u.treeAdapter.getTagName(a);if(s.toLowerCase()===e.tagName){e.tagName=s,u.openElements.shortenToLength(t);break}}}var Gl=String.prototype.codePointAt==null?(u,e)=>(u.charCodeAt(e)&64512)===55296?(u.charCodeAt(e)-55296)*1024+u.charCodeAt(e+1)-56320+65536:u.charCodeAt(e):(u,e)=>u.codePointAt(e);function bs(u,e){return function(a){let s,i=0,n="";for(;s=u.exec(a);)i!==s.index&&(n+=a.substring(i,s.index)),n+=e.get(s[0].charCodeAt(0)),i=s.index+1;return n+a.substring(i)}}var hs=bs(/["&\u00A0]/g,new Map([[34,"&quot;"],[38,"&amp;"],[160,"&nbsp;"]])),ms=bs(/[&<>\u00A0]/g,new Map([[38,"&amp;"],[60,"&lt;"],[62,"&gt;"],[160,"&nbsp;"]]));var Uo=new Set([b.AREA,b.BASE,b.BASEFONT,b.BGSOUND,b.BR,b.COL,b.EMBED,b.FRAME,b.HR,b.IMG,b.INPUT,b.KEYGEN,b.LINK,b.META,b.PARAM,b.SOURCE,b.TRACK,b.WBR]);function Ho(u,e){return e.treeAdapter.isElementNode(u)&&e.treeAdapter.getNamespaceURI(u)===E.HTML&&Uo.has(e.treeAdapter.getTagName(u))}var Fo={treeAdapter:su,scriptingEnabled:!0};function ia(u,e){let t={...Fo,...e};return Es(u,t)}function qo(u,e){let t="",a=e.treeAdapter.isElementNode(u)&&e.treeAdapter.getTagName(u)===b.TEMPLATE&&e.treeAdapter.getNamespaceURI(u)===E.HTML?e.treeAdapter.getTemplateContent(u):u,s=e.treeAdapter.getChildNodes(a);if(s)for(let i of s)t+=Es(i,e);return t}function Es(u,e){return e.treeAdapter.isElementNode(u)?Yo(u,e):e.treeAdapter.isTextNode(u)?Go(u,e):e.treeAdapter.isCommentNode(u)?Wo(u,e):e.treeAdapter.isDocumentTypeNode(u)?Qo(u,e):""}function Yo(u,e){let t=e.treeAdapter.getTagName(u);return`<${t}${Vo(u,e)}>${Ho(u,e)?"":`${qo(u,e)}</${t}>`}`}function Vo(u,{treeAdapter:e}){let t="";for(let a of e.getAttrList(u)){if(t+=" ",a.namespace)switch(a.namespace){case E.XML:{t+=`xml:${a.name}`;break}case E.XMLNS:{a.name!=="xmlns"&&(t+="xmlns:"),t+=a.name;break}case E.XLINK:{t+=`xlink:${a.name}`;break}default:t+=`${a.prefix}:${a.name}`}else t+=a.name;t+=`="${hs(a.value)}"`}return t}function Go(u,e){let{treeAdapter:t}=e,a=t.getTextNodeContent(u),s=t.getParentNode(u),i=s&&t.isElementNode(s)&&t.getTagName(s);return i&&t.getNamespaceURI(s)===E.HTML&&z0(i,e.scriptingEnabled)?a:ms(a)}function Wo(u,{treeAdapter:e}){return`<!--${e.getCommentNodeContent(u)}-->`}function Qo(u,{treeAdapter:e}){return`<!DOCTYPE ${e.getDocumentTypeNodeName(u)}>`}function ps(u,e){return re.parse(u,e)}function Ts(u,e,t){typeof u=="string"&&(t=e,e=u,u=null);let a=re.getFragmentParser(u,t);return a.tokenizer.write(e,!0),a.getFragment()}function gs(u){let e=u.includes('"')?"'":'"';return e+u+e}function Xo(u,e,t){let a="!DOCTYPE ";return u&&(a+=u),e?a+=` PUBLIC ${gs(e)}`:t&&(a+=" SYSTEM"),t&&(a+=` ${gs(t)}`),a}var gu={isCommentNode:Uu,isElementNode:N,isTextNode:K,createDocument(){let u=new J([]);return u["x-mode"]=Ce.DOCUMENT_MODE.NO_QUIRKS,u},createDocumentFragment(){return new J([])},createElement(u,e,t){let a=Object.create(null),s=Object.create(null),i=Object.create(null);for(let d=0;d<t.length;d++){let l=t[d].name;a[l]=t[d].value,s[l]=t[d].namespace,i[l]=t[d].prefix}let n=new vu(u,a,[]);return n.namespace=e,n["x-attribsNamespace"]=s,n["x-attribsPrefix"]=i,n},createCommentNode(u){return new wu(u)},createTextNode(u){return new ou(u)},appendChild(u,e){let t=u.children[u.children.length-1];t&&(t.next=e,e.prev=t),u.children.push(e),e.parent=u},insertBefore(u,e,t){let a=u.children.indexOf(t),{prev:s}=t;s&&(s.next=e,e.prev=s),t.prev=e,e.next=t,u.children.splice(a,0,e),e.parent=u},setTemplateContent(u,e){gu.appendChild(u,e)},getTemplateContent(u){return u.children[0]},setDocumentType(u,e,t,a){let s=Xo(e,t,a),i=u.children.find(n=>Ye(n)&&n.name==="!doctype");i?i.data=s!=null?s:null:(i=new Bu("!doctype",s),gu.appendChild(u,i)),i["x-name"]=e,i["x-publicId"]=t,i["x-systemId"]=a},setDocumentMode(u,e){u["x-mode"]=e},getDocumentMode(u){return u["x-mode"]},detachNode(u){if(u.parent){let e=u.parent.children.indexOf(u),{prev:t,next:a}=u;u.prev=null,u.next=null,t&&(t.next=a),a&&(a.prev=t),u.parent.children.splice(e,1),u.parent=null}},insertText(u,e){let t=u.children[u.children.length-1];t&&K(t)?t.data+=e:gu.appendChild(u,gu.createTextNode(e))},insertTextBefore(u,e,t){let a=u.children[u.children.indexOf(t)-1];a&&K(a)?a.data+=e:gu.insertBefore(u,gu.createTextNode(e),t)},adoptAttributes(u,e){for(let t=0;t<e.length;t++){let a=e[t].name;u.attribs[a]===void 0&&(u.attribs[a]=e[t].value,u["x-attribsNamespace"][a]=e[t].namespace,u["x-attribsPrefix"][a]=e[t].prefix)}},getFirstChild(u){return u.children[0]},getChildNodes(u){return u.children},getParentNode(u){return u.parent},getAttrList(u){return u.attributes},getTagName(u){return u.name},getNamespaceURI(u){return u.namespace},getTextNodeContent(u){return u.data},getCommentNodeContent(u){return u.data},getDocumentTypeNodeName(u){var e;return(e=u["x-name"])!==null&&e!==void 0?e:""},getDocumentTypeNodePublicId(u){var e;return(e=u["x-publicId"])!==null&&e!==void 0?e:""},getDocumentTypeNodeSystemId(u){var e;return(e=u["x-systemId"])!==null&&e!==void 0?e:""},isDocumentTypeNode(u){return Ye(u)&&u.name==="!doctype"},setNodeSourceCodeLocation(u,e){e&&(u.startIndex=e.startOffset,u.endIndex=e.endOffset),u.sourceCodeLocation=e},getNodeSourceCodeLocation(u){return u.sourceCodeLocation},updateNodeSourceCodeLocation(u,e){e.endOffset!=null&&(u.endIndex=e.endOffset),u.sourceCodeLocation={...u.sourceCodeLocation,...e}}};function xs(u,e,t,a){var s;return(s=e.treeAdapter)!==null&&s!==void 0||(e.treeAdapter=gu),e.scriptingEnabled!==!1&&(e.scriptingEnabled=!0),t?ps(u,e):Ts(a,u,e)}var Ko={treeAdapter:gu};function As(u){let e="length"in u?u:[u];for(let a=0;a<e.length;a+=1){let s=e[a];uu(s)&&Array.prototype.splice.call(e,a,1,...s.children)}let t="";for(let a=0;a<e.length;a+=1){let s=e[a];t+=ia(s,Ko)}return t}var jo=Ar((u,e,t,a)=>e._useHtmlParser2?Va(u,e):xs(u,e,t,a)),_s=Dr(jo,(u,e)=>e._useHtmlParser2?Xe(u,e):As(u));var $o={enableDebugMode:!1},na=class{constructor(){this.id="reddit-post-parser";this.name="Paste Reddit Posts from Copied Subreddit Directory";this.description="Parse Reddit HTML from clipboard and paste formatted posts"}async execute(e){try{let t=await navigator.clipboard.readText();if(!t){new v.Notice("Clipboard is empty");return}e.settings.enableDebugMode&&console.log("Reddit parser: Processing clipboard content length:",t.length);let a=this.extractRedditPosts(t);if(a.length===0){new v.Notice("No Reddit posts found in clipboard content. Make sure you copied HTML from a Reddit subreddit page.");return}let s=a.join(`
`);e.settings.enableDebugMode&&console.log("Reddit parser: Generated output:",s),await navigator.clipboard.writeText(s);let i=e.app.workspace.getActiveViewOfType(v.MarkdownView);i?(i.editor.replaceSelection(s),new v.Notice(`Parsed and pasted ${a.length} Reddit posts`)):new v.Notice(`Parsed ${a.length} Reddit posts and copied to clipboard`)}catch(t){new v.Notice(`Error parsing Reddit posts: ${t.message}`),console.error("Reddit parser error:",t)}}extractRedditPosts(e){let t=_s(e),a=t("shreddit-post"),s=[];return a.each((i,n)=>{let d=t(n),l=d.attr("post-title"),h=d.attr("permalink"),p=d.attr("created-timestamp");if(!l||!h||!p)return;let x=h.match(/(\/r\/[^/]+\/comments\/[^/]+\/)/);if(!x)return;let A=`https://www.reddit.com${x[1]}`,S=new Date(p),_=4*60*60*1e3,D=new Date(S.getTime()-_),k=Au=>Au.toString().padStart(2,"0"),xu=`${D.getFullYear()}${k(D.getMonth()+1)}${k(D.getDate())}${k(D.getHours())}${k(D.getMinutes())}`;s.push(`- ${xu} - [${l}](${A})`)}),s}},ca=class extends v.FuzzySuggestModal{constructor(t,a){super(t);this.onSelect=a,this.setPlaceholder("Select a note to sort timestamps in...")}getItems(){return this.app.vault.getMarkdownFiles()}getItemText(t){return t.path}onChooseItem(t,a){console.log("NoteSelectionModal: File selected:",t.path),this.close(),this.onSelect(t)}},oa=class extends v.FuzzySuggestModal{constructor(t,a,s){super(t);this.sections=[];this.sectionsLoaded=!1;this.file=a,this.onSelect=s,this.setPlaceholder("Loading sections...")}async onOpen(){super.onOpen(),console.log("SectionSelectionModal: Opening modal for file:",this.file.path),await this.loadSections(),console.log("SectionSelectionModal: Loaded",this.sections.length,"sections"),this.setPlaceholder("Select a section to sort timestamps in...")}async loadSections(){try{let a=(await this.app.vault.read(this.file)).split(`
`);this.sections=[],a.forEach((s,i)=>{let n=s.match(/^(#{1,6})\s+(.+)$/);if(n){let d=n[1].length,l=n[2];this.sections.push({heading:`${"  ".repeat(d-1)}${l}`,line:i})}}),this.sections.unshift({heading:"\u{1F4C4} Entire Document",line:-1}),this.sectionsLoaded=!0}catch(t){console.error("Error loading sections:",t),this.sections=[{heading:"\u{1F4C4} Entire Document",line:-1}],this.sectionsLoaded=!0}}getItems(){return this.sectionsLoaded?this.sections:[]}getItemText(t){return t.heading}onChooseItem(t,a){console.log("SectionSelectionModal: Section selected:",t.heading),this.close(),this.onSelect(t)}},Ut=class{static parseTimestamp(e){let t=e.match(/^-\s+(\d{8}|\d{12})\s+-\s+/);if(!t)return null;let a=t[1];if(a.length===8){let s=parseInt(a.substring(0,4)),i=parseInt(a.substring(4,6))-1,n=parseInt(a.substring(6,8));return new Date(s,i,n)}else if(a.length===12){let s=parseInt(a.substring(0,4)),i=parseInt(a.substring(4,6))-1,n=parseInt(a.substring(6,8)),d=parseInt(a.substring(8,10)),l=parseInt(a.substring(10,12));return new Date(s,i,n,d,l)}return null}static sortTimestampedEntries(e){return e.sort((t,a)=>!t.timestamp&&!a.timestamp?t.originalIndex-a.originalIndex:t.timestamp?a.timestamp?a.timestamp.getTime()-t.timestamp.getTime():1:-1)}},da=class{constructor(){this.id="sort-timestamped-list";this.name="Sort Timestamped List";this.description="Sort timestamped entries in a selected note section chronologically"}async execute(e){try{e.settings.enableDebugMode&&console.log("SortTimestampedListCommand: Starting execution");let t=await this.selectNote(e.app);if(!t){e.settings.enableDebugMode&&console.log("SortTimestampedListCommand: Note selection cancelled");return}e.settings.enableDebugMode&&console.log("SortTimestampedListCommand: Selected file:",t.path);let a=await this.selectSection(e.app,t);if(!a){e.settings.enableDebugMode&&console.log("SortTimestampedListCommand: Section selection cancelled");return}e.settings.enableDebugMode&&console.log("SortTimestampedListCommand: Selected section:",a.heading,"at line",a.line),await this.sortTimestampsInSection(e,t,a)}catch(t){new v.Notice(`Error sorting timestamps: ${t.message}`),console.error("Sort timestamps error:",t)}}selectNote(e){return new Promise(t=>{let a=new ca(e,i=>{t(i)});a.open();let s=a.onClose;a.onClose=function(){s.call(this),t(null)}})}selectSection(e,t){return new Promise(a=>{let s=!1,i=new oa(e,t,d=>{s||(s=!0,a(d))}),n=i.onClose;i.onClose=function(){n.call(this),s||(s=!0,a(null))},i.open()})}async sortTimestampsInSection(e,t,a){let i=(await e.app.vault.read(t)).split(`
`),n=0,d=i.length-1;if(a.line!==-1){n=a.line+1;let _=this.getHeadingLevel(i[a.line]);for(let D=n;D<i.length;D++){let k=this.getHeadingLevel(i[D]);if(k>0&&k<=_){d=D-1;break}}}let l=[],h=[];for(let _=n;_<=d;_++){let D=i[_],k=Ut.parseTimestamp(D);k!==null||D.match(/^-\s+\d{8,12}\s+-\s+/)?l.push({line:D,timestamp:k,originalIndex:_}):h.push(D)}if(l.length===0){new v.Notice("No timestamped entries found in the selected section");return}let p=Ut.sortTimestampedEntries(l),x=[...i];for(let _=n;_<=d;_++)x[_]="";let A=n;for(p.forEach(_=>{A<=d&&(x[A]=_.line,A++)}),h.forEach(_=>{A<=d&&_.trim()!==""&&(x[A]=_,A++)});A<=d;)x[A]="",A++;let S=x.join(`
`);await e.app.vault.modify(t,S),new v.Notice(`Sorted ${p.length} timestamped entries in "${a.heading}"`),e.settings.enableDebugMode&&console.log(`Sorted timestamps in ${t.path}, section: ${a.heading}`)}getHeadingLevel(e){let t=e.match(/^(#{1,6})\s+/);return t?t[1].length:0}},Ht=class extends v.Plugin{constructor(){super(...arguments);this.commands=[]}initializeCommands(){this.commands.push(new na),this.commands.push(new da),this.commands.forEach(t=>{this.addCommand({id:t.id,name:t.name,callback:()=>t.execute(this)})})}async onload(){await this.loadSettings(),this.initializeCommands(),this.addSettingTab(new fa(this.app,this))}onunload(){}async loadSettings(){this.settings=Object.assign({},$o,await this.loadData())}async saveSettings(){await this.saveData(this.settings)}},fa=class extends v.PluginSettingTab{constructor(t,a){super(t,a);this.plugin=a}display(){let{containerEl:t}=this;t.empty(),t.createEl("h2",{text:"HW Command Scripts Settings"}),new v.Setting(t).setName("Debug Mode").setDesc("Enable debug logging for troubleshooting").addToggle(s=>s.setValue(this.plugin.settings.enableDebugMode).onChange(async i=>{this.plugin.settings.enableDebugMode=i,await this.plugin.saveSettings()})),t.createEl("h3",{text:"Available Commands"});let a=t.createEl("ul");this.plugin.commands.forEach(s=>{let i=a.createEl("li");i.createEl("strong",{text:s.name}),i.createEl("br"),i.createEl("span",{text:s.description})})}};
