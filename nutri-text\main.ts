import { A<PERSON>, Editor, MarkdownView, <PERSON><PERSON>, Notice, Plugin, PluginSettingTab, Setting, TFile, TFolder, SuggestModal, request } from 'obsidian';

interface NutriTextSettings {
	foodLogPath: string;
	foodArchivePath: string;
	dietGoalsPath: string;
	localDatabasePath: string;
	mealPlanPath: string;
	nutritionSchemaPath: string;
	nutritionReportPath: string;
	googleApiKey: string;
	llmModel: string;
}

const DEFAULT_SETTINGS: NutriTextSettings = {
	foodLogPath: 'food-log.md',
	foodArchivePath: 'food-archive.md',
	dietGoalsPath: 'diet-goals.md',
	localDatabasePath: 'local-database',
	mealPlanPath: 'meal-plan.md',
	nutritionSchemaPath: 'nutrition-schema.md',
	nutritionReportPath: 'nutrition-report.md',
	googleApiKey: '',
	llmModel: 'gemini-2.5-pro-preview-06-05'
}

export default class NutriText extends Plugin {
	settings: NutriTextSettings;

	async onload() {
		await this.loadSettings();

		// This creates an icon in the left ribbon.
		const ribbonIconEl = this.addRibbonIcon('dice', 'Nutri-Text', (evt: MouseEvent) => {
			// Called when the user clicks the icon.
			new Notice('This is a notice!');
		});
		// Perform additional things with the ribbon
		ribbonIconEl.addClass('my-plugin-ribbon-class');

		// This adds a status bar item to the bottom of the app. Does not work on mobile apps.
		const statusBarItemEl = this.addStatusBarItem();
		statusBarItemEl.setText('Status Bar Text');

		this.addCommand({
			id: 'calculate-todays-nutrition',
			name: 'Calculate Today\'s Nutrition',
			callback: async () => {
				if (!this.settings.googleApiKey) {
					new Notice('Please set your Google API key in the settings.');
					return;
				}

				const foodLogFile = this.app.vault.getAbstractFileByPath(this.settings.foodLogPath);
				if (!foodLogFile || !(foodLogFile instanceof TFile)) {
					new Notice(`Food log file not found at "${this.settings.foodLogPath}". Please check the path in the settings.`);
					return;
				}

				const dietGoalsFile = this.app.vault.getAbstractFileByPath(this.settings.dietGoalsPath);
				if (!dietGoalsFile || !(dietGoalsFile instanceof TFile)) {
					new Notice(`Diet goals file not found at "${this.settings.dietGoalsPath}". Please check the path in the settings.`);
					return;
				}

				const nutritionSchemaFile = this.app.vault.getAbstractFileByPath(this.settings.nutritionSchemaPath);
				if (!nutritionSchemaFile || !(nutritionSchemaFile instanceof TFile)) {
					new Notice(`Nutrition schema file not found at "${this.settings.nutritionSchemaPath}". Please check the path in the settings.`);
					return;
				}

				const foodLogContent = await this.app.vault.read(foodLogFile);
				const dietGoalsContent = await this.app.vault.read(dietGoalsFile);
				const nutritionSchemaContent = await this.app.vault.read(nutritionSchemaFile);

				const prompt = `
Given a list of foods that was eaten calculate [My Nutrition] using the following [Nutrition Schema]
${nutritionSchemaContent}

[Food Log]
${foodLogContent}

[Diet Goals]
${dietGoalsContent}

---
- For every food that you find report it's nutrition facts (including URL if possible) you see in JSON that makes up the temporary [Nutrition Fact] for each food
- Then calculate the calorie composition for each of the three macronutrients. Do not include dietary fiber as it is assumed to not be absorbed into the bodies metabolism.
	- Net Carbohydrates (% of Total Calories):
	- Protein (% of Total Calories):
	- Fat (% of Total Calories):
- Then provide two lists of the foods, ranking net carbs and sodium, respectively.
- Then propose changes to the main prompt to help decrease thinking time while maintaining accuracy.
`;
				const notice = new Notice('Calculating nutrition...', 0);
				try {
					const response = await request({
						url: `https://generativelanguage.googleapis.com/v1beta/models/${this.settings.llmModel}:generateContent?key=${this.settings.googleApiKey}`,
						method: 'POST',
						headers: {
							'Content-Type': 'application/json'
						},
						body: JSON.stringify({
							"contents": [
								{ "parts": [ { "text": prompt } ] }
							]
						})
					});
					notice.hide();
					const reportFile = this.app.vault.getAbstractFileByPath(this.settings.nutritionReportPath);
					if (reportFile && reportFile instanceof TFile) {
						await this.app.vault.modify(reportFile, response);
					} else {
						await this.app.vault.create(this.settings.nutritionReportPath, response);
					}
					new Notice('Nutrition report saved.');
				} catch (error) {
						notice.hide();
						new Notice('Error communicating with Google Gemini API. Please check your API key and network connection.');
						console.error(error);
					}
			}
		});

		// This adds a settings tab so the user can configure various aspects of the plugin
		this.addSettingTab(new NutriTextSettingTab(this.app, this));

		// If the plugin hooks up any global DOM events (on parts of the app that doesn't belong to this plugin)
		// Using this function will automatically remove the event listener when this plugin is disabled.
		this.registerDomEvent(document, 'click', (evt: MouseEvent) => {
			console.log('click', evt);
		});

		// When registering intervals, this function will automatically clear the interval when the plugin is disabled.
		this.registerInterval(window.setInterval(() => console.log('setInterval'), 5 * 60 * 1000));
	}

	onunload() {

	}

	async loadSettings() {
		this.settings = Object.assign({}, DEFAULT_SETTINGS, await this.loadData());
	}

	async saveSettings() {
		await this.saveData(this.settings);
	}
}

class NutritionModal extends Modal {
	content: string;

	constructor(app: App, content: string) {
		super(app);
		this.content = content;
	}

	onOpen() {
		const {contentEl} = this;
		contentEl.createEl('h2', {text: 'LLM Prompt'});
		contentEl.createEl('p', {text: this.content});
	}

	onClose() {
		const {contentEl} = this;
		contentEl.empty();
	}
}

class FileSuggestModal extends SuggestModal<TFile> {
	constructor(app: App, private onSelect: (path: string) => void) {
		super(app);
	}

	getSuggestions(query: string): TFile[] {
		return this.app.vault.getMarkdownFiles().filter(file =>
			file.path.toLowerCase().includes(query.toLowerCase())
		);
	}

	renderSuggestion(file: TFile, el: HTMLElement) {
		el.createEl('div', { text: file.path });
	}

	onChooseSuggestion(file: TFile, evt: MouseEvent | KeyboardEvent) {
		this.onSelect(file.path);
	}
}

class FolderSuggestModal extends SuggestModal<TFolder> {
	constructor(app: App, private onSelect: (path: string) => void) {
		super(app);
	}

	getSuggestions(query: string): TFolder[] {
		return this.app.vault.getAllLoadedFiles().filter(folder =>
			folder instanceof TFolder && folder.path.toLowerCase().includes(query.toLowerCase())
		) as TFolder[];
	}

	renderSuggestion(folder: TFolder, el: HTMLElement) {
		el.createEl('div', { text: folder.path });
	}

	onChooseSuggestion(folder: TFolder, evt: MouseEvent | KeyboardEvent) {
		this.onSelect(folder.path);
	}
}

class NutriTextSettingTab extends PluginSettingTab {
	plugin: NutriText;

	constructor(app: App, plugin: NutriText) {
		super(app, plugin);
		this.plugin = plugin;
	}

	display(): void {
		const {containerEl} = this;

		containerEl.empty();

		containerEl.createEl('h2', {text: 'Nutri-Text Settings'});

		new Setting(containerEl)
			.setName('Food Log Path')
			.setDesc('Path to the food log file.')
			.addText(text => {
				text.setPlaceholder('Example: food-log.md')
					.setValue(this.plugin.settings.foodLogPath)
					.onChange(async (value) => {
						this.plugin.settings.foodLogPath = value;
						await this.plugin.saveSettings();
					});
			})
			.addButton(button => {
				button.setButtonText('Browse').onClick(() => {
					new FileSuggestModal(this.app, (path) => {
						this.plugin.settings.foodLogPath = path;
						this.plugin.saveSettings();
						this.display();
					}).open();
				});
			});

		new Setting(containerEl)
			.setName('Food Archive Path')
			.setDesc('Path to the food archive file.')
			.addText(text => {
				text.setPlaceholder('Example: food-archive.md')
					.setValue(this.plugin.settings.foodArchivePath)
					.onChange(async (value) => {
						this.plugin.settings.foodArchivePath = value;
						await this.plugin.saveSettings();
					});
			})
			.addButton(button => {
				button.setButtonText('Browse').onClick(() => {
					new FileSuggestModal(this.app, (path) => {
						this.plugin.settings.foodArchivePath = path;
						this.plugin.saveSettings();
						this.display();
					}).open();
				});
			});

		new Setting(containerEl)
			.setName('Diet Goals Path')
			.setDesc('Path to the diet goals file.')
			.addText(text => {
				text.setPlaceholder('Example: diet-goals.md')
					.setValue(this.plugin.settings.dietGoalsPath)
					.onChange(async (value) => {
						this.plugin.settings.dietGoalsPath = value;
						await this.plugin.saveSettings();
					});
			})
			.addButton(button => {
				button.setButtonText('Browse').onClick(() => {
					new FileSuggestModal(this.app, (path) => {
						this.plugin.settings.dietGoalsPath = path;
						this.plugin.saveSettings();
						this.display();
					}).open();
				});
			});

		new Setting(containerEl)
			.setName('Local Database Path')
			.setDesc('Path to the local database folder.')
			.addText(text => {
				text.setPlaceholder('Example: local-database')
					.setValue(this.plugin.settings.localDatabasePath)
					.onChange(async (value) => {
						this.plugin.settings.localDatabasePath = value;
						await this.plugin.saveSettings();
					});
			})
			.addButton(button => {
				button.setButtonText('Browse').onClick(() => {
					new FolderSuggestModal(this.app, (path) => {
						this.plugin.settings.localDatabasePath = path;
						this.plugin.saveSettings();
						this.display();
					}).open();
				});
			});

		new Setting(containerEl)
			.setName('Meal Plan Path')
			.setDesc('Path to the meal plan file.')
			.addText(text => {
				text.setPlaceholder('Example: meal-plan.md')
					.setValue(this.plugin.settings.mealPlanPath)
					.onChange(async (value) => {
						this.plugin.settings.mealPlanPath = value;
						await this.plugin.saveSettings();
					});
			})
			.addButton(button => {
				button.setButtonText('Browse').onClick(() => {
					new FileSuggestModal(this.app, (path) => {
						this.plugin.settings.mealPlanPath = path;
						this.plugin.saveSettings();
						this.display();
					}).open();
				});
			});

		new Setting(containerEl)
			.setName('Nutrition Schema Path')
			.setDesc('Path to the nutrition schema file.')
			.addText(text => {
				text.setPlaceholder('Example: nutrition-schema.md')
					.setValue(this.plugin.settings.nutritionSchemaPath)
					.onChange(async (value) => {
						this.plugin.settings.nutritionSchemaPath = value;
						await this.plugin.saveSettings();
					});
			})
			.addButton(button => {
				button.setButtonText('Browse').onClick(() => {
					new FileSuggestModal(this.app, (path) => {
						this.plugin.settings.nutritionSchemaPath = path;
						this.plugin.saveSettings();
						this.display();
					}).open();
				});
			});

		new Setting(containerEl)
			.setName('Nutrition Report Path')
			.setDesc('Path to the nutrition report file.')
			.addText(text => {
				text.setPlaceholder('Example: nutrition-report.md')
					.setValue(this.plugin.settings.nutritionReportPath)
					.onChange(async (value) => {
						this.plugin.settings.nutritionReportPath = value;
						await this.plugin.saveSettings();
					});
			})
			.addButton(button => {
				button.setButtonText('Browse').onClick(() => {
					new FileSuggestModal(this.app, (path) => {
						this.plugin.settings.nutritionReportPath = path;
						this.plugin.saveSettings();
						this.display();
					}).open();
				});
			});
		
		containerEl.createEl('h2', {text: 'LLM Settings'});

		new Setting(containerEl)
			.setName('Google API Key')
			.setDesc('Your Google API key.')
			.addText(text => text
				.setPlaceholder('Enter your API key')
				.setValue(this.plugin.settings.googleApiKey)
				.onChange(async (value) => {
					this.plugin.settings.googleApiKey = value;
					await this.plugin.saveSettings();
				}))
			.addButton(button => button
				.setButtonText('Test Connection')
				.onClick(async () => {
					if (!this.plugin.settings.googleApiKey) {
						new Notice('Please enter your Google API key.');
						return;
					}
					try {
						const response = await request({
							url: `https://generativelanguage.googleapis.com/v1beta/models?key=${this.plugin.settings.googleApiKey}`,
							method: 'GET'
						});
						const data = JSON.parse(response);
						const models = data.models.map((model: any) => model.name.replace('models/', ''));
						if (models.includes(this.plugin.settings.llmModel)) {
							new Notice('Connection successful!');
						} else {
							new Notice(`Connection successful, but the selected model "${this.plugin.settings.llmModel}" is not available. Please select a different model.`);
						}
						this.display();
					} catch (error) {
						new Notice('Connection failed. Please check your API key and network connection.');
						console.error(error);
					}
				}));

		new Setting(containerEl)
			.setName('LLM Model')
			.setDesc('The LLM model to use.')
			.addDropdown(async dropdown => {
				if (this.plugin.settings.googleApiKey) {
					try {
						const response = await request({
							url: `https://generativelanguage.googleapis.com/v1beta/models?key=${this.plugin.settings.googleApiKey}`,
							method: 'GET'
						});
						const data = JSON.parse(response);
						const models = data.models.map((model: any) => model.name.replace('models/', ''));
						models.forEach((model: string) => {
							dropdown.addOption(model, model);
						});
						dropdown.setValue(this.plugin.settings.llmModel);
						dropdown.onChange(async (value) => {
							this.plugin.settings.llmModel = value;
							await this.plugin.saveSettings();
						});
					} catch (error) {
						// ignore
					}
				}
			});

		containerEl.createEl('h2', {text: 'Commands'});
		const commands = (this.app as any).commands.listCommands();
		for (const command of commands) {
			if (command.id.startsWith('nutri-text:')) {
				new Setting(containerEl)
					.setName(command.name)
					.setDesc(command.id)
					.addButton(button => button
						.setButtonText('Run')
						.onClick(() => {
							(this.app as any).commands.executeCommandById(command.id);
						}));
			}
		}
	}
}
