import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>ie<PERSON>, <PERSON><PERSON>, Notice, Plugin, PluginSettingTab, Setting } from 'obsidian';
import * as cheerio from 'cheerio';

interface HWCommandSettings {
	enableDebugMode: boolean;
}

const DEFAULT_SETTINGS: HWCommandSettings = {
	enableDebugMode: false
}

// Base interface for all script commands
interface ScriptCommand {
	id: string;
	name: string;
	description: string;
	execute(plugin: HWCommandPlugin): Promise<void>;
}

// Reddit Post Parser Command
class RedditPostParserCommand implements ScriptCommand {
	id = 'reddit-post-parser';
	name = 'Paste Reddit Posts from Copied Subreddit Directory';
	description = 'Parse Reddit HTML from clipboard and paste formatted posts';

	async execute(plugin: HWCommandPlugin): Promise<void> {
		try {
			// Get HTML from clipboard
			const htmlContent = await navigator.clipboard.readText();

			if (!htmlContent) {
				new Notice('Clipboard is empty');
				return;
			}

			if (plugin.settings.enableDebugMode) {
				console.log('Reddit parser: Processing clipboard content length:', htmlContent.length);
			}

			// Parse Reddit posts
			const posts = this.extractRedditPosts(htmlContent);

			if (posts.length === 0) {
				new Notice('No Reddit posts found in clipboard content. Make sure you copied HTML from a Reddit subreddit page.');
				return;
			}

			// Format output
			const output = posts.join('\n');

			if (plugin.settings.enableDebugMode) {
				console.log('Reddit parser: Generated output:', output);
			}

			// Copy to clipboard
			await navigator.clipboard.writeText(output);

			// Paste into active editor
			const activeView = plugin.app.workspace.getActiveViewOfType(MarkdownView);
			if (activeView) {
				activeView.editor.replaceSelection(output);
				new Notice(`Parsed and pasted ${posts.length} Reddit posts`);
			} else {
				new Notice(`Parsed ${posts.length} Reddit posts and copied to clipboard`);
			}

		} catch (error) {
			new Notice(`Error parsing Reddit posts: ${error.message}`);
			console.error('Reddit parser error:', error);
		}
	}

	private extractRedditPosts(htmlContent: string): string[] {
		const $ = cheerio.load(htmlContent);
		const posts = $('shreddit-post');
		const results: string[] = [];

		posts.each((_, elem) => {
			const post = $(elem);
			const title = post.attr('post-title');
			const slug = post.attr('permalink');
			const timestampStr = post.attr('created-timestamp');

			if (!title || !slug || !timestampStr) {
				return; // Skip this post if missing required data
			}

			// Extract the base URL without the slug
			const match = slug.match(/(\/r\/[^/]+\/comments\/[^/]+\/)/);
			if (!match) {
				return; // Skip if URL format doesn't match
			}

			const postUrl = `https://www.reddit.com${match[1]}`;

			// Convert timestamp to datetime object
			const utcTime = new Date(timestampStr);

			// Convert UTC to EDT (UTC-4)
			const edtOffsetMs = 4 * 60 * 60 * 1000;
			const edtTime = new Date(utcTime.getTime() - edtOffsetMs);

			// Format the time as YYYYMMDDHHmm
			const pad = (n: number) => n.toString().padStart(2, '0');
			const formattedTime =
				`${edtTime.getFullYear()}${pad(edtTime.getMonth() + 1)}${pad(edtTime.getDate())}` +
				`${pad(edtTime.getHours())}${pad(edtTime.getMinutes())}`;

			results.push(`- ${formattedTime} - [${title}](${postUrl})`);
		});

		return results;
	}
}

export default class HWCommandPlugin extends Plugin {
	settings: HWCommandSettings;
	public commands: ScriptCommand[] = [];

	private initializeCommands() {
		// Add all script commands here
		this.commands.push(new RedditPostParserCommand());

		// Register each command with Obsidian
		this.commands.forEach(command => {
			this.addCommand({
				id: command.id,
				name: command.name,
				callback: () => command.execute(this)
			});
		});
	}

	async onload() {
		await this.loadSettings();

		// Initialize commands
		this.initializeCommands();

		// Add settings tab
		this.addSettingTab(new HWCommandSettingTab(this.app, this));
	}

	onunload() {
		// Cleanup if needed
	}

	async loadSettings() {
		this.settings = Object.assign({}, DEFAULT_SETTINGS, await this.loadData());
	}

	async saveSettings() {
		await this.saveData(this.settings);
	}
}

class HWCommandSettingTab extends PluginSettingTab {
	plugin: HWCommandPlugin;

	constructor(app: App, plugin: HWCommandPlugin) {
		super(app, plugin);
		this.plugin = plugin;
	}

	display(): void {
		const {containerEl} = this;

		containerEl.empty();

		containerEl.createEl('h2', {text: 'HW Command Scripts Settings'});

		new Setting(containerEl)
			.setName('Debug Mode')
			.setDesc('Enable debug logging for troubleshooting')
			.addToggle(toggle => toggle
				.setValue(this.plugin.settings.enableDebugMode)
				.onChange(async (value) => {
					this.plugin.settings.enableDebugMode = value;
					await this.plugin.saveSettings();
				}));

		// Add information about available commands
		containerEl.createEl('h3', {text: 'Available Commands'});
		const commandList = containerEl.createEl('ul');

		this.plugin.commands.forEach(command => {
			const listItem = commandList.createEl('li');
			listItem.createEl('strong', {text: command.name});
			listItem.createEl('br');
			listItem.createEl('span', {text: command.description});
		});
	}
}
